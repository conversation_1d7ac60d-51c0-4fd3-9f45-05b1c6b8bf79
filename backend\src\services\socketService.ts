import { Server } from 'socket.io';
import { logger } from '../utils/logger';
import { verifyToken } from '../utils/jwt';
import type { User } from '@prisma/client';
import { NotificationService } from './notificationService';
import { onlineUserService } from './onlineUserService';

export interface SocketUser extends User {
  socketId: string;
}

export class SocketService {
  private io: Server;
  private connectedUsers: Map<string, SocketUser> = new Map();
  private notificationService: NotificationService;

  constructor(io: Server) {
    this.io = io;
    this.notificationService = new NotificationService();
    this.setupMiddleware();
    this.setupEventHandlers();
  }

  // 设置中间件
  private setupMiddleware() {
    // Socket.IO认证中间件
    this.io.use(async (socket, next) => {
      try {
        logger.info('Socket.IO认证开始', {
          socketId: socket.id,
          handshake: {
            auth: socket.handshake.auth,
            headers: socket.handshake.headers
          }
        });

        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

        if (!token) {
          logger.warn('Socket.IO认证失败：缺少token');
          return next(new Error('认证失败：缺少token'));
        }

        const decoded = verifyToken(token);
        if (!decoded || !decoded.userId) {
          logger.warn('Socket.IO认证失败：无效token');
          return next(new Error('认证失败：无效token'));
        }

        // 将用户信息附加到socket
        socket.data.user = decoded;
        logger.info('Socket.IO认证成功', {
          socketId: socket.id,
          userId: decoded.userId,
          username: decoded.username,
          role: decoded.role
        });
        next();
      } catch (error) {
        logger.error('Socket认证失败:', error);
        next(new Error('认证失败'));
      }
    });
  }

  // 设置事件处理器
  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const user = socket.data.user;
      logger.info(`用户连接: ${user.username} (${socket.id})`);

      // 用户加入对应的房间
      this.handleUserJoin(socket, user);

      // 处理用户离开房间
      socket.on('leave-room', (roomName: string) => {
        this.handleUserLeave(socket, roomName);
      });

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        this.handleUserDisconnect(socket, reason);
      });

      // 处理心跳
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  // 处理用户加入
  private async handleUserJoin(socket: any, user: any) {
    const socketUser: SocketUser = {
      ...user,
      socketId: socket.id
    };

    // 保存连接的用户
    this.connectedUsers.set(user.userId, socketUser);

    // 更新用户会话的Socket ID
    try {
      await onlineUserService.updateUserActivity(user.sessionToken || '', socket.id);
    } catch (error) {
      logger.error('更新用户Socket ID失败:', error);
    }

    // 根据用户角色加入不同的房间
    if (user.role === 'BOSS' || user.role === 'ADMIN') {
      socket.join('boss-room');
      socket.join(`user-${user.userId}`);
    } else if (user.role === 'EMPLOYEE') {
      socket.join('employee-room');
      socket.join(`user-${user.userId}`);
    }

    // 发送连接成功消息
    socket.emit('connected', {
      message: '连接成功',
      user: {
        id: user.userId,
        username: user.username,
        role: user.role
      }
    });

    // 发送离线期间的通知
    this.sendOfflineNotifications(user.userId);

    // 通知管理员有用户上线（仅通知BOSS和ADMIN）
    this.notifyUserOnline(user);

    logger.info(`用户 ${user.username} 加入房间成功`);
  }

  // 处理用户离开房间
  private handleUserLeave(socket: any, roomName: string) {
    socket.leave(roomName);
    logger.info(`用户 ${socket.data.user.username} 离开房间: ${roomName}`);
  }

  // 处理用户断开连接
  private async handleUserDisconnect(socket: any, reason: string) {
    const user = socket.data.user;
    if (user) {
      this.connectedUsers.delete(user.userId);

      // 更新用户活动时间（标记为离线）
      try {
        await onlineUserService.updateUserActivity(user.sessionToken || '');
      } catch (error) {
        logger.error('更新用户离线状态失败:', error);
      }

      // 通知管理员有用户下线
      this.notifyUserOffline(user, reason);

      logger.info(`用户断开连接: ${user.username} (${socket.id}), 原因: ${reason}`);
    }
  }

  // 通用通知发送方法（同时处理Socket推送和数据库存储）
  private async sendNotificationToUser(
    userId: string,
    title: string,
    content: string,
    type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' = 'INFO',
    socketEvent: string,
    data?: any
  ) {
    try {
      // 1. 保存通知到数据库
      await this.notificationService.createNotification({
        userId,
        title,
        content,
        type,
        data
      });

      // 2. 发送Socket实时通知
      const socketNotification = {
        type: socketEvent.toUpperCase(),
        message: content,
        data,
        timestamp: new Date().toISOString()
      };

      this.io.to(`user-${userId}`).emit(socketEvent, socketNotification);

      logger.info(`发送通知成功: ${title} -> 用户 ${userId}`);
    } catch (error) {
      logger.error('发送通知失败:', error);
    }
  }

  // 发送通知给多个用户
  private async sendNotificationToUsers(
    userIds: string[],
    title: string,
    content: string,
    type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' = 'INFO',
    socketEvent: string,
    data?: any
  ) {
    const promises = userIds.map(userId =>
      this.sendNotificationToUser(userId, title, content, type, socketEvent, data)
    );

    try {
      await Promise.all(promises);
      logger.info(`批量发送通知成功: ${title} -> ${userIds.length} 个用户`);
    } catch (error) {
      logger.error('批量发送通知失败:', error);
    }
  }

  // 发送通知给房间内所有用户
  private async sendNotificationToRoom(
    room: string,
    title: string,
    content: string,
    type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' = 'INFO',
    socketEvent: string,
    data?: any
  ) {
    try {
      // 获取房间内的所有用户ID
      const roomUsers: string[] = [];
      this.connectedUsers.forEach((user, userId) => {
        // 这里需要根据房间规则判断用户是否在房间内
        if (room === 'employee-room' && user.role === 'EMPLOYEE') {
          roomUsers.push(userId);
        } else if (room === 'boss-room' && (user.role === 'BOSS' || user.role === 'ADMIN')) {
          roomUsers.push(userId);
        }
      });

      // 发送Socket通知到房间
      const socketNotification = {
        type: socketEvent.toUpperCase(),
        message: content,
        data,
        timestamp: new Date().toISOString()
      };

      this.io.to(room).emit(socketEvent, socketNotification);

      // 为在线用户保存数据库通知
      if (roomUsers.length > 0) {
        await this.sendNotificationToUsers(roomUsers, title, content, type, socketEvent, data);
      }

      logger.info(`发送房间通知成功: ${title} -> 房间 ${room}`);
    } catch (error) {
      logger.error('发送房间通知失败:', error);
    }
  }

  // 发送新任务通知给员工
  public async notifyNewTask(task: any) {
    const title = '新任务通知';
    const content = `有新的任务可以接单: ${task.taskNo}`;

    await this.sendNotificationToRoom(
      'employee-room',
      title,
      content,
      'INFO',
      'new-task',
      task
    );
  }

  // 发送任务状态更新通知
  public async notifyTaskStatusUpdate(task: any, targetUserId?: string) {
    const title = '任务状态更新';
    const content = `任务 ${task.taskNo} 状态已更新为 ${this.getStatusText(task.status)}`;

    if (targetUserId) {
      // 发送给特定用户
      await this.sendNotificationToUser(
        targetUserId,
        title,
        content,
        'SUCCESS',
        'task-status-update',
        task
      );
    } else {
      // 发送给所有相关用户
      const userIds: string[] = [];
      if (task.assigneeId) userIds.push(task.assigneeId);
      if (task.createdById) userIds.push(task.createdById);

      if (userIds.length > 0) {
        await this.sendNotificationToUsers(
          userIds,
          title,
          content,
          'SUCCESS',
          'task-status-update',
          task
        );
      }
    }
  }

  // 发送任务分配通知
  public async notifyTaskAssignment(task: any, assigneeId: string) {
    const title = '任务分配通知';
    const content = `您有新的任务: ${task.taskNo}`;

    await this.sendNotificationToUser(
      assigneeId,
      title,
      content,
      'INFO',
      'task-assigned',
      task
    );
  }

  // 发送任务进度更新通知
  public notifyTaskProgressUpdate(task: any, progress: any) {
    const notification = {
      type: 'TASK_PROGRESS_UPDATE',
      message: `任务 ${task.taskNo} 进度已更新: ${progress.progress}%`,
      data: { task, progress },
      timestamp: new Date().toISOString()
    };

    // 通知老板和管理员
    this.io.to('boss-room').emit('task-progress-update', notification);
    
    // 通知任务创建者
    if (task.createdById) {
      this.io.to(`user-${task.createdById}`).emit('task-progress-update', notification);
    }

    logger.info(`发送任务进度更新通知: ${task.taskNo} -> ${progress.progress}%`);
  }

  // 发送订单状态更新通知
  public notifyOrderStatusUpdate(order: any) {
    const notification = {
      type: 'ORDER_STATUS_UPDATE',
      message: `订单 ${order.orderNo} 状态已更新为 ${this.getOrderStatusText(order.status)}`,
      data: order,
      timestamp: new Date().toISOString()
    };

    // 通知所有老板和管理员
    this.io.to('boss-room').emit('order-status-update', notification);

    logger.info(`发送订单状态更新通知: ${order.orderNo} -> ${order.status}`);
  }

  // 发送系统通知
  public sendSystemNotification(message: string, targetRoom?: string, data?: any) {
    const notification = {
      type: 'SYSTEM_NOTIFICATION',
      message,
      data,
      timestamp: new Date().toISOString()
    };

    if (targetRoom) {
      this.io.to(targetRoom).emit('system-notification', notification);
    } else {
      this.io.emit('system-notification', notification);
    }

    logger.info(`发送系统通知: ${message}`);
  }

  // 获取在线用户数量
  public getOnlineUsersCount(): number {
    return this.connectedUsers.size;
  }

  // 获取在线用户列表
  public getOnlineUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values());
  }

  // 检查用户是否在线
  public isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  // 为用户发送离线期间的通知
  public async sendOfflineNotifications(userId: string) {
    try {
      // 获取用户未读通知
      const notifications = await this.notificationService.getUserNotifications(userId, {
        isRead: false,
        limit: 50,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      if (notifications.items.length > 0) {
        // 发送给用户
        this.io.to(`user-${userId}`).emit('offline-notifications', {
          type: 'OFFLINE_NOTIFICATIONS',
          data: notifications.items,
          timestamp: new Date().toISOString()
        });

        logger.info(`发送离线通知: 用户 ${userId} 收到 ${notifications.items.length} 条未读通知`);
      }
    } catch (error) {
      logger.error('发送离线通知失败:', error);
    }
  }

  // 添加结算完成通知
  public async notifySettlementCompleted(settlement: any) {
    const title = '结算完成通知';
    const content = `您的任务 ${settlement.task?.taskNo} 结算已完成，金额: ¥${settlement.amount}`;

    await this.sendNotificationToUser(
      settlement.userId,
      title,
      content,
      'SUCCESS',
      'settlement-completed',
      settlement
    );
  }

  // 添加任务即将超时提醒
  public async notifyTaskDeadlineWarning(task: any) {
    const title = '任务超时提醒';
    const content = `任务 ${task.taskNo} 即将超时，请及时完成`;

    if (task.assigneeId) {
      await this.sendNotificationToUser(
        task.assigneeId,
        title,
        content,
        'WARNING',
        'task-deadline-warning',
        task
      );
    }
  }

  // 通知用户上线
  private notifyUserOnline(user: any) {
    const notification = {
      type: 'USER_ONLINE',
      message: `用户 ${user.username} 已上线`,
      data: {
        userId: user.userId,
        username: user.username,
        role: user.role,
        loginTime: new Date().toISOString(),
      },
      timestamp: new Date().toISOString()
    };

    // 只通知管理员和老板
    this.io.to('boss-room').emit('user-online', notification);

    logger.info(`通知用户上线: ${user.username}`);
  }

  // 通知用户下线
  private notifyUserOffline(user: any, reason: string) {
    const notification = {
      type: 'USER_OFFLINE',
      message: `用户 ${user.username} 已下线`,
      data: {
        userId: user.userId,
        username: user.username,
        role: user.role,
        reason,
        logoutTime: new Date().toISOString(),
      },
      timestamp: new Date().toISOString()
    };

    // 只通知管理员和老板
    this.io.to('boss-room').emit('user-offline', notification);

    logger.info(`通知用户下线: ${user.username}, 原因: ${reason}`);
  }

  // 强制用户下线
  public async forceUserLogout(userId: string, reason: string = '管理员操作') {
    try {
      // 查找用户的Socket连接
      const socketUser = this.connectedUsers.get(userId);

      if (socketUser) {
        // 发送强制下线通知给用户
        this.io.to(`user-${userId}`).emit('force-logout', {
          type: 'FORCE_LOGOUT',
          message: '您已被管理员强制下线',
          reason,
          timestamp: new Date().toISOString()
        });

        // 断开用户连接
        const sockets = await this.io.in(`user-${userId}`).fetchSockets();
        for (const socket of sockets) {
          socket.disconnect(true);
        }

        // 从连接列表中移除
        this.connectedUsers.delete(userId);

        logger.info(`强制用户下线成功: ${socketUser.username}, 原因: ${reason}`);
      }
    } catch (error) {
      logger.error('强制用户下线失败:', error);
      throw error;
    }
  }

  // 获取在线用户列表（Socket层面）
  public getConnectedUsers(): Map<string, SocketUser> {
    return this.connectedUsers;
  }



  // 添加系统维护通知
  public async notifySystemMaintenance(message: string, maintenanceTime: string) {
    const title = '系统维护通知';
    const content = `${message}，维护时间: ${maintenanceTime}`;

    // 发送给所有在线用户
    const allUserIds = Array.from(this.connectedUsers.keys());
    if (allUserIds.length > 0) {
      await this.sendNotificationToUsers(
        allUserIds,
        title,
        content,
        'WARNING',
        'system-maintenance',
        { maintenanceTime }
      );
    }
  }

  // 获取任务状态文本
  private getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING: '待处理',
      ACCEPTED: '已接受',
      IN_PROGRESS: '进行中',
      PAUSED: '已暂停',
      SUBMITTED: '已提交',
      APPROVED: '已审核',
      REJECTED: '已拒绝',
      COMPLETED: '已完成',
      CANCELLED: '已取消'
    };
    return statusMap[status] || status;
  }

  // 获取订单状态文本
  private getOrderStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING: '待处理',
      ASSIGNED: '已分配',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      CANCELLED: '已取消'
    };
    return statusMap[status] || status;
  }
}

// 导出单例实例
let socketService: SocketService | null = null;

export const initializeSocketService = (io: Server): SocketService => {
  if (!socketService) {
    socketService = new SocketService(io);
  }
  return socketService;
};

export const getSocketService = (): SocketService => {
  if (!socketService) {
    throw new Error('SocketService 未初始化');
  }
  return socketService;
};
