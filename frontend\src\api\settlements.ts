import { request } from './http'
import type { ApiResponse, PaginatedResponse, PaginationQuery } from '@/types'

// 结算接口
export interface Settlement {
  id: string
  taskId: string
  userId: string
  amount: number
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  settledAt?: string
  notes?: string
  createdAt: string
  updatedAt: string
  task?: {
    id: string
    taskNo: string
    order?: {
      id: string
      customerName: string
    }
  }
  user?: {
    id: string
    username: string
    nickname?: string
  }
}

export interface SettlementQuery extends PaginationQuery {
  status?: string
  userId?: string
  keyword?: string
}

export interface SettleRequest {
  notes?: string
}

// 结算API
export const settlementApi = {
  // 获取结算列表
  getSettlements(query: SettlementQuery = {}): Promise<ApiResponse<PaginatedResponse<Settlement>>> {
    // 过滤掉空字符串参数
    const filteredQuery = Object.fromEntries(
      Object.entries(query).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )
    return request.get('/settlements', { params: filteredQuery })
  },

  // 获取结算详情
  getSettlementById(id: string): Promise<ApiResponse<Settlement>> {
    return request.get(`/settlements/${id}`)
  },

  // 执行结算
  settleSettlement(id: string, data: SettleRequest): Promise<ApiResponse<Settlement>> {
    return request.post(`/settlements/${id}/settle`, data)
  },

  // 取消结算
  cancelSettlement(id: string): Promise<ApiResponse<Settlement>> {
    return request.post(`/settlements/${id}/cancel`)
  },

  // 获取结算统计
  getSettlementStats(): Promise<ApiResponse<{
    totalAmount: number
    pendingAmount: number
    completedAmount: number
    totalCount: number
    pendingCount: number
    completedCount: number
  }>> {
    return request.get('/settlements/stats')
  }
}
