import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { orderApi } from '@/api/orders'
import type { Order, OrderForm, PaginatedResponse, PaginationQuery } from '@/types'

export const useOrderStore = defineStore('orders', () => {
  // 状态
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // 计算属性
  const totalOrders = computed(() => pagination.value.total)
  const hasOrders = computed(() => orders.value.length > 0)

  // 获取订单列表
  const fetchOrders = async (query: PaginationQuery & {
    status?: string
    priority?: string
    keyword?: string
  } = {}) => {
    try {
      loading.value = true
      const response = await orderApi.getOrders(query)
      
      if (response.success && response.data) {
        orders.value = response.data.items
        pagination.value = response.data.pagination
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取订单列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取我的订单
  const fetchMyOrders = async (query: PaginationQuery & {
    status?: string
    priority?: string
    keyword?: string
  } = {}) => {
    try {
      loading.value = true
      const response = await orderApi.getMyOrders(query)
      
      if (response.success && response.data) {
        orders.value = response.data.items
        pagination.value = response.data.pagination
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取我的订单失败')
    } finally {
      loading.value = false
    }
  }

  // 获取订单详情
  const fetchOrderById = async (id: string) => {
    try {
      loading.value = true
      const response = await orderApi.getOrderById(id)
      
      if (response.success && response.data) {
        currentOrder.value = response.data
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取订单详情失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建订单
  const createOrder = async (orderForm: OrderForm) => {
    try {
      loading.value = true
      const response = await orderApi.createOrder(orderForm)
      
      if (response.success && response.data) {
        ElMessage.success('订单创建成功')
        // 将新订单添加到列表开头
        orders.value.unshift(response.data)
        pagination.value.total += 1
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '创建订单失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新订单
  const updateOrder = async (id: string, updateData: Partial<OrderForm>) => {
    try {
      loading.value = true
      const response = await orderApi.updateOrder(id, updateData)
      
      if (response.success && response.data) {
        ElMessage.success('订单更新成功')
        
        // 更新列表中的订单
        const index = orders.value.findIndex(order => order.id === id)
        if (index !== -1) {
          orders.value[index] = response.data
        }
        
        // 更新当前订单
        if (currentOrder.value?.id === id) {
          currentOrder.value = response.data
        }
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '更新订单失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 删除订单
  const deleteOrder = async (id: string) => {
    try {
      loading.value = true
      const response = await orderApi.deleteOrder(id)

      if (response.success) {
        ElMessage.success('订单删除成功')

        // 从列表中移除订单
        const index = orders.value.findIndex(order => order.id === id)
        if (index !== -1) {
          orders.value.splice(index, 1)
          pagination.value.total -= 1
        }

        // 清除当前订单
        if (currentOrder.value?.id === id) {
          currentOrder.value = null
        }

        return true
      }
      return false
    } catch (error: any) {
      // 不在这里显示错误消息，让调用方处理
      // 因为http拦截器已经显示了错误消息
      console.error('删除订单失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取订单统计
  const fetchOrderStats = async () => {
    try {
      const response = await orderApi.getOrderStats()
      if (response.success && response.data) {
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取订单统计失败')
      return null
    }
  }

  // 清空订单列表
  const clearOrders = () => {
    orders.value = []
    currentOrder.value = null
    pagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    }
  }

  // 设置当前页
  const setCurrentPage = (page: number) => {
    pagination.value.page = page
  }

  // 设置每页大小
  const setPageSize = (size: number) => {
    pagination.value.limit = size
    pagination.value.page = 1 // 重置到第一页
  }

  return {
    // 状态
    orders,
    currentOrder,
    loading,
    pagination,
    
    // 计算属性
    totalOrders,
    hasOrders,
    
    // 方法
    fetchOrders,
    fetchMyOrders,
    fetchOrderById,
    createOrder,
    updateOrder,
    deleteOrder,
    fetchOrderStats,
    clearOrders,
    setCurrentPage,
    setPageSize
  }
})
