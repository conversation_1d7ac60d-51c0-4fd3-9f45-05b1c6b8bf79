<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑游戏' : '添加游戏'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="游戏名称" prop="name">
        <el-input 
          v-model="form.name" 
          placeholder="请输入游戏英文名称（如：wzry）"
          :disabled="isEdit"
        />
        <div class="form-tip">游戏的唯一标识符，创建后不可修改</div>
      </el-form-item>

      <el-form-item label="显示名称" prop="displayName">
        <el-input
          v-model="form.displayName"
          placeholder="请输入游戏显示名称（如：王者荣耀）"
          :disabled="isEdit"
        />
        <div v-if="isEdit" class="form-tip">编辑模式下不可修改</div>
      </el-form-item>

      <el-form-item label="游戏描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea"
          :rows="3"
          placeholder="请输入游戏描述"
        />
      </el-form-item>

      <el-form-item label="游戏图标" prop="icon">
        <el-input
          v-model="form.icon"
          placeholder="请输入游戏图标URL（可选）"
          @input="handleIconUrlChange"
        />
        <div v-if="form.icon" class="icon-preview">
          <div class="preview-container">
            <img
              :src="form.icon"
              alt="游戏图标"
              @load="handleImageLoad"
              @error="handleImageError"
              v-show="!imageError"
            />
            <div v-if="imageError" class="error-placeholder">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
              <div class="error-tip">请检查URL是否正确或图片是否可访问</div>
            </div>
            <div v-if="imageLoading" class="loading-placeholder">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="排序权重" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="0"
          :max="999"
          placeholder="数字越小排序越靠前"
          style="width: 100%"
          :disabled="isEdit"
        />
        <div v-if="isEdit" class="form-tip">编辑模式下不可修改</div>
      </el-form-item>

      <el-form-item label="是否启用" prop="isActive">
        <el-switch v-model="form.isActive" :disabled="isEdit" />
        <div v-if="isEdit" class="form-tip">编辑模式下不可修改</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { Picture, Loading } from '@element-plus/icons-vue';
import { useGameStore } from '@/stores/game';
import type { CreateGameRequest, UpdateGameRequest } from '@/types/game';

interface Props {
  modelValue: boolean;
  gameId?: string;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const gameStore = useGameStore();
const formRef = ref<FormInstance>();
const loading = ref(false);

// 图片加载状态
const imageLoading = ref(false);
const imageError = ref(false);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEdit = computed(() => !!props.gameId);

// 默认图标选项
const defaultIcons = [
  { name: '默认游戏图标', url: 'https://via.placeholder.com/64x64/2196F3/FFFFFF?text=Game' },
  { name: '王者荣耀', url: 'https://game.gtimg.cn/images/yxzj/img201606/heroimg/169/169.jpg' },
  { name: '和平精英', url: 'https://ossweb-img.qq.com/images/game/pubg/web201706/logo.png' },
  { name: '英雄联盟', url: 'https://game.gtimg.cn/images/lol/act/img/champion/Jinx_0.jpg' }
];

// 图片处理函数
const handleIconUrlChange = () => {
  if (form.icon) {
    imageLoading.value = true;
    imageError.value = false;
  } else {
    imageLoading.value = false;
    imageError.value = false;
  }
};

const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
  console.warn('图片加载失败:', form.icon);
};

// 表单数据
const form = reactive<CreateGameRequest>({
  name: '',
  displayName: '',
  description: '',
  icon: '', // 先设为空，让用户选择
  sortOrder: 0,
  isActive: true
});

// 表单验证规则
const rules = computed<FormRules>(() => {
  const baseRules: FormRules = {
    description: [
      { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
    ],
    icon: [
      { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
    ]
  };

  // 如果不是编辑模式，添加其他字段的验证规则
  if (!isEdit.value) {
    baseRules.name = [
      { required: true, message: '请输入游戏名称', trigger: 'blur' },
      { min: 2, max: 20, message: '游戏名称长度在 2 到 20 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_-]+$/, message: '游戏名称只能包含字母、数字、下划线和连字符', trigger: 'blur' }
    ];
    baseRules.displayName = [
      { required: true, message: '请输入显示名称', trigger: 'blur' },
      { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
    ];
    baseRules.sortOrder = [
      { type: 'number', min: 0, max: 999, message: '排序权重必须在 0 到 999 之间', trigger: 'blur' }
    ];
  }

  return baseRules;
});

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    displayName: '',
    description: '',
    icon: '',
    sortOrder: 0,
    isActive: true
  });

  formRef.value?.clearValidate();
};

const loadGameData = async () => {
  if (!props.gameId) return;
  
  try {
    loading.value = true;
    const game = await gameStore.fetchGameById(props.gameId);
    
    Object.assign(form, {
      name: game.name,
      displayName: game.displayName,
      description: game.description || '',
      icon: game.icon || '',
      sortOrder: game.sortOrder,
      isActive: game.isActive
    });
  } catch (error) {
    console.error('加载游戏数据失败:', error);
    ElMessage.error('加载游戏数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // 在编辑模式下，只验证允许编辑的字段
    let valid: boolean = true;
    if (isEdit.value) {
      try {
        await formRef.value.validateField(['description', 'icon']);
      } catch (error) {
        valid = false;
      }
    } else {
      valid = await formRef.value.validate();
    }
    if (!valid) return;
    
    loading.value = true;
    
    let submitData: any;

    if (isEdit.value) {
      // 编辑模式下只提交描述和图标URL
      submitData = {
        description: form.description,
        icon: form.icon
      };
    } else {
      // 创建模式下提交所有字段
      submitData = {
        ...form
      };
    }

    console.log('🎮 准备发送的游戏数据:', submitData);

    if (isEdit.value) {
      await gameStore.updateGame(props.gameId!, submitData as UpdateGameRequest);
      ElMessage.success('游戏更新成功');
    } else {
      console.log('🎮 调用创建游戏API...');
      await gameStore.createGame(submitData);
      ElMessage.success('游戏创建成功');
    }
    
    emit('success');
  } catch (error) {
    console.error('保存游戏失败:', error);
    ElMessage.error('保存游戏失败');
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    if (isEdit.value) {
      loadGameData();
    } else {
      resetForm();
    }
  }
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.icon-preview {
  margin-top: 8px;

  .preview-container {
    position: relative;
    display: inline-block;
  }

  img {
    width: 48px;
    height: 48px;
    object-fit: contain;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 4px;
  }

  .error-placeholder,
  .loading-placeholder {
    width: 48px;
    height: 48px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    font-size: 12px;
    text-align: center;

    .el-icon {
      font-size: 16px;
      margin-bottom: 2px;
    }

    span {
      font-size: 10px;
      line-height: 1;
    }
  }

  .error-placeholder {
    color: var(--el-color-danger);

    .error-tip {
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: 4px;
      font-size: 10px;
      color: var(--el-color-danger);
      white-space: nowrap;
      z-index: 10;
    }
  }
}

.config-card {
  width: 100%;
  
  :deep(.el-card__body) {
    padding: 16px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
