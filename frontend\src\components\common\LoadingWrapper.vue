<template>
  <div class="loading-wrapper" :class="{ 'is-loading': loading }">
    <el-loading
      v-if="loading"
      :text="loadingText"
      :spinner="spinner"
      :background="background"
      element-loading-svg-view-box="-10, -10, 50, 50"
    />
    <div v-show="!loading" class="content">
      <slot />
    </div>
    
    <!-- 空状态 -->
    <div v-if="showEmpty && !loading" class="empty-state">
      <el-empty :description="emptyText">
        <template v-if="$slots.empty" #default>
          <slot name="empty" />
        </template>
      </el-empty>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="showError && !loading" class="error-state">
      <el-result
        icon="error"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <el-button type="primary" @click="$emit('retry')">
            重试
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean
  loadingText?: string
  spinner?: string
  background?: string
  showEmpty?: boolean
  emptyText?: string
  showError?: boolean
  errorTitle?: string
  errorMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '加载中...',
  spinner: '',
  background: 'rgba(0, 0, 0, 0.8)',
  showEmpty: false,
  emptyText: '暂无数据',
  showError: false,
  errorTitle: '加载失败',
  errorMessage: '请稍后重试'
})

defineEmits<{
  retry: []
}>()
</script>

<style lang="scss" scoped>
.loading-wrapper {
  position: relative;
  min-height: 200px;
  
  &.is-loading {
    .content {
      opacity: 0.5;
      pointer-events: none;
    }
  }
  
  .content {
    transition: opacity 0.3s ease;
  }
  
  .empty-state,
  .error-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
  }
}
</style>
