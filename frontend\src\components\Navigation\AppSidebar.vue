<template>
  <div 
    class="nav-sidebar"
    :class="[
      `theme-${theme}`,
      { collapsed: isCollapsed }
    ]"
  >
    <!-- Logo区域 -->
    <div class="nav-logo">
      <div class="logo-content">
        <div v-if="!isCollapsed" class="logo-text">
          {{ logoText }}
        </div>
        <div v-else class="logo-text collapsed">
          {{ logoText.charAt(0) }}
        </div>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="nav-menu">
      <template v-for="item in menuItems" :key="item.index">
        <!-- 普通菜单项 -->
        <div
          v-if="!item.children"
          class="nav-menu-item"
          :class="{
            'is-active': isActive(item.index),
            collapsed: isCollapsed
          }"
          :title="isCollapsed ? item.title : ''"
          @click="handleMenuClick(item)"
        >
          <div class="nav-icon">
            <component :is="item.icon" />
          </div>
          <div v-if="!isCollapsed" class="nav-text">{{ item.title }}</div>
        </div>

        <!-- 子菜单 -->
        <div
          v-else
          class="nav-submenu"
          :class="{ 
            'is-opened': openedSubmenus.includes(item.index),
            collapsed: isCollapsed 
          }"
        >
          <div
            class="nav-submenu-title"
            :class="{ collapsed: isCollapsed }"
            @click="toggleSubmenu(item.index)"
            :title="isCollapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <component :is="item.icon" />
            </div>
            <div v-if="!isCollapsed" class="nav-text">{{ item.title }}</div>
            <div
              v-if="!isCollapsed"
              class="nav-arrow"
              :class="{ expanded: openedSubmenus.includes(item.index) }"
            >
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          
          <div 
            v-if="!isCollapsed"
            class="nav-submenu-items"
            :style="{ 
              maxHeight: openedSubmenus.includes(item.index) 
                ? `${item.children.length * 48}px` 
                : '0px' 
            }"
          >
            <div
              v-for="child in item.children"
              :key="child.index"
              class="nav-menu-item"
              :class="{ 'is-active': isActive(child.index) }"
              @click="handleMenuClick(child)"
            >
              <div class="nav-icon">
                <component :is="child.icon" />
              </div>
              <div class="nav-text">{{ child.title }}</div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'

interface MenuItem {
  index: string
  title: string
  icon: any
  children?: MenuItem[]
  permission?: string[]
}

interface Props {
  theme: 'boss' | 'employee' | 'admin'
  logoText: string
  menuItems: MenuItem[]
  collapsed: boolean
}

interface Emits {
  (e: 'menu-click', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const route = useRoute()

const isCollapsed = computed(() => props.collapsed)
const openedSubmenus = ref<string[]>([])

// 检查菜单项是否激活
const isActive = (index: string) => {
  return route.path === index || route.path.startsWith(index + '/')
}

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  if (item.index) {
    router.push(item.index)
  }
  emit('menu-click', item)
}

// 切换子菜单
const toggleSubmenu = (index: string) => {
  if (isCollapsed.value) return
  
  const idx = openedSubmenus.value.indexOf(index)
  if (idx > -1) {
    openedSubmenus.value.splice(idx, 1)
  } else {
    openedSubmenus.value.push(index)
  }
}

// 初始化展开当前激活的子菜单
const initActiveSubmenu = () => {
  props.menuItems.forEach(item => {
    if (item.children) {
      const hasActiveChild = item.children.some(child => isActive(child.index))
      if (hasActiveChild && !openedSubmenus.value.includes(item.index)) {
        openedSubmenus.value.push(item.index)
      }
    }
  })
}

// 监听路由变化，自动展开相关子菜单
watch(() => route.path, () => {
  initActiveSubmenu()
}, { immediate: true })
</script>

<style lang="scss" scoped>
@use '@/styles/navigation.scss';

// 组件特定样式
.nav-sidebar {
  // 继承基础样式
}

// Tooltip样式（收缩状态下的提示）
.nav-menu-item.collapsed,
.nav-submenu.collapsed .nav-submenu-title {
  position: relative;

  &:hover::after {
    content: attr(data-title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 8px;
    pointer-events: none;
  }

  &:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 4px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.8);
    margin-left: 4px;
    z-index: 1000;
    pointer-events: none;
  }
}
</style>
