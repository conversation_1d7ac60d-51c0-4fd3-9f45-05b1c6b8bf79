import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { notificationApi, type Notification, type NotificationQuery } from '@/api/notifications'
import type { PaginatedResponse } from '@/types'

export const useNotificationStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // 计算属性
  const hasNotifications = computed(() => notifications.value.length > 0)
  const hasUnreadNotifications = computed(() => unreadCount.value > 0)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead)
  )

  // 获取通知列表
  const fetchNotifications = async (query: NotificationQuery = {}) => {
    try {
      loading.value = true
      const response = await notificationApi.getNotifications({
        page: pagination.value.page,
        limit: pagination.value.limit,
        ...query
      })

      if (response.success && response.data) {
        notifications.value = response.data.items
        pagination.value = response.data.pagination
      }
    } catch (error) {
      console.error('获取通知列表失败:', error)
      ElMessage.error('获取通知列表失败')
    } finally {
      loading.value = false
    }
  }

  // 加载更多通知（分页加载）
  const loadMoreNotifications = async (query: NotificationQuery = {}) => {
    if (!pagination.value.hasNext || loading.value) return

    try {
      loading.value = true
      const response = await notificationApi.getNotifications({
        page: pagination.value.page + 1,
        limit: pagination.value.limit,
        ...query
      })

      if (response.success && response.data) {
        notifications.value.push(...response.data.items)
        pagination.value = response.data.pagination
      }
    } catch (error) {
      console.error('加载更多通知失败:', error)
      ElMessage.error('加载更多通知失败')
    } finally {
      loading.value = false
    }
  }

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    try {
      const response = await notificationApi.getUnreadCount()
      if (response.success && response.data) {
        unreadCount.value = response.data.count
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error)
    }
  }

  // 标记通知为已读
  const markAsRead = async (id: string) => {
    try {
      const response = await notificationApi.markAsRead(id)
      if (response.success) {
        // 更新本地状态
        const notification = notifications.value.find(n => n.id === id)
        if (notification && !notification.isRead) {
          notification.isRead = true
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
      }
    } catch (error) {
      console.error('标记通知已读失败:', error)
      ElMessage.error('标记通知已读失败')
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      const response = await notificationApi.markAllAsRead()
      if (response.success) {
        // 更新本地状态
        notifications.value.forEach(n => n.isRead = true)
        unreadCount.value = 0
        ElMessage.success(`成功标记 ${response.data?.count || 0} 条通知为已读`)
      }
    } catch (error) {
      console.error('标记所有通知已读失败:', error)
      ElMessage.error('标记所有通知已读失败')
    }
  }

  // 删除通知
  const deleteNotification = async (id: string) => {
    try {
      const response = await notificationApi.deleteNotification(id)
      if (response.success) {
        // 从本地状态中移除
        const index = notifications.value.findIndex(n => n.id === id)
        if (index > -1) {
          const notification = notifications.value[index]
          if (!notification.isRead) {
            unreadCount.value = Math.max(0, unreadCount.value - 1)
          }
          notifications.value.splice(index, 1)
          pagination.value.total = Math.max(0, pagination.value.total - 1)
        }
        ElMessage.success('删除通知成功')
      }
    } catch (error) {
      console.error('删除通知失败:', error)
      ElMessage.error('删除通知失败')
    }
  }

  // 添加新通知（用于Socket推送）
  const addNotification = (notification: Notification) => {
    // 检查是否已存在
    const exists = notifications.value.find(n => n.id === notification.id)
    if (!exists) {
      notifications.value.unshift(notification)
      if (!notification.isRead) {
        unreadCount.value += 1
      }
      pagination.value.total += 1
    }
  }

  // 批量添加通知（用于离线通知）
  const addNotifications = (newNotifications: Notification[]) => {
    newNotifications.forEach(notification => {
      const exists = notifications.value.find(n => n.id === notification.id)
      if (!exists) {
        notifications.value.unshift(notification)
        if (!notification.isRead) {
          unreadCount.value += 1
        }
      }
    })
    pagination.value.total += newNotifications.length
  }

  // 清空所有通知
  const clearAllNotifications = () => {
    notifications.value = []
    unreadCount.value = 0
    pagination.value = {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    }
  }

  // 重置分页
  const resetPagination = () => {
    pagination.value.page = 1
  }

  // 设置分页参数
  const setPagination = (page: number, limit: number = 20) => {
    pagination.value.page = page
    pagination.value.limit = limit
  }

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    pagination,
    
    // 计算属性
    hasNotifications,
    hasUnreadNotifications,
    unreadNotifications,
    
    // 方法
    fetchNotifications,
    loadMoreNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    addNotifications,
    clearAllNotifications,
    resetPagination,
    setPagination
  }
})
