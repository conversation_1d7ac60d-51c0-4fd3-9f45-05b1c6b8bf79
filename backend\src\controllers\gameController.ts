import { Request, Response } from 'express';
import { GameService } from '../services/gameService';
import { GameRankService } from '../services/gameRankService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import {
  CreateGameRequest,
  UpdateGameRequest,
  CreateGameRankRequest,
  UpdateGameRankRequest
} from '../types/game';

const gameService = new GameService();
const gameRankService = new GameRankService();

// 游戏管理控制器

// 创建游戏
export const createGame = asyncHandler(async (req: Request, res: Response) => {
  const gameData: CreateGameRequest = req.body;
  
  const game = await gameService.createGame(gameData);
  
  const response: ApiResponse = {
    success: true,
    data: game,
    message: '游戏创建成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// 获取游戏列表
export const getGames = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  const result = await gameService.getGames(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取游戏列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取活跃游戏简单列表
export const getActiveGames = asyncHandler(async (req: Request, res: Response) => {
  const games = await gameService.getActiveGamesSimple();
  
  const response: ApiResponse = {
    success: true,
    data: games,
    message: '获取活跃游戏列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取游戏详情
export const getGameById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const game = await gameService.getGameById(id);
  
  const response: ApiResponse = {
    success: true,
    data: game,
    message: '获取游戏详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取游戏及其表单字段
export const getGameWithFormFields = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const game = await gameService.getGameWithFormFields(id);

  const response: ApiResponse = {
    success: true,
    data: game,
    message: '获取游戏及表单字段成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 更新游戏
export const updateGame = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateGameRequest = req.body;
  
  const game = await gameService.updateGame(id, updateData);
  
  const response: ApiResponse = {
    success: true,
    data: game,
    message: '游戏更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 删除游戏
export const deleteGame = asyncHandler(async (req: Request, res: Response) => {
  const { id, name } = req.params;

  let result;
  if (name) {
    // 通过name删除
    result = await gameService.deleteGameByName(name);
  } else {
    // 通过id删除
    result = await gameService.deleteGame(id);
  }

  const response: ApiResponse = {
    success: true,
    data: result,
    message: '游戏删除成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 获取游戏统计信息
export const getGameStatistics = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const statistics = await gameService.getGameStatistics(id);
  
  const response: ApiResponse = {
    success: true,
    data: statistics,
    message: '获取游戏统计信息成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取游戏热度分析
export const getGameHotness = asyncHandler(async (req: Request, res: Response) => {
  const hotness = await gameService.getGameHotness();
  
  const response: ApiResponse = {
    success: true,
    data: hotness,
    message: '获取游戏热度分析成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 计算订单价格
export const calculateOrderPrice = asyncHandler(async (req: Request, res: Response) => {
  const { gameId, currentRankId, targetRankId, priority = 'NORMAL' } = req.body;
  
  const priceResult = await gameService.calculateOrderPrice(
    gameId,
    currentRankId,
    targetRankId,
    priority
  );
  
  const response: ApiResponse = {
    success: true,
    data: priceResult,
    message: '价格计算成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 游戏段位管理控制器

// 创建游戏段位
export const createGameRank = asyncHandler(async (req: Request, res: Response) => {
  const rankData: CreateGameRankRequest = req.body;
  
  const rank = await gameRankService.createGameRank(rankData);
  
  const response: ApiResponse = {
    success: true,
    data: rank,
    message: '游戏段位创建成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// 获取游戏段位列表
export const getGameRanks = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  const result = await gameRankService.getGameRanks(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取游戏段位列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取指定游戏的段位列表
export const getGameRanksByGameId = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.params;
  
  const ranks = await gameRankService.getGameRanksByGameId(gameId);
  
  const response: ApiResponse = {
    success: true,
    data: ranks,
    message: '获取游戏段位列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取段位详情
export const getGameRankById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const rank = await gameRankService.getGameRankById(id);
  
  const response: ApiResponse = {
    success: true,
    data: rank,
    message: '获取段位详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新段位
export const updateGameRank = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateGameRankRequest = req.body;
  
  const rank = await gameRankService.updateGameRank(id, updateData);
  
  const response: ApiResponse = {
    success: true,
    data: rank,
    message: '段位更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 删除段位
export const deleteGameRank = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const result = await gameRankService.deleteGameRank(id);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '段位删除成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 批量创建段位
export const batchCreateGameRanks = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.params;
  const { ranks } = req.body;
  
  const result = await gameRankService.batchCreateGameRanks(gameId, ranks);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '批量创建段位成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});
