<template>
  <div class="earnings-container" v-loading="loading" element-loading-text="加载收益数据中...">
    <div class="page-header">
      <h1>收益统计</h1>
      <div class="header-actions">
        <!-- Excel导出按钮 -->
        <QuickExportButtons />
        <el-button @click="fetchEarningsData" :loading="loading" :disabled="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalTasks }}</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-icon">
          <el-icon><Document /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ stats.completedTasks }}</div>
          <div class="stat-label">已完成任务</div>
        </div>
        <div class="stat-icon">
          <el-icon><Check /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{ stats.completionRate }}%</div>
          <div class="stat-label">完成率</div>
        </div>
        <div class="stat-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">¥{{ stats.totalEarnings }}</div>
          <div class="stat-label">总收益</div>
        </div>
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
      </el-card>
    </div>

    <!-- 详细统计 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>工时统计</span></template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="总工时">{{ stats.totalHours }}小时</el-descriptions-item>
            <el-descriptions-item label="平均每任务工时">{{ stats.avgHoursPerTask }}小时</el-descriptions-item>
            <el-descriptions-item label="本月工时">{{ monthlyStats.currentMonthHours }}小时</el-descriptions-item>
            <el-descriptions-item label="上月工时">{{ monthlyStats.lastMonthHours }}小时</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>收益统计</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="平均每任务收益">¥{{ stats.avgEarningsPerTask }}</el-descriptions-item>
            <el-descriptions-item label="本月收益">¥{{ monthlyStats.currentMonthEarnings }}</el-descriptions-item>
            <el-descriptions-item label="上月收益">¥{{ monthlyStats.lastMonthEarnings }}</el-descriptions-item>
            <el-descriptions-item label="收益增长率">
              <span :class="monthlyStats.growthRate >= 0 ? 'growth-positive' : 'growth-negative'">
                {{ monthlyStats.growthRate >= 0 ? '+' : '' }}{{ monthlyStats.growthRate }}%
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务状态分布 -->
    <el-card class="chart-card">
      <template #header>
        <span>任务状态分布</span>
      </template>
      <div class="status-distribution">
        <div
          v-for="(count, status) in stats.statusBreakdown"
          :key="status"
          class="status-item"
        >
          <div class="status-bar">
            <div
              class="status-fill"
              :style="{
                width: `${(count / stats.totalTasks) * 100}%`,
                backgroundColor: getStatusColor(status)
              }"
            ></div>
          </div>
          <div class="status-info">
            <span class="status-name">{{ getStatusText(status) }}</span>
            <span class="status-count">{{ count }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 最近任务 -->
    <el-card class="recent-tasks-card">
      <template #header>
        <span>最近完成的任务</span>
      </template>
      <el-table :data="recentTasks" stripe>
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column label="客户" width="100">
          <template #default="{ row }">
            {{ row.order?.customerName }}
          </template>
        </el-table-column>
        <el-table-column label="段位" width="150">
          <template #default="{ row }">
            {{ row.order?.currentRank }} → {{ row.order?.targetRank }}
          </template>
        </el-table-column>
        <el-table-column label="佣金" width="100">
          <template #default="{ row }">
            ¥{{ row.commission || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="完成时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Document, Check, TrendCharts, Money } from '@element-plus/icons-vue'
import { userApi } from '@/api/users'
import { taskApi } from '@/api/tasks'
import QuickExportButtons from '@/components/ExcelExport/QuickExportButtons.vue'
import type { Task, TaskStatus } from '@/types'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const recentTasks = ref<Task[]>([])

const stats = reactive({
  totalTasks: 0,
  completedTasks: 0,
  completionRate: 0,
  totalEarnings: 0,
  totalHours: 0,
  avgHoursPerTask: 0,
  avgEarningsPerTask: 0,
  statusBreakdown: {} as Record<string, number>
})

const monthlyStats = reactive({
  currentMonthTasks: 0,
  lastMonthTasks: 0,
  currentMonthHours: 0,
  lastMonthHours: 0,
  currentMonthEarnings: 0,
  lastMonthEarnings: 0,
  growthRate: 0
})

// 获取收益数据
const fetchEarningsData = async () => {
  try {
    loading.value = true

    // 获取用户统计信息
    const userStatsResponse = await userApi.getMyStats()
    if (userStatsResponse.success && userStatsResponse.data) {
      const userStats = userStatsResponse.data

      // 安全地访问数据，添加空值检查
      stats.totalTasks = userStats.totalTasks || 0
      stats.completedTasks = userStats.completedTasks || 0
      stats.completionRate = Math.round(userStats.completionRate || 0)
      stats.totalEarnings = userStats.totalEarnings || 0
      stats.totalHours = userStats.totalHours || 0

      // 计算平均工时
      stats.avgHoursPerTask = stats.completedTasks > 0
        ? Math.round((stats.totalHours / stats.completedTasks) * 100) / 100
        : 0

      // 计算平均每任务收益
      stats.avgEarningsPerTask = stats.totalTasks > 0
        ? Math.round((stats.totalEarnings / stats.totalTasks) * 100) / 100
        : 0

      // 更新月度统计
      monthlyStats.currentMonthTasks = userStats.currentMonthTasks || 0
      monthlyStats.currentMonthEarnings = userStats.currentMonthEarnings || 0
    }

    // 获取任务统计信息
    try {
      const taskStatsResponse = await taskApi.getTaskStats()
      if (taskStatsResponse.success && taskStatsResponse.data) {
        stats.statusBreakdown = taskStatsResponse.data.statusBreakdown || {}
      }
    } catch (error) {
      console.warn('获取任务统计失败:', error)
      stats.statusBreakdown = {}
    }

    // 获取最近完成的任务
    try {
      const recentTasksResponse = await taskApi.getMyTasks({
        status: 'APPROVED' as any, // 使用APPROVED状态获取已完成的任务
        limit: 10,
        sortBy: 'endTime',
        sortOrder: 'desc'
      })
      if (recentTasksResponse.success && recentTasksResponse.data) {
        recentTasks.value = recentTasksResponse.data.items || []
      }
    } catch (error) {
      console.warn('获取最近任务失败:', error)
      recentTasks.value = []
    }

    // 计算月度统计（使用实际数据）
    calculateMonthlyStats()

  } catch (error: any) {
    console.error('获取收益数据失败:', error)
    ElMessage.error(error.message || '获取收益数据失败，请稍后重试')

    // 设置默认值，避免undefined错误
    resetStatsToDefault()
  } finally {
    loading.value = false
  }
}

// 重置统计数据为默认值
const resetStatsToDefault = () => {
  stats.totalTasks = 0
  stats.completedTasks = 0
  stats.completionRate = 0
  stats.totalEarnings = 0
  stats.totalHours = 0
  stats.avgHoursPerTask = 0
  stats.avgEarningsPerTask = 0
  stats.statusBreakdown = {}

  monthlyStats.currentMonthTasks = 0
  monthlyStats.currentMonthEarnings = 0
  monthlyStats.lastMonthTasks = 0
  monthlyStats.lastMonthEarnings = 0
  monthlyStats.currentMonthHours = 0
  monthlyStats.lastMonthHours = 0
  monthlyStats.growthRate = 0

  recentTasks.value = []
}

// 计算月度统计
const calculateMonthlyStats = () => {
  // 使用从API获取的实际月度数据
  // monthlyStats.currentMonthTasks 和 monthlyStats.currentMonthEarnings 已在fetchEarningsData中设置

  // 计算上月数据（模拟，实际应该从后端获取历史数据）
  monthlyStats.lastMonthTasks = Math.round(monthlyStats.currentMonthTasks * 0.8)
  monthlyStats.lastMonthEarnings = Math.round(monthlyStats.currentMonthEarnings * 0.8)

  // 计算工时（基于任务数估算）
  monthlyStats.currentMonthHours = monthlyStats.currentMonthTasks * 8 // 假设每任务8小时
  monthlyStats.lastMonthHours = monthlyStats.lastMonthTasks * 8

  // 计算增长率
  if (monthlyStats.lastMonthEarnings > 0) {
    monthlyStats.growthRate = Math.round(
      ((monthlyStats.currentMonthEarnings - monthlyStats.lastMonthEarnings) / monthlyStats.lastMonthEarnings) * 100
    )
  } else {
    monthlyStats.growthRate = 0
  }
}

// 状态相关方法
const getStatusType = (status: TaskStatus) => {
  const statusMap = {
    PENDING: 'warning',
    ACCEPTED: 'info',
    IN_PROGRESS: 'primary',
    SUBMITTED: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    COMPLETED: 'success',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    PENDING: '待处理',
    ACCEPTED: '已接受',
    IN_PROGRESS: '进行中',
    SUBMITTED: '已提交',
    APPROVED: '已审核',
    REJECTED: '已拒绝',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    PENDING: '#e6a23c',
    ACCEPTED: '#909399',
    IN_PROGRESS: '#409eff',
    SUBMITTED: '#67c23a',
    APPROVED: '#67c23a',
    REJECTED: '#f56c6c',
    COMPLETED: '#67c23a',
    CANCELLED: '#909399'
  }
  return colorMap[status] || '#909399'
}

// 初始化
onMounted(() => {
  fetchEarningsData()
})
</script>

<style lang="scss" scoped>
.earnings-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  .el-card__body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
  }

  .stat-content {
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #909399;
    }
  }

  .stat-icon {
    font-size: 32px;
    color: #409eff;
    opacity: 0.8;
  }
}

.chart-card {
  margin: 20px 0;
}

.status-distribution {
  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .status-bar {
      flex: 1;
      height: 20px;
      background-color: #f5f7fa;
      border-radius: 10px;
      overflow: hidden;
      margin-right: 12px;

      .status-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
      }
    }

    .status-info {
      display: flex;
      justify-content: space-between;
      min-width: 120px;

      .status-name {
        color: var(--text-primary);
        font-size: 14px;
      }

      .status-count {
        color: #909399;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.recent-tasks-card {
  margin-top: 20px;
}

.growth-positive {
  color: #67c23a;
  font-weight: 500;
}

.growth-negative {
  color: #f56c6c;
  font-weight: 500;
}
</style>
