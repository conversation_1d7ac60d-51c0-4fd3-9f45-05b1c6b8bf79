<template>
  <div class="dynamic-form-field">
    <!-- 文本输入 -->
    <el-input
      v-if="field.type === 'text'"
      v-model="modelValue"
      :placeholder="field.display?.placeholder"
      :maxlength="field.config?.maxLength"
      :minlength="field.config?.minLength"
      :disabled="disabled"
      clearable
    />

    <!-- 多行文本 -->
    <el-input
      v-else-if="field.type === 'textarea'"
      v-model="modelValue"
      type="textarea"
      :placeholder="field.display?.placeholder"
      :maxlength="field.config?.maxLength"
      :rows="4"
      :disabled="disabled"
      show-word-limit
    />

    <!-- 数字输入 -->
    <el-input-number
      v-else-if="field.type === 'number'"
      v-model="modelValue"
      :min="field.config?.min"
      :max="field.config?.max"
      :step="field.config?.step || 1"
      :precision="field.config?.precision"
      :disabled="disabled"
      style="width: 100%"
    />

    <!-- 下拉选择 -->
    <el-select
      v-else-if="field.type === 'select'"
      v-model="modelValue"
      :placeholder="field.display?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      clearable
      style="width: 100%"
    >
      <el-option
        v-for="option in field.config?.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      />
    </el-select>

    <!-- 多选下拉 -->
    <el-select
      v-else-if="field.type === 'multi_select'"
      v-model="modelValue"
      :placeholder="field.display?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      multiple
      clearable
      style="width: 100%"
    >
      <el-option
        v-for="option in field.config?.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      />
    </el-select>

    <!-- 单选按钮 -->
    <el-radio-group
      v-else-if="field.type === 'radio'"
      v-model="modelValue"
      :disabled="disabled"
    >
      <el-radio
        v-for="option in field.config?.options"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </el-radio>
    </el-radio-group>

    <!-- 复选框 -->
    <el-checkbox-group
      v-else-if="field.type === 'checkbox'"
      v-model="modelValue"
      :disabled="disabled"
    >
      <el-checkbox
        v-for="option in field.config?.options"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </el-checkbox>
    </el-checkbox-group>

    <!-- 日期选择 -->
    <el-date-picker
      v-else-if="field.type === 'date'"
      v-model="modelValue"
      type="date"
      :placeholder="field.display?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      style="width: 100%"
    />

    <!-- 日期时间选择 -->
    <el-date-picker
      v-else-if="field.type === 'datetime'"
      v-model="modelValue"
      type="datetime"
      :placeholder="field.display?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      style="width: 100%"
    />

    <!-- 级联选择 -->
    <el-cascader
      v-else-if="field.type === 'cascader'"
      v-model="modelValue"
      :options="field.config?.cascaderOptions"
      :placeholder="field.display?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      clearable
      style="width: 100%"
    />

    <!-- 标签显示 -->
    <div v-else-if="field.type === 'tag'" class="tag-field">
      <el-tag
        v-if="modelValue"
        :type="field.config?.tagType || 'primary'"
        :size="field.config?.tagSize || 'default'"
        :effect="field.config?.tagEffect || 'light'"
      >
        {{ modelValue }}
      </el-tag>
      <span v-else class="tag-placeholder">{{ field.display?.placeholder || field.label }}</span>
    </div>

    <!-- 标签输入 -->
    <div v-else-if="field.type === 'tag_input'" class="tag-input-field">
      <el-input
        v-model="modelValue"
        :placeholder="field.display?.placeholder || `请输入${field.label}`"
        :disabled="disabled"
        clearable
      >
        <template #append>
          <el-tag
            v-if="modelValue"
            :type="field.config?.tagType || 'primary'"
            size="small"
          >
            预览
          </el-tag>
        </template>
      </el-input>
    </div>

    <!-- 范围选择 -->
    <el-slider
      v-else-if="field.type === 'range'"
      v-model="modelValue"
      :min="field.config?.min || 0"
      :max="field.config?.max || 100"
      :step="field.config?.step || 1"
      :disabled="disabled"
      range
    />

    <!-- 文件上传 -->
    <el-upload
      v-else-if="field.type === 'file'"
      :action="uploadAction"
      :headers="uploadHeaders"
      :accept="field.config?.accept?.join(',')"
      :multiple="(field.config?.maxCount || 1) > 1"
      :limit="field.config?.maxCount || 1"
      :file-list="fileList"
      :disabled="disabled"
      @success="handleFileSuccess"
      @remove="handleFileRemove"
    >
      <el-button type="primary" :disabled="disabled">
        <el-icon><Upload /></el-icon>
        选择文件
      </el-button>
      <template #tip>
        <div class="el-upload__tip">
          <span v-if="field.config?.accept">
            支持格式：{{ field.config.accept.join(', ') }}
          </span>
          <span v-if="field.config?.maxSize">
            ，文件大小不超过 {{ formatFileSize(field.config.maxSize) }}
          </span>
        </div>
      </template>
    </el-upload>

    <!-- 自定义组件 -->
    <component
      v-else-if="field.type === 'custom' && field.config?.component"
      :is="field.config.component"
      v-model="modelValue"
      v-bind="field.config?.props"
      :disabled="disabled"
    />

    <!-- 未知类型 -->
    <div v-else class="unknown-field-type">
      <el-alert
        title="未知字段类型"
        :description="`字段类型 '${field.type}' 暂不支持`"
        type="warning"
        show-icon
      />
    </div>

    <!-- 帮助文本 -->
    <div v-if="field.display?.helpText" class="field-help-text">
      <el-text type="info" size="small">
        <el-icon><InfoFilled /></el-icon>
        {{ field.display.helpText }}
      </el-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Upload, InfoFilled } from '@element-plus/icons-vue'
// import { useAuthStore } from '@/stores/auth'

interface FieldOption {
  value: string | number
  label: string
  disabled?: boolean
  children?: FieldOption[]
}

interface CascaderOption {
  value: string
  label: string
  children?: CascaderOption[]
}

interface OrderField {
  id: string
  name: string
  label: string
  type: string
  required: boolean
  defaultValue?: any
  config?: {
    options?: FieldOption[]
    min?: number
    max?: number
    step?: number
    precision?: number
    minLength?: number
    maxLength?: number
    pattern?: string
    accept?: string[]
    maxSize?: number
    maxCount?: number
    cascaderOptions?: CascaderOption[]
    component?: string
    props?: Record<string, any>
  }
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    custom?: string
    message?: string
  }
  display: {
    order: number
    group?: string
    helpText?: string
    placeholder?: string
  }
}

interface Props {
  field: OrderField
  modelValue: any
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// const authStore = useAuthStore()

// 文件上传相关
const fileList = ref([])
const uploadAction = computed(() => '/api/v1/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer token`
}))

// 双向绑定
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 文件上传处理
const handleFileSuccess = (response: any, file: any) => {
  if (response.success) {
    const newValue = props.field.config?.maxCount === 1 
      ? response.data.url 
      : [...(modelValue.value || []), response.data.url]
    emit('update:modelValue', newValue)
  }
}

const handleFileRemove = (file: any) => {
  if (props.field.config?.maxCount === 1) {
    emit('update:modelValue', null)
  } else {
    const newValue = (modelValue.value || []).filter((url: string) => url !== file.url)
    emit('update:modelValue', newValue)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 初始化默认值
watch(() => props.field, (newField) => {
  if (newField.defaultValue !== undefined && modelValue.value === undefined) {
    emit('update:modelValue', newField.defaultValue)
  }
}, { immediate: true })
</script>

<style scoped>
.dynamic-form-field {
  width: 100%;
}

.field-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.unknown-field-type {
  margin: 8px 0;
}

:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
</style>
