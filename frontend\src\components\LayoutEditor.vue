<template>
  <div class="layout-editor">
    <div class="groups-list">
      <div
        v-for="(group, index) in modelValue.groups"
        :key="index"
        class="group-item"
      >
        <el-card shadow="hover">
          <template #header>
            <div class="group-header">
              <span class="group-title">分组 {{ index + 1 }}</span>
              <el-button
                size="small"
                type="danger"
                @click="removeGroup(index)"
                :disabled="modelValue.groups.length <= 1"
              >
                删除
              </el-button>
            </div>
          </template>
          
          <el-form label-width="80px">
            <el-form-item label="分组名称">
              <el-input
                v-model="group.name"
                placeholder="分组的英文名称，如：customer_info"
              />
            </el-form-item>
            
            <el-form-item label="显示标签">
              <el-input
                v-model="group.label"
                placeholder="分组的中文标签，如：客户信息"
              />
            </el-form-item>
            
            <el-form-item label="列数">
              <el-select v-model="group.columns" style="width: 100%">
                <el-option label="1列" :value="1" />
                <el-option label="2列" :value="2" />
                <el-option label="3列" :value="3" />
                <el-option label="4列" :value="4" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <div class="add-group">
      <el-button type="primary" @click="addGroup">
        <el-icon><Plus /></el-icon>
        添加分组
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'

interface FormGroup {
  name: string
  label: string
  columns: number
  collapsed?: boolean
}

interface FormLayout {
  groups: FormGroup[]
}

interface Props {
  modelValue: FormLayout
}

interface Emits {
  (e: 'update:modelValue', value: FormLayout): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 添加分组
const addGroup = () => {
  const newGroup: FormGroup = {
    name: `group_${props.modelValue.groups.length + 1}`,
    label: `分组 ${props.modelValue.groups.length + 1}`,
    columns: 2
  }
  
  const newLayout = {
    ...props.modelValue,
    groups: [...props.modelValue.groups, newGroup]
  }
  
  emit('update:modelValue', newLayout)
}

// 删除分组
const removeGroup = (index: number) => {
  const newLayout = {
    ...props.modelValue,
    groups: props.modelValue.groups.filter((_, i) => i !== index)
  }
  
  emit('update:modelValue', newLayout)
}
</script>

<style lang="scss" scoped>
.layout-editor {
  .groups-list {
    .group-item {
      margin-bottom: 16px;
      
      .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .group-title {
          font-weight: 500;
        }
      }
    }
  }
  
  .add-group {
    text-align: center;
    margin-top: 16px;
  }
}
</style>
