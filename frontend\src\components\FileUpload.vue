<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :file-list="fileList"
      :list-type="listType"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-progress="handleProgress"
      :disabled="disabled"
      :name="name"
      :data="uploadData"
    >
      <template v-if="listType === 'picture-card'">
        <el-icon><Plus /></el-icon>
      </template>
      <template v-else>
        <el-button type="primary" :disabled="disabled">
          <el-icon><Upload /></el-icon>
          {{ buttonText }}
        </el-button>
      </template>
    </el-upload>
    
    <!-- 上传提示 -->
    <div v-if="tip" class="upload-tip">{{ tip }}</div>
    
    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="50%">
      <img :src="previewUrl" alt="预览图片" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import type { UploadFile, UploadFiles, UploadProps, UploadInstance } from 'element-plus'

interface Props {
  modelValue?: UploadFile[]
  uploadType?: 'single' | 'multiple' | 'avatar' | 'screenshots'
  listType?: 'text' | 'picture' | 'picture-card'
  multiple?: boolean
  limit?: number
  accept?: string
  disabled?: boolean
  buttonText?: string
  tip?: string
  maxSize?: number // MB
}

interface Emits {
  (e: 'update:modelValue', files: UploadFile[]): void
  (e: 'success', response: any, file: UploadFile): void
  (e: 'error', error: any, file: UploadFile): void
  (e: 'progress', progress: number): void
}

const props = withDefaults(defineProps<Props>(), {
  uploadType: 'single',
  listType: 'text',
  multiple: false,
  limit: 1,
  accept: 'image/*',
  disabled: false,
  buttonText: '选择文件',
  maxSize: 5
})

const emit = defineEmits<Emits>()

// Store
const authStore = useAuthStore()

// 响应式数据
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
  switch (props.uploadType) {
    case 'screenshots':
      return `${baseUrl}/api/v1/upload/screenshots`
    case 'multiple':
      return `${baseUrl}/api/v1/upload/multiple`
    default:
      return `${baseUrl}/api/v1/upload/single`
  }
})

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

const uploadData = computed(() => ({
  type: props.uploadType === 'avatar' ? 'avatar' : 
        props.uploadType === 'screenshots' ? 'screenshot' : 'documents'
}))

const name = computed(() => {
  switch (props.uploadType) {
    case 'avatar':
      return 'avatar'
    case 'screenshots':
      return 'screenshots'
    case 'multiple':
      return 'files'
    default:
      return 'file'
  }
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fileList.value = [...newVal]
  }
}, { immediate: true })

// 上传前检查
const beforeUpload = (file: File) => {
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 检查文件类型
  if (props.uploadType === 'avatar' || props.uploadType === 'screenshots') {
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    
    if (props.uploadType === 'avatar' && !isImage) {
      ElMessage.error('头像只能是图片格式')
      return false
    }
    
    if (props.uploadType === 'screenshots' && !isImage && !isVideo) {
      ElMessage.error('截图只能是图片或视频格式')
      return false
    }
  }

  return true
}

// 上传成功
const handleSuccess = (response: any, file: UploadFile) => {
  if (response.success) {
    // 更新文件信息
    file.url = response.data.url
    file.response = response.data
    
    emit('update:modelValue', [...fileList.value])
    emit('success', response, file)
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 上传失败
const handleError = (error: any, file: UploadFile) => {
  console.error('文件上传失败:', error)
  emit('error', error, file)
  ElMessage.error('文件上传失败')
}

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
    emit('update:modelValue', [...fileList.value])
  }
}

// 预览文件
const handlePreview = (file: UploadFile) => {
  if (file.url) {
    previewUrl.value = file.url
    previewVisible.value = true
  }
}

// 上传进度
const handleProgress = (event: any) => {
  emit('progress', event.percent)
}

// 清空文件列表
const clearFiles = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  emit('update:modelValue', [])
}

// 手动上传
const submit = () => {
  uploadRef.value?.submit()
}

// 暴露方法
defineExpose({
  clearFiles,
  submit
})
</script>

<style lang="scss" scoped>
.file-upload {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
}
</style>
