<template>
  <el-dialog
    v-model="visible"
    title="员工详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-if="employee" class="employee-detail" v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(employee.status)">
              {{ getStatusText(employee.status) }}
            </el-tag>
          </div>
        </template>
        
        <div class="employee-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="employee.avatar">
              {{ employee.nickname?.[0] || employee.username[0] }}
            </el-avatar>
            <div class="level-badge">
              <el-tag :type="getLevelType(employee.level)" size="large">
                等级 {{ employee.level }}
              </el-tag>
            </div>
          </div>
          
          <div class="info-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">{{ employee.username }}</el-descriptions-item>
              <el-descriptions-item label="昵称">
                {{ employee.nickname || '未设置' }}
              </el-descriptions-item>

              <el-descriptions-item label="手机号">
                {{ employee.phone || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="注册时间">
                {{ formatDate(employee.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后更新">
                {{ formatDate(employee.updatedAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="总收益" span="2">
                <span class="earnings">¥{{ employee.totalEarnings.toFixed(2) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 统计数据 -->
      <el-card class="info-card" shadow="never" v-if="employeeStats">
        <template #header>
          <span>工作统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ employeeStats.statistics.totalTasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ employeeStats.statistics.completedTasks }}</div>
              <div class="stat-label">已完成任务</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ employeeStats.statistics.completionRate.toFixed(1) }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ employeeStats.statistics.totalHours }}</div>
              <div class="stat-label">总工时</div>
            </div>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">{{ employeeStats.statistics.avgHoursPerTask.toFixed(1) }}</div>
              <div class="stat-label">平均每任务工时</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">¥{{ employeeStats.user.totalEarnings.toFixed(2) }}</div>
              <div class="stat-label">累计收益</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 工作表现评级 -->
      <el-card class="info-card" shadow="never" v-if="employeeStats">
        <template #header>
          <span>工作表现</span>
        </template>
        
        <div class="performance-section">
          <div class="performance-item">
            <div class="performance-label">任务完成率</div>
            <el-progress 
              :percentage="employeeStats.statistics.completionRate" 
              :color="getProgressColor(employeeStats.statistics.completionRate)"
            />
          </div>
          
          <div class="performance-item">
            <div class="performance-label">工作效率</div>
            <el-progress 
              :percentage="getEfficiencyScore(employeeStats.statistics.avgHoursPerTask)" 
              :color="getProgressColor(getEfficiencyScore(employeeStats.statistics.avgHoursPerTask))"
            />
          </div>
          
          <div class="performance-item">
            <div class="performance-label">活跃度</div>
            <el-progress 
              :percentage="getActivityScore(employeeStats.statistics.totalTasks)" 
              :color="getProgressColor(getActivityScore(employeeStats.statistics.totalTasks))"
            />
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑员工</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { userApi } from '@/api/users'
import type { User, UserStatus, UserStats } from '@/types'
import { formatDate } from '@/utils/date'

// Props
interface Props {
  modelValue: boolean
  employeeId: string | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', employee: User): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const employee = ref<User | null>(null)
const employeeStats = ref<UserStats | null>(null)
const loading = ref(false)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.employeeId) {
    fetchEmployeeDetail()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    employee.value = null
    employeeStats.value = null
  }
})

// 获取员工详情
const fetchEmployeeDetail = async () => {
  if (!props.employeeId) return
  
  try {
    loading.value = true
    
    // 并行获取员工基本信息和统计数据
    const [userResponse, statsResponse] = await Promise.all([
      userApi.getUserById(props.employeeId),
      userApi.getEmployeeStats(props.employeeId)
    ])
    
    if (userResponse.success && userResponse.data) {
      employee.value = userResponse.data
    }
    
    if (statsResponse.success && statsResponse.data) {
      employeeStats.value = statsResponse.data
    }
  } catch (error) {
    console.error('获取员工详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 编辑员工
const handleEdit = () => {
  if (employee.value) {
    emit('edit', employee.value)
    handleClose()
  }
}

// 状态相关方法
const getStatusType = (status: UserStatus) => {
  const statusMap = {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    BANNED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: UserStatus) => {
  const statusMap = {
    ACTIVE: '激活',
    INACTIVE: '未激活',
    BANNED: '已封禁'
  }
  return statusMap[status] || status
}

const getLevelType = (level: number) => {
  if (level >= 4) return 'success'
  if (level >= 3) return 'primary'
  if (level >= 2) return 'warning'
  return 'info'
}

// 进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
}

// 计算效率分数（基于平均工时，工时越少效率越高）
const getEfficiencyScore = (avgHours: number) => {
  if (avgHours <= 2) return 100
  if (avgHours <= 4) return 80
  if (avgHours <= 6) return 60
  if (avgHours <= 8) return 40
  return 20
}

// 计算活跃度分数（基于总任务数）
const getActivityScore = (totalTasks: number) => {
  if (totalTasks >= 50) return 100
  if (totalTasks >= 30) return 80
  if (totalTasks >= 20) return 60
  if (totalTasks >= 10) return 40
  return Math.min(totalTasks * 4, 20)
}
</script>

<style lang="scss" scoped>
.employee-detail {
  .info-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }

  .employee-info {
    display: flex;
    gap: 24px;

    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .level-badge {
        margin-top: 8px;
      }
    }

    .info-section {
      flex: 1;
    }

    .earnings {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-success);
    }
  }

  .stat-item {
    text-align: center;
    padding: 16px;
    background: var(--bg-page);
    border-radius: 8px;

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      color: var(--color-primary);
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: var(--text-secondary);
    }
  }

  .performance-section {
    .performance-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .performance-label {
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .employee-detail {
    .employee-info {
      flex-direction: column;
      text-align: center;

      .avatar-section {
        align-self: center;
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__body {
        .el-descriptions__table {
          .el-descriptions__cell {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}

// 头像样式优化
:deep(.el-avatar) {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  color: white;
  font-weight: 600;
  font-size: 24px;
}

// 进度条样式优化
:deep(.el-progress) {
  .el-progress__text {
    font-weight: 600;
  }
}

// 描述列表样式优化
:deep(.el-descriptions) {
  .el-descriptions__header {
    margin-bottom: 16px;
  }

  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        &.is-bordered-label {
          background-color: var(--bg-page);
          font-weight: 500;
        }
      }
    }
  }
}
</style>
