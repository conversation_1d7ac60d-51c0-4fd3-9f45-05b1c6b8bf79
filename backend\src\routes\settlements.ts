import { Router } from 'express';
import {
  getSettlements,
  getSettlementById,
  settleSettlement,
  cancelSettlement,
  getSettlementStats,
} from '../controllers/settlementController';
import { authenticateToken, requireBossOrAdmin } from '../middleware/auth';
import { validate, validateQuery, validateParams } from '../utils/validation';
import { commonSchemas } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 结算查询参数验证schema
const settlementQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  status: Joi.string().valid('PENDING', 'COMPLETED', 'CANCELLED').allow('').optional(),
  userId: Joi.string().allow('').optional(),
  keyword: Joi.string().allow('').optional(),
});

// 结算操作验证schema
const settleSchema = Joi.object({
  notes: Joi.string().optional(),
});

// 获取结算列表
router.get('/', 
  authenticateToken, 
  validateQuery(settlementQuerySchema), 
  getSettlements
);

// 获取结算统计信息
router.get('/stats', 
  authenticateToken, 
  getSettlementStats
);

// 获取结算详情
router.get('/:id', 
  authenticateToken, 
  validateParams(Joi.object({ id: commonSchemas.id })),
  getSettlementById
);

// 执行结算（需要老板或管理员权限）
router.post('/:id/settle', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(settleSchema),
  settleSettlement
);

// 取消结算（需要老板或管理员权限）
router.post('/:id/cancel', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  cancelSettlement
);

export default router;
