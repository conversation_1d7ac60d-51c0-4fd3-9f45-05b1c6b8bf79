import { Router } from 'express';
import {
  createEmployeeGameSkill,
  getEmployeeGameSkills,
  getEmployeeGameSkillById,
  updateEmployeeGameSkill,
  deleteEmployeeGameSkill,
  certifyEmployeeSkill,
  getSkillMatchesForGame,
  getTaskAssignmentRecommendations,
  autoAssignTask,
  getGameHotnessStats,
  getEmployeeSkillDistribution,
  getEmployeeSkillOverview
} from '../controllers/employeeSkillController';
import { authenticateToken, requireBossOrAdmin, requireAdmin } from '../middleware/auth';
import { validate, validateQuery, validateParams } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 员工技能查询参数验证schema
const employeeSkillQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().valid('skillLevel', 'createdAt').optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  userId: Joi.string().optional(),
  gameId: Joi.string().optional(),
  skillLevel: Joi.string().valid('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER').optional(),
  isActive: Joi.boolean().optional(),
  certified: Joi.boolean().optional()
});

// 创建员工技能验证schema
const createEmployeeSkillSchema = Joi.object({
  userId: Joi.string().required(),
  gameId: Joi.string().required(),
  skillLevel: Joi.string().valid('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER').required(),
  maxRankLevel: Joi.number().integer().min(1).optional(),
  isActive: Joi.boolean().optional().default(true)
});

// 更新员工技能验证schema
const updateEmployeeSkillSchema = Joi.object({
  skillLevel: Joi.string().valid('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER').optional(),
  maxRankLevel: Joi.number().integer().min(1).optional(),
  isActive: Joi.boolean().optional(),
  certifiedBy: Joi.string().optional()
});

// 任务分配推荐验证schema
const taskAssignmentRecommendationSchema = Joi.object({
  maxRecommendations: Joi.number().integer().min(1).max(20).optional().default(5),
  considerWorkload: Joi.boolean().optional().default(true),
  considerSkillLevel: Joi.boolean().optional().default(true),
  considerExperience: Joi.boolean().optional().default(true),
  minMatchScore: Joi.number().min(0).max(100).optional().default(50)
});

// 自动分配任务验证schema
const autoAssignTaskSchema = Joi.object({
  minMatchScore: Joi.number().min(0).max(100).optional().default(70),
  excludeOverloaded: Joi.boolean().optional().default(true)
});

// ID参数验证schema
const idParamSchema = Joi.object({
  id: Joi.string().required()
});

const gameIdParamSchema = Joi.object({
  gameId: Joi.string().required()
});

const orderIdParamSchema = Joi.object({
  orderId: Joi.string().required()
});

const userIdParamSchema = Joi.object({
  userId: Joi.string().required()
});

// ==================== 员工技能管理路由 ====================

// 创建员工游戏技能（需要管理员或老板权限）
router.post('/',
  authenticateToken,
  requireBossOrAdmin,
  validate(createEmployeeSkillSchema),
  createEmployeeGameSkill
);

// 获取员工游戏技能列表
router.get('/',
  authenticateToken,
  validateQuery(employeeSkillQuerySchema),
  getEmployeeGameSkills
);

// 获取员工游戏技能详情
router.get('/:id',
  authenticateToken,
  validateParams(idParamSchema),
  getEmployeeGameSkillById
);

// 更新员工游戏技能（需要管理员或老板权限）
router.put('/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  validate(updateEmployeeSkillSchema),
  updateEmployeeGameSkill
);

// 删除员工游戏技能（需要管理员权限）
router.delete('/:id',
  authenticateToken,
  requireAdmin,
  validateParams(idParamSchema),
  deleteEmployeeGameSkill
);

// 认证员工技能（需要管理员或老板权限）
router.post('/:id/certify',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  certifyEmployeeSkill
);

// ==================== 技能匹配和分配路由 ====================

// 获取指定游戏的技能员工匹配
router.get('/games/:gameId/matches',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(gameIdParamSchema),
  getSkillMatchesForGame
);

// 获取任务分配推荐（需要老板或管理员权限）
router.post('/assignments/:orderId/recommendations',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(orderIdParamSchema),
  validate(taskAssignmentRecommendationSchema),
  getTaskAssignmentRecommendations
);

// 自动分配任务（需要老板或管理员权限）
router.post('/assignments/:orderId/auto-assign',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(orderIdParamSchema),
  validate(autoAssignTaskSchema),
  autoAssignTask
);

// ==================== 统计分析路由 ====================

// 获取游戏热度统计（需要老板或管理员权限）
router.get('/analytics/game-hotness',
  authenticateToken,
  requireBossOrAdmin,
  getGameHotnessStats
);

// 获取员工技能分布统计
router.get('/analytics/skill-distribution',
  authenticateToken,
  getEmployeeSkillDistribution
);

// 获取员工个人技能概览
router.get('/users/:userId/overview',
  authenticateToken,
  validateParams(userIdParamSchema),
  getEmployeeSkillOverview
);

// ==================== 批量操作路由 ====================

// 批量创建员工技能（需要管理员权限）
router.post('/batch',
  authenticateToken,
  requireAdmin,
  validate(Joi.object({
    skills: Joi.array().items(createEmployeeSkillSchema).min(1).required()
  })),
  async (req, res) => {
    // TODO: 实现批量创建逻辑
    res.status(501).json({
      success: false,
      message: '批量创建功能待实现'
    });
  }
);

// 批量更新员工技能状态（需要管理员权限）
router.put('/batch/status',
  authenticateToken,
  requireAdmin,
  validate(Joi.object({
    skillIds: Joi.array().items(Joi.string()).min(1).required(),
    isActive: Joi.boolean().required()
  })),
  async (req, res) => {
    // TODO: 实现批量更新逻辑
    res.status(501).json({
      success: false,
      message: '批量更新功能待实现'
    });
  }
);

// 批量认证员工技能（需要管理员或老板权限）
router.post('/batch/certify',
  authenticateToken,
  requireBossOrAdmin,
  validate(Joi.object({
    skillIds: Joi.array().items(Joi.string()).min(1).required()
  })),
  async (req, res) => {
    // TODO: 实现批量认证逻辑
    res.status(501).json({
      success: false,
      message: '批量认证功能待实现'
    });
  }
);

export default router;
