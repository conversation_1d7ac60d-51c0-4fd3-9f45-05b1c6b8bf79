import { Router } from 'express';
import {
  createTask,
  getTasks,
  getAvailableTasks,
  acceptTask,
  getTaskById,
  updateTask,
  updateTaskProgress,
  updateTaskStatus,
  reviewTask,
  cancelTask,
  getTaskStats,
  getMyTasks,
  calculateCommission,
  fixTaskHours,
} from '../controllers/taskController';
import { authenticateToken, requireBossOrAdmin, requireEmployee } from '../middleware/auth';
import { validate, validateQuery, validateParams } from '../utils/validation';
import { taskSchemas, commonSchemas } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 任务查询参数验证schema
const taskQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  status: Joi.string().valid('PENDING', 'ACCEPTED', 'IN_PROGRESS', 'SUBMITTED', 'APPROVED', 'REJECTED', 'COMPLETED', 'CANCELLED').optional(),
  assignType: Joi.string().valid('DIRECT', 'SYSTEM').optional(),
  assigneeId: Joi.string().optional(),
  orderId: Joi.string().optional(),
  keyword: Joi.string().allow('').optional(),
});



// 创建任务（需要老板或管理员权限）
router.post('/', 
  authenticateToken, 
  requireBossOrAdmin,
  validate(taskSchemas.create), 
  createTask
);

// 获取任务列表
router.get('/', 
  authenticateToken, 
  validateQuery(taskQuerySchema), 
  getTasks
);

// 获取可接单的任务列表（员工查看）
router.get('/available', 
  authenticateToken, 
  requireEmployee,
  validateQuery(commonSchemas.pagination), 
  getAvailableTasks
);

// 获取我的任务
router.get('/my', 
  authenticateToken, 
  validateQuery(taskQuerySchema), 
  getMyTasks
);

// 获取任务统计信息
router.get('/stats',
  authenticateToken,
  getTaskStats
);

// 计算佣金
router.post('/calculate-commission',
  authenticateToken,
  requireBossOrAdmin,
  calculateCommission
);



// 获取任务详情
router.get('/:id',
  authenticateToken,
  validateParams(Joi.object({ id: commonSchemas.id })),
  getTaskById
);

// 更新任务信息（需要老板或管理员权限）
router.put('/:id', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(taskSchemas.update),
  updateTask
);

// 接单（员工抢单）
router.post('/:id/accept', 
  authenticateToken, 
  requireEmployee,
  validateParams(Joi.object({ id: commonSchemas.id })),
  acceptTask
);

// 更新任务进度（员工操作）
router.post('/:id/progress',
  authenticateToken,
  requireEmployee,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(taskSchemas.updateProgress),
  updateTaskProgress
);

// 员工更新任务状态（开始/暂停/提交任务）
router.patch('/:id/status',
  authenticateToken,
  requireEmployee,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(Joi.object({
    status: Joi.string().valid('IN_PROGRESS', 'PAUSED', 'SUBMITTED').required()
  })),
  updateTaskStatus
);

// 审核任务（需要老板或管理员权限）
router.post('/:id/review',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(taskSchemas.review),
  reviewTask
);

// 取消任务
router.post('/:id/cancel',
  authenticateToken,
  validateParams(Joi.object({ id: commonSchemas.id })),
  cancelTask
);

// 修复现有任务的工时数据（管理员功能）
router.post('/fix-hours',
  authenticateToken,
  requireBossOrAdmin,
  fixTaskHours
);

export default router;
