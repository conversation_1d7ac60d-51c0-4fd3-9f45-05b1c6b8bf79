<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754354508707_9l8zats0q" time="2025/08/05 08:41">
    <content>
      用户服务器环境：宝塔面板已安装必要软件（Nginx、MySQL、Redis、Node.js等），无域名使用公网IP访问，准备开始完整部署流程
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754354679212_nwve8wr7c" time="2025/08/05 08:44">
    <content>
      用户服务器软件版本：Node.js v18.19.1, MySQL 5.7.40, Redis 7.4.1, Nginx 1.28.0。MySQL版本5.7需要特殊配置以支持Prisma ORM，需要调整数据库连接字符串和配置
    </content>
    <tags>#其他</tags>
  </item>
</memory>