import { Router } from 'express';
import {
  uploadSingleFile,
  uploadMultipleFiles,
  uploadScreenshots,
} from '../controllers/uploadController';
import { authenticateToken } from '../middleware/auth';
import { uploadSingle, uploadMultiple } from '../middleware/upload';

const router = Router();

// 单文件上传
router.post('/single', 
  authenticateToken,
  uploadSingle('file'),
  uploadSingleFile
);

// 多文件上传
router.post('/multiple', 
  authenticateToken,
  uploadMultiple('files', 5),
  uploadMultipleFiles
);



// 上传任务截图
router.post('/screenshots', 
  authenticateToken,
  uploadMultiple('screenshots', 10),
  uploadScreenshots
);

export default router;
