/*
  Warnings:

  - Added the required column `gameType` to the `orders` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `orders` DROP FOREIGN KEY `orders_currentRankId_fkey`;

-- DropForeignKey
ALTER TABLE `orders` DROP FOREIGN KEY `orders_gameId_fkey`;

-- DropForeignKey
ALTER TABLE `orders` DROP FOREIGN KEY `orders_targetRankId_fkey`;

-- DropIndex
DROP INDEX `orders_gameId_status_idx` ON `orders`;

-- AlterTable
ALTER TABLE `orders` ADD COLUMN `gameType` VARCHAR(191) NOT NULL,
    MODIFY `gameId` VARCHAR(191) NULL,
    MODIFY `currentRankId` VARCHAR(191) NULL,
    MODIFY `targetRankId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `tasks` ADD COLUMN `commissionParams` JSO<PERSON> NULL,
    ADD COLUMN `commissionType` ENUM('AUTO', 'MANUAL') NOT NULL DEFAULT 'MANUAL';

-- CreateIndex
CREATE INDEX `orders_gameType_status_idx` ON `orders`(`gameType`, `status`);

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_gameId_fkey` FOREIGN KEY (`gameId`) REFERENCES `games`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_currentRankId_fkey` FOREIGN KEY (`currentRankId`) REFERENCES `game_ranks`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_targetRankId_fkey` FOREIGN KEY (`targetRankId`) REFERENCES `game_ranks`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- RenameIndex
ALTER TABLE `orders` RENAME INDEX `orders_createdById_fkey` TO `orders_createdById_idx`;
