import { io, Socket } from 'socket.io-client'
import { ElMessage, ElNotification } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { useTaskStore } from '@/stores/tasks'
import { useOrderStore } from '@/stores/orders'
import { useNotificationStore } from '@/stores/notifications'

export interface SocketNotification {
  type: string
  message: string
  data?: any
  timestamp: string
}

export class SocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor() {
    this.connect()
  }

  // 连接Socket.IO服务器
  private connect() {
    const authStore = useAuthStore()

    if (!authStore.token) {
      console.warn('未找到认证token，无法连接Socket.IO')
      return
    }

    try {
      // 连接到Socket.IO服务器（使用专门的Socket URL，不包含API路径）
      const isProduction = import.meta.env.PROD;

      // 生产环境配置
      const productionConfig = {
        path: '/api/socket.io', // 使用Nginx代理的路径
        auth: { token: authStore.token },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        autoConnect: true
      };

      // 开发环境配置
      const developmentConfig = {
        auth: { token: authStore.token },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        autoConnect: true
      };
      
      if (isProduction) {
        // 生产环境：不指定URL，只指定path，让它自动使用当前域名
        this.socket = io(productionConfig);
      } else {
        // 开发环境：使用环境变量或默认的localhost
        const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3000';
        this.socket = io(socketUrl, developmentConfig);
      }

      this.setupEventListeners()
    } catch (error) {
      console.error('Socket.IO连接失败:', error)
    }
  }

  // 设置事件监听器
  private setupEventListeners() {
    if (!this.socket) return

    // 连接成功
    this.socket.on('connect', () => {
      console.log('Socket.IO连接成功:', this.socket?.id)
      this.reconnectAttempts = 0
      
      const authStore = useAuthStore()
      if (authStore.user) {
        this.joinUserRoom(authStore.user.id)
      }
    })

    // 连接失败
    this.socket.on('connect_error', (error) => {
      console.error('Socket.IO连接错误:', error)
      this.handleReconnect()
    })

    // 断开连接
    this.socket.on('disconnect', (reason) => {
      console.log('Socket.IO断开连接:', reason)
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.handleReconnect()
      }
    })

    // 认证成功
    this.socket.on('connected', (data) => {
      console.log('Socket.IO认证成功:', data)
      ElMessage.success('实时通信连接成功')
    })

    // 新任务通知
    this.socket.on('new-task', (notification: SocketNotification) => {
      this.handleNewTaskNotification(notification)
    })

    // 任务状态更新通知
    this.socket.on('task-status-update', (notification: SocketNotification) => {
      this.handleTaskStatusUpdateNotification(notification)
    })

    // 任务分配通知
    this.socket.on('task-assigned', (notification: SocketNotification) => {
      this.handleTaskAssignedNotification(notification)
    })

    // 任务进度更新通知
    this.socket.on('task-progress-update', (notification: SocketNotification) => {
      this.handleTaskProgressUpdateNotification(notification)
    })

    // 订单状态更新通知
    this.socket.on('order-status-update', (notification: SocketNotification) => {
      this.handleOrderStatusUpdateNotification(notification)
    })

    // 系统通知
    this.socket.on('system-notification', (notification: SocketNotification) => {
      this.handleSystemNotification(notification)
    })

    // 离线通知
    this.socket.on('offline-notifications', (data: { type: string, data: any[], timestamp: string }) => {
      this.handleOfflineNotifications(data)
    })

    // 结算完成通知
    this.socket.on('settlement-completed', (notification: SocketNotification) => {
      this.handleSettlementCompletedNotification(notification)
    })

    // 任务超时提醒
    this.socket.on('task-deadline-warning', (notification: SocketNotification) => {
      this.handleTaskDeadlineWarningNotification(notification)
    })

    // 系统维护通知
    this.socket.on('system-maintenance', (notification: SocketNotification) => {
      this.handleSystemMaintenanceNotification(notification)
    })

    // 心跳响应
    this.socket.on('pong', () => {
      // console.log('收到心跳响应')
    })

    // 用户上线通知
    this.socket.on('user-online', (notification: SocketNotification) => {
      this.handleUserOnlineNotification(notification)
    })

    // 用户下线通知
    this.socket.on('user-offline', (notification: SocketNotification) => {
      this.handleUserOfflineNotification(notification)
    })

    // 强制下线通知
    this.socket.on('force-logout', (notification: SocketNotification) => {
      this.handleForceLogoutNotification(notification)
    })
  }

  // 处理重连
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('Socket.IO重连失败，已达到最大重试次数')
      ElMessage.error('实时通信连接失败，请刷新页面重试')
    }
  }

  // 加入用户房间
  private joinUserRoom(userId: string) {
    if (this.socket) {
      // 后端会自动根据用户信息加入房间，这里不需要手动发送join事件
      console.log(`用户 ${userId} 已自动加入对应房间`)
    }
  }

  // 处理新任务通知
  private handleNewTaskNotification(notification: SocketNotification) {
    const authStore = useAuthStore()
    
    // 只有员工才显示新任务通知
    if (authStore.user?.role === 'EMPLOYEE') {
      ElNotification({
        title: '新任务通知',
        message: notification.message,
        type: 'info',
        duration: 5000,
        onClick: () => {
          // 跳转到可接单任务页面
          window.location.href = '/employee/available-tasks'
        }
      })

      // 更新任务store
      const taskStore = useTaskStore()
      taskStore.fetchAvailableTasks()
    }
  }

  // 处理任务状态更新通知
  private handleTaskStatusUpdateNotification(notification: SocketNotification) {
    ElNotification({
      title: '任务状态更新',
      message: notification.message,
      type: 'success',
      duration: 4000
    })

    // 更新相关store
    const taskStore = useTaskStore()
    taskStore.fetchTasks()
    taskStore.fetchMyTasks()
  }

  // 处理任务分配通知
  private handleTaskAssignedNotification(notification: SocketNotification) {
    ElNotification({
      title: '新任务分配',
      message: notification.message,
      type: 'warning',
      duration: 6000,
      onClick: () => {
        // 跳转到我的任务页面
        window.location.href = '/employee/my-tasks'
      }
    })

    // 更新任务store
    const taskStore = useTaskStore()
    taskStore.fetchMyTasks()
  }

  // 处理任务进度更新通知
  private handleTaskProgressUpdateNotification(notification: SocketNotification) {
    const authStore = useAuthStore()
    
    // 只有老板和管理员才显示进度更新通知
    if (authStore.user?.role === 'BOSS' || authStore.user?.role === 'ADMIN') {
      ElNotification({
        title: '任务进度更新',
        message: notification.message,
        type: 'info',
        duration: 4000
      })

      // 更新任务store
      const taskStore = useTaskStore()
      taskStore.fetchTasks()
    }
  }

  // 处理订单状态更新通知
  private handleOrderStatusUpdateNotification(notification: SocketNotification) {
    ElNotification({
      title: '订单状态更新',
      message: notification.message,
      type: 'success',
      duration: 4000
    })

    // 更新订单store
    const orderStore = useOrderStore()
    orderStore.fetchOrders()
  }

  // 处理系统通知
  private handleSystemNotification(notification: SocketNotification) {
    ElNotification({
      title: '系统通知',
      message: notification.message,
      type: 'warning',
      duration: 6000
    })

    // 添加到应用通知列表
    const appStore = useAppStore()
    appStore.showNotification({
      title: '系统通知',
      message: notification.message,
      type: 'warning'
    })
  }

  // 发送心跳
  public sendHeartbeat() {
    if (this.socket?.connected) {
      this.socket.emit('ping')
    }
  }

  // 断开连接
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  // 重新连接
  public reconnect() {
    this.disconnect()
    this.reconnectAttempts = 0
    this.connect()
  }

  // 处理离线通知
  private handleOfflineNotifications(data: { type: string, data: any[], timestamp: string }) {
    const notificationStore = useNotificationStore()

    if (data.data && data.data.length > 0) {
      // 批量添加离线通知到store
      notificationStore.addNotifications(data.data)

      ElNotification({
        title: '离线通知',
        message: `您有 ${data.data.length} 条未读通知`,
        type: 'info',
        duration: 3000
      })
    }
  }

  // 处理结算完成通知
  private handleSettlementCompletedNotification(notification: SocketNotification) {
    const notificationStore = useNotificationStore()

    ElNotification({
      title: '结算完成',
      message: notification.message,
      type: 'success',
      duration: 5000
    })

    // 添加到通知store
    notificationStore.addNotification({
      id: Date.now().toString(),
      userId: '',
      title: '结算完成',
      content: notification.message,
      type: 'SUCCESS',
      isRead: false,
      createdAt: notification.timestamp,
      updatedAt: notification.timestamp,
      data: notification.data
    })
  }

  // 处理任务超时提醒
  private handleTaskDeadlineWarningNotification(notification: SocketNotification) {
    const notificationStore = useNotificationStore()

    ElNotification({
      title: '任务超时提醒',
      message: notification.message,
      type: 'warning',
      duration: 8000
    })

    // 添加到通知store
    notificationStore.addNotification({
      id: Date.now().toString(),
      userId: '',
      title: '任务超时提醒',
      content: notification.message,
      type: 'WARNING',
      isRead: false,
      createdAt: notification.timestamp,
      updatedAt: notification.timestamp,
      data: notification.data
    })
  }

  // 处理系统维护通知
  private handleSystemMaintenanceNotification(notification: SocketNotification) {
    const notificationStore = useNotificationStore()

    ElNotification({
      title: '系统维护通知',
      message: notification.message,
      type: 'warning',
      duration: 10000
    })

    // 添加到通知store
    notificationStore.addNotification({
      id: Date.now().toString(),
      userId: '',
      title: '系统维护通知',
      content: notification.message,
      type: 'WARNING',
      isRead: false,
      createdAt: notification.timestamp,
      updatedAt: notification.timestamp,
      data: notification.data
    })
  }

  // 处理用户上线通知
  private handleUserOnlineNotification(notification: SocketNotification) {
    const authStore = useAuthStore()

    // 只有管理员和老板才显示用户上线通知
    if (authStore.user?.role === 'BOSS' || authStore.user?.role === 'ADMIN') {
      ElNotification({
        title: '用户上线',
        message: notification.message,
        type: 'info',
        duration: 3000
      })
    }
  }

  // 处理用户下线通知
  private handleUserOfflineNotification(notification: SocketNotification) {
    const authStore = useAuthStore()

    // 只有管理员和老板才显示用户下线通知
    if (authStore.user?.role === 'BOSS' || authStore.user?.role === 'ADMIN') {
      ElNotification({
        title: '用户下线',
        message: notification.message,
        type: 'info',
        duration: 3000
      })
    }
  }

  // 处理强制下线通知
  private handleForceLogoutNotification(notification: SocketNotification) {
    const authStore = useAuthStore()

    ElNotification({
      title: '强制下线通知',
      message: notification.message,
      type: 'error',
      duration: 0, // 不自动关闭
      onClick: () => {
        // 强制退出登录
        authStore.logout()
        window.location.href = '/login'
      }
    })

    // 延迟3秒后自动退出
    setTimeout(() => {
      authStore.logout()
      window.location.href = '/login'
    }, 3000)
  }

  // 检查连接状态
  public isConnected(): boolean {
    return this.socket?.connected || false
  }

  // 获取Socket实例
  public getSocket(): Socket | null {
    return this.socket
  }
}

// 创建单例实例
let socketService: SocketService | null = null

export const initializeSocketService = (): SocketService => {
  if (!socketService) {
    socketService = new SocketService()
  }
  return socketService
}

export const getSocketService = (): SocketService | null => {
  return socketService
}

export const destroySocketService = () => {
  if (socketService) {
    socketService.disconnect()
    socketService = null
  }
}
