const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addPasswordField() {
  try {
    console.log('🔧 为原神游戏添加游戏密码字段...');
    
    // 查找原神游戏
    const yuanshenGame = await prisma.game.findFirst({
      where: { name: 'ys' }
    });
    
    if (!yuanshenGame) {
      console.log('❌ 未找到原神游戏');
      return;
    }
    
    console.log('✅ 找到原神游戏:', yuanshenGame.displayName);
    
    // 检查是否已经存在游戏密码字段
    const existingField = await prisma.gameFormField.findFirst({
      where: {
        gameId: yuanshenGame.id,
        fieldKey: 'game_password'
      }
    });
    
    if (existingField) {
      console.log('⚠️  游戏密码字段已存在，更新为活跃状态...');
      await prisma.gameFormField.update({
        where: { id: existingField.id },
        data: { isActive: true }
      });
    } else {
      console.log('➕ 创建新的游戏密码字段...');
      
      // 获取当前最大的排序值
      const maxSortOrder = await prisma.gameFormField.findFirst({
        where: { gameId: yuanshenGame.id },
        orderBy: { sortOrder: 'desc' },
        select: { sortOrder: true }
      });
      
      const newSortOrder = (maxSortOrder?.sortOrder || 0) + 1;
      
      await prisma.gameFormField.create({
        data: {
          gameId: yuanshenGame.id,
          fieldKey: 'game_password',
          fieldLabel: '游戏密码',
          fieldType: 'PASSWORD',
          isRequired: true,
          isActive: true,
          sortOrder: newSortOrder,
          placeholder: '请输入游戏密码'
        }
      });
    }
    
    console.log('✅ 游戏密码字段添加成功！');
    
    // 验证结果
    console.log('\n🔍 验证更新后的字段列表...');
    const updatedFields = await prisma.gameFormField.findMany({
      where: {
        gameId: yuanshenGame.id,
        isActive: true
      },
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log(`📋 原神游戏现在有 ${updatedFields.length} 个字段:`);
    updatedFields.forEach((field, index) => {
      console.log(`${index + 1}. ${field.fieldLabel} (${field.fieldKey}) - ${field.fieldType}`);
    });
    
  } catch (error) {
    console.error('❌ 添加字段失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

addPasswordField();
