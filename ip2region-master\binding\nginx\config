ngx_addon_name=ngx_http_ip2region_module

NGX_HTTP_IP2REGION_SRCS="                                                           \
                $ngx_addon_dir/src/ngx_http_ip2region_module.c                      \
                "

NGX_HTTP_IP2REGION_DEPS="                                                           \
                "

if test -n "$ngx_module_link"; then
    ngx_module_type=HTTP
    ngx_module_name=$ngx_addon_name
    ngx_module_deps="$NGX_HTTP_IP2REGION_DEPS"
    ngx_module_srcs="$NGX_HTTP_IP2REGION_SRCS"
    ngx_module_libs="-lxdb_searcher"

    . auto/module
else
    HTTP_MODULES="$HTTP_MODULES $ngx_addon_name"
    NGX_ADDON_DEPS="$NGX_ADDON_DEPS $NGX_HTTP_IP2REGION_DEPS"
    NGX_ADDON_SRCS="$NGX_ADDON_SRCS $NGX_HTTP_IP2REGION_SRCS"
fi