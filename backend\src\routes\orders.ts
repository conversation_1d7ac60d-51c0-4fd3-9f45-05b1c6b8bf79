import { Router } from 'express';
import {
  createOrder,
  getOrders,
  getOrderById,
  updateOrder,
  deleteOrder,
  getOrderStats,
  getMyOrders,
} from '../controllers/orderController';
import { authenticateToken, requireBossOrAdmin } from '../middleware/auth';
import { validate, validateQuery, validateParams, validateOrderCreate } from '../utils/validation';
import { orderSchemas, commonSchemas } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 订单查询参数验证schema
const orderQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  status: Joi.string().valid('PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED').optional(),
  priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional(),
  keyword: Joi.string().allow('').optional(),
  createdById: Joi.string().optional(),
});

// 创建订单（需要老板或管理员权限）
router.post('/',
  authenticateToken,
  requireBossOrAdmin,
  validateOrderCreate,  // 使用动态验证中间件
  createOrder
);

// 获取订单列表
router.get('/', 
  authenticateToken, 
  validateQuery(orderQuerySchema), 
  getOrders
);

// 获取我的订单
router.get('/my', 
  authenticateToken, 
  validateQuery(orderQuerySchema), 
  getMyOrders
);

// 获取订单统计信息
router.get('/stats', 
  authenticateToken, 
  getOrderStats
);

// 获取订单详情
router.get('/:id', 
  authenticateToken, 
  validateParams(Joi.object({ id: commonSchemas.id })),
  getOrderById
);

// 更新订单（需要老板或管理员权限）
router.put('/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(orderSchemas.update),
  updateOrder
);

// 更新订单状态（员工也可以操作）
router.put('/:id/status',
  authenticateToken,
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(Joi.object({
    status: Joi.string().valid('PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED').required()
  })),
  updateOrder
);

// 删除订单（需要老板或管理员权限）
router.delete('/:id', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  deleteOrder
);

export default router;
