-- 多游戏支持数据库迁移脚本
-- 此迁移将现有的单一王者荣耀系统扩展为支持多游戏的系统

-- 1. MySQL不需要单独创建枚举类型，直接在表定义中使用ENUM

-- 2. 创建游戏配置表
CREATE TABLE `games` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `displayName` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `icon` VARCHAR(191) NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `sortOrder` INTEGER NOT NULL DEFAULT 0,
    `config` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `games_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 创建游戏段位表
CREATE TABLE `game_ranks` (
    `id` VARCHAR(191) NOT NULL,
    `gameId` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `displayName` VARCHAR(191) NOT NULL,
    `level` INTEGER NOT NULL,
    `difficultyMultiplier` DOUBLE NOT NULL DEFAULT 1.0,
    `icon` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `game_ranks_gameId_level_idx`(`gameId`, `level`),
    UNIQUE INDEX `game_ranks_gameId_name_key`(`gameId`, `name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 4. 创建游戏价格规则表
CREATE TABLE `game_price_rules` (
    `id` VARCHAR(191) NOT NULL,
    `gameId` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `ruleType` ENUM('RANK_BASED', 'TIME_BASED', 'FIXED_PRICE', 'CUSTOM') NOT NULL DEFAULT 'RANK_BASED',
    `basePrice` DOUBLE NOT NULL DEFAULT 0,
    `pricePerLevel` DOUBLE NOT NULL DEFAULT 0,
    `multiplier` DOUBLE NOT NULL DEFAULT 1.0,
    `minLevel` INTEGER NULL,
    `maxLevel` INTEGER NULL,
    `priority` ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `game_price_rules_gameId_isActive_idx`(`gameId`, `isActive`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 5. 创建员工游戏技能表
CREATE TABLE `employee_game_skills` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `gameId` VARCHAR(191) NOT NULL,
    `skillLevel` ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER') NOT NULL DEFAULT 'BEGINNER',
    `maxRankLevel` INTEGER NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `certifiedAt` DATETIME(3) NULL,
    `certifiedBy` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `employee_game_skills_gameId_skillLevel_idx`(`gameId`, `skillLevel`),
    UNIQUE INDEX `employee_game_skills_userId_gameId_key`(`userId`, `gameId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 6. 插入默认的王者荣耀游戏数据
INSERT INTO `games` (`id`, `name`, `displayName`, `description`, `isActive`, `sortOrder`, `createdAt`, `updatedAt`) 
VALUES ('game_wzry_001', 'wzry', '王者荣耀', '腾讯王者荣耀游戏代练', true, 1, NOW(), NOW());

-- 7. 插入王者荣耀段位数据
INSERT INTO `game_ranks` (`id`, `gameId`, `name`, `displayName`, `level`, `difficultyMultiplier`, `isActive`, `createdAt`, `updatedAt`) VALUES
('rank_wzry_001', 'game_wzry_001', '倔强青铜', '倔强青铜', 1, 1.0, true, NOW(), NOW()),
('rank_wzry_002', 'game_wzry_001', '秩序白银', '秩序白银', 2, 1.2, true, NOW(), NOW()),
('rank_wzry_003', 'game_wzry_001', '荣耀黄金', '荣耀黄金', 3, 1.4, true, NOW(), NOW()),
('rank_wzry_004', 'game_wzry_001', '尊贵铂金', '尊贵铂金', 4, 1.6, true, NOW(), NOW()),
('rank_wzry_005', 'game_wzry_001', '永恒钻石', '永恒钻石', 5, 1.8, true, NOW(), NOW()),
('rank_wzry_006', 'game_wzry_001', '至尊星耀', '至尊星耀', 6, 2.0, true, NOW(), NOW()),
('rank_wzry_007', 'game_wzry_001', '最强王者', '最强王者', 7, 2.5, true, NOW(), NOW()),
('rank_wzry_008', 'game_wzry_001', '荣耀王者', '荣耀王者', 8, 3.0, true, NOW(), NOW());

-- 8. 插入默认价格规则
INSERT INTO `game_price_rules` (`id`, `gameId`, `name`, `description`, `ruleType`, `basePrice`, `pricePerLevel`, `multiplier`, `isActive`, `createdAt`, `updatedAt`)
VALUES ('price_wzry_001', 'game_wzry_001', '王者荣耀基础价格', '基于段位等级的价格计算', 'RANK_BASED', 50.0, 30.0, 1.0, true, NOW(), NOW());

-- 9. 为订单表添加新字段（先添加可空字段）
ALTER TABLE `orders` ADD COLUMN `gameId` VARCHAR(191) NULL;
ALTER TABLE `orders` ADD COLUMN `currentRankId` VARCHAR(191) NULL;
ALTER TABLE `orders` ADD COLUMN `targetRankId` VARCHAR(191) NULL;

-- 10. 数据迁移：将现有订单关联到王者荣耀游戏
-- 首先更新所有订单的gameId
UPDATE `orders` SET `gameId` = 'game_wzry_001' WHERE `gameId` IS NULL;

-- 11. 创建段位映射函数来转换现有的段位数据
-- 更新currentRankId
UPDATE `orders` o
SET `currentRankId` = (
    SELECT gr.id
    FROM `game_ranks` gr
    WHERE gr.gameId = 'game_wzry_001'
    AND gr.name = o.currentRank
    LIMIT 1
)
WHERE o.currentRankId IS NULL;

-- 更新targetRankId
UPDATE `orders` o
SET `targetRankId` = (
    SELECT gr.id
    FROM `game_ranks` gr
    WHERE gr.gameId = 'game_wzry_001'
    AND gr.name = o.targetRank
    LIMIT 1
)
WHERE o.targetRankId IS NULL;

-- 12. 处理无法匹配的段位数据（设置为默认值）
-- 如果有订单的段位无法匹配，设置为青铜段位
UPDATE `orders` SET `currentRankId` = 'rank_wzry_001' WHERE `currentRankId` IS NULL;
UPDATE `orders` SET `targetRankId` = 'rank_wzry_002' WHERE `targetRankId` IS NULL;

-- 13. 将新字段设置为非空
ALTER TABLE `orders` MODIFY COLUMN `gameId` VARCHAR(191) NOT NULL;
ALTER TABLE `orders` MODIFY COLUMN `currentRankId` VARCHAR(191) NOT NULL;
ALTER TABLE `orders` MODIFY COLUMN `targetRankId` VARCHAR(191) NOT NULL;

-- 14. 添加外键约束
ALTER TABLE `orders` ADD CONSTRAINT `orders_gameId_fkey` FOREIGN KEY (`gameId`) REFERENCES `games`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `orders` ADD CONSTRAINT `orders_currentRankId_fkey` FOREIGN KEY (`currentRankId`) REFERENCES `game_ranks`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `orders` ADD CONSTRAINT `orders_targetRankId_fkey` FOREIGN KEY (`targetRankId`) REFERENCES `game_ranks`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 15. 添加其他表的外键约束
ALTER TABLE `game_ranks` ADD CONSTRAINT `game_ranks_gameId_fkey` FOREIGN KEY (`gameId`) REFERENCES `games`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `game_price_rules` ADD CONSTRAINT `game_price_rules_gameId_fkey` FOREIGN KEY (`gameId`) REFERENCES `games`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `employee_game_skills` ADD CONSTRAINT `employee_game_skills_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `employee_game_skills` ADD CONSTRAINT `employee_game_skills_gameId_fkey` FOREIGN KEY (`gameId`) REFERENCES `games`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `employee_game_skills` ADD CONSTRAINT `employee_game_skills_certifiedBy_fkey` FOREIGN KEY (`certifiedBy`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 16. 添加索引优化查询性能
ALTER TABLE `orders` ADD INDEX `orders_gameId_status_idx`(`gameId`, `status`);
ALTER TABLE `orders` ADD INDEX `orders_createdAt_idx`(`createdAt`);

-- 17. 为现有员工添加王者荣耀技能（可选，根据需要调整）
-- 这里可以根据员工的历史任务完成情况来设置技能等级
-- INSERT INTO `employee_game_skills` (`id`, `userId`, `gameId`, `skillLevel`, `isActive`, `createdAt`, `updatedAt`)
-- SELECT CONCAT('skill_', u.id, '_wzry'), u.id, 'game_wzry_001', 'INTERMEDIATE', true, NOW(), NOW()
-- FROM `users` u WHERE u.role = 'EMPLOYEE';

-- 18. 创建视图用于兼容性查询（可选）
CREATE VIEW `orders_with_rank_names` AS
SELECT
    o.*,
    cr.name as currentRankName,
    tr.name as targetRankName,
    g.displayName as gameName
FROM `orders` o
LEFT JOIN `game_ranks` cr ON o.currentRankId = cr.id
LEFT JOIN `game_ranks` tr ON o.targetRankId = tr.id
LEFT JOIN `games` g ON o.gameId = g.id;

-- 迁移完成
-- 注意：在生产环境中执行此迁移前，请务必备份数据库
