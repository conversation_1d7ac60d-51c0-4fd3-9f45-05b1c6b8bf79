<template>
  <div class="order-detail-display">
    <!-- 基础订单信息 -->
    <el-card class="basic-info-card">
      <template #header>
        <div class="card-header">
          <span>基础信息</span>
          <el-tag :type="getStatusTagType(order.status)">
            {{ getStatusLabel(order.status) }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单编号">
          {{ order.orderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="游戏类型">
          <div class="game-info">
            <el-avatar
              v-if="gameInfo?.icon"
              :src="gameInfo.icon"
              :alt="gameInfo.displayName"
              shape="square"
              size="small"
            >
              {{ gameInfo.displayName?.charAt(0) }}
            </el-avatar>
            <span>{{ gameInfo?.displayName || order.gameType }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="订单价格">
          <span class="price">¥{{ order.price }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityTagType(order.priority)" size="small">
            {{ getPriorityLabel(order.priority) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(order.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="截止时间">
          {{ order.deadline ? formatDate(order.deadline) : '无' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 动态表单数据展示 - 按照新思路要求的格式化展示 -->
    <el-card v-if="formFieldsDisplay.length > 0" class="form-data-card">
      <template #header>
        <div class="card-header">
          <span>详细信息</span>
          <el-tag v-if="gameInfo" size="small" type="info">
            {{ gameInfo.displayName }} 专属字段
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item
          v-for="field in formFieldsDisplay"
          :key="field.fieldKey"
          :label="field.fieldLabel"
        >
          <div class="field-value">
            <!-- 文本类型 -->
            <span v-if="field.fieldType === 'TEXT' || field.fieldType === 'TEXTAREA'">
              {{ field.value || '-' }}
            </span>

            <!-- 密码类型 -->
            <div v-else-if="field.fieldType === 'PASSWORD'" class="password-field">
              <span v-if="showPasswords[field.fieldKey]">{{ field.value || '-' }}</span>
              <span v-else>{{ '●'.repeat((field.value || '').length) }}</span>
              <el-button
                size="small"
                type="text"
                @click="togglePassword(field.fieldKey)"
                :icon="showPasswords[field.fieldKey] ? EyeSlash : Eye"
              />
            </div>

            <!-- 选择类型 -->
            <el-tag v-else-if="field.fieldType === 'SELECT'" type="primary" size="small">
              {{ field.value || '-' }}
            </el-tag>

            <!-- 多选类型 -->
            <div v-else-if="field.fieldType === 'CHECKBOX'" class="checkbox-values">
              <el-tag
                v-for="value in (field.value || [])"
                :key="value"
                type="success"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ value }}
              </el-tag>
              <span v-if="!field.value || field.value.length === 0">-</span>
            </div>

            <!-- 数字类型 -->
            <span v-else-if="field.fieldType === 'NUMBER'" class="number-value">
              {{ field.value !== undefined && field.value !== null ? field.value : '-' }}
            </span>

            <!-- 图片类型 -->
            <div v-else-if="field.fieldType === 'IMAGE'" class="image-value">
              <el-image
                v-if="field.value"
                :src="field.value"
                :alt="field.fieldLabel"
                style="width: 100px; height: 100px;"
                fit="cover"
                :preview-src-list="[field.value]"
              />
              <span v-else>-</span>
            </div>

            <!-- 其他类型 -->
            <span v-else>{{ field.value || '-' }}</span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 传统字段展示（兼容旧数据） -->
    <el-card v-if="hasTraditionalFields" class="traditional-fields-card">
      <template #header>
        <span>订单信息</span>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item v-if="order.customerName" label="客户姓名">
          {{ order.customerName }}
        </el-descriptions-item>
        <el-descriptions-item v-if="order.customerContact" label="联系方式">
          {{ order.customerContact }}
        </el-descriptions-item>
        <el-descriptions-item v-if="order.gameAccount" label="游戏账号">
          {{ order.gameAccount }}
        </el-descriptions-item>
        <el-descriptions-item v-if="order.gamePassword" label="游戏密码">
          <div class="password-field">
            <span v-if="showTraditionalPassword">{{ order.gamePassword }}</span>
            <span v-else>{{ '●'.repeat(order.gamePassword.length) }}</span>
            <el-button
              size="small"
              type="text"
              @click="showTraditionalPassword = !showTraditionalPassword"
              :icon="showTraditionalPassword ? Hide : View"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="order.currentRank" label="当前段位">
          <el-tag type="info" size="small">{{ order.currentRank }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="order.targetRank" label="目标段位">
          <el-tag type="success" size="small">{{ order.targetRank }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="order.requirements" label="特殊要求" :span="2">
          <div class="requirements">{{ order.requirements }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { View, Hide } from '@element-plus/icons-vue'
import { getGameWithFormFields } from '../api/game'
import { type GameFormField } from '../api/gameFormFields'
import { formatDate } from '../utils/date'

// Props
interface Props {
  order: any // 订单数据
}

const props = defineProps<Props>()

// 响应式数据
const gameInfo = ref<any>(null)
const gameFormFields = ref<GameFormField[]>([])
const showPasswords = ref<Record<string, boolean>>({})
const showTraditionalPassword = ref(false)

// 计算属性 - 按照新思路要求的格式化展示逻辑
const formFieldsDisplay = computed(() => {
  // 优先使用details字段，兼容formData字段
  const orderData = props.order.details || props.order.formData
  if (!orderData || !gameFormFields.value.length) {
    return []
  }

  // 关键实现：按照表单模板的顺序来展示，而不是按JSON的无序键
  // 这确保了：
  // 1. 保证顺序：始终按照表单模板的顺序来展示
  // 2. 保证完整性：即使某个订单的formData里缺少了一个字段，模板里有，依然会展示出标签（值为空）
  // 3. 适应变化：即使老板后来在模板里新增了一个字段，老的订单虽然没有这个数据，但新订单会正确地展示出来
  return gameFormFields.value
    .sort((a, b) => a.sortOrder - b.sortOrder) // 按排序顺序展示
    .map(field => {
      const value = orderData[field.fieldKey]
      return {
        ...field,
        value: value,
        // 显示状态：即使值为空也显示（如果是必填字段或有值）
        shouldDisplay: field.isRequired || (value !== undefined && value !== null && value !== '')
      }
    })
    .filter(field => field.shouldDisplay) // 只显示应该显示的字段
})

const hasTraditionalFields = computed(() => {
  return props.order.customerName || 
         props.order.customerContact || 
         props.order.gameAccount || 
         props.order.gamePassword || 
         props.order.currentRank || 
         props.order.targetRank || 
         props.order.requirements
})

// 方法
const loadGameInfo = async () => {
  if (!props.order.gameId) return

  try {
    const game = await getGameWithFormFields(props.order.gameId)
    gameInfo.value = game
    gameFormFields.value = game.formFields || []
  } catch (error) {
    console.error('加载游戏信息失败:', error)
  }
}

const togglePassword = (fieldKey: string) => {
  showPasswords.value[fieldKey] = !showPasswords.value[fieldKey]
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: 'warning',
    ASSIGNED: 'info',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '待处理',
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityTagType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    LOW: 'info',
    NORMAL: 'primary',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityLabel = (priority: string) => {
  const priorityMap: Record<string, string> = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return priorityMap[priority] || priority
}

// 生命周期
onMounted(() => {
  loadGameInfo()
})
</script>

<style scoped>
.order-detail-display {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

.field-value {
  min-height: 20px;
  display: flex;
  align-items: center;
}

.password-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-values {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.number-value {
  font-weight: 500;
}

.image-value {
  display: flex;
  align-items: center;
}

.requirements {
  white-space: pre-wrap;
  line-height: 1.5;
}

.basic-info-card,
.form-data-card,
.traditional-fields-card {
  margin-bottom: 0;
}
</style>
