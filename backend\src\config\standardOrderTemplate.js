/**
 * 标准订单模板配置 (JavaScript版本)
 */

// 标准订单字段定义
const STANDARD_ORDER_FIELDS = [
  {
    id: 'field_1',
    name: 'customerName',
    label: '客户姓名',
    type: 'text',
    required: true,
    display: {
      order: 1,
      group: 'customer',
      helpText: '请输入客户的真实姓名',
      placeholder: '请输入客户姓名'
    },
    config: {
      minLength: 1,
      maxLength: 50
    },
    validation: {
      minLength: 1,
      maxLength: 50,
      message: '客户姓名长度应在1-50个字符之间'
    }
  },
  {
    id: 'field_2',
    name: 'customerContact',
    label: '联系方式',
    type: 'text',
    required: false,
    display: {
      order: 2,
      group: 'customer',
      helpText: '客户的联系方式（电话、微信等）',
      placeholder: '请输入联系方式'
    },
    config: {
      maxLength: 100
    },
    validation: {
      maxLength: 100,
      message: '联系方式长度不能超过100个字符'
    }
  },
  {
    id: 'field_3',
    name: 'gameAccount',
    label: '游戏账号',
    type: 'text',
    required: true,
    display: {
      order: 3,
      group: 'game',
      helpText: '游戏账号信息',
      placeholder: '请输入游戏账号'
    },
    config: {
      minLength: 1,
      maxLength: 100
    },
    validation: {
      minLength: 1,
      maxLength: 100,
      message: '游戏账号长度应在1-100个字符之间'
    }
  },
  {
    id: 'field_4',
    name: 'gamePassword',
    label: '游戏密码',
    type: 'password',
    required: true,
    display: {
      order: 4,
      group: 'game',
      helpText: '游戏账号密码',
      placeholder: '请输入游戏密码'
    },
    config: {
      minLength: 1,
      maxLength: 100
    },
    validation: {
      minLength: 1,
      maxLength: 100,
      message: '游戏密码长度应在1-100个字符之间'
    }
  },
  {
    id: 'field_5',
    name: 'price',
    label: '订单价格',
    type: 'number',
    required: true,
    defaultValue: 0,
    display: {
      order: 5,
      group: 'order',
      helpText: '订单总价格（元）',
      placeholder: '请输入订单价格'
    },
    config: {
      min: 0,
      max: 999999
    },
    validation: {
      message: '订单价格必须大于等于0'
    }
  },
  {
    id: 'field_6',
    name: 'gameServer',
    label: '游戏区服',
    type: 'text',
    required: false,
    display: {
      order: 6,
      group: 'game',
      helpText: '游戏服务器或区域信息',
      placeholder: '请输入游戏区服'
    },
    config: {
      maxLength: 100
    }
  },
  {
    id: 'field_7',
    name: 'currentLevel',
    label: '当前段位/等级',
    type: 'text',
    required: false,
    display: {
      order: 7,
      group: 'game',
      helpText: '当前游戏段位或等级',
      placeholder: '请输入当前段位/等级'
    },
    config: {
      maxLength: 50
    }
  },
  {
    id: 'field_8',
    name: 'targetLevel',
    label: '目标段位/等级',
    type: 'text',
    required: false,
    display: {
      order: 8,
      group: 'game',
      helpText: '目标游戏段位或等级',
      placeholder: '请输入目标段位/等级'
    },
    config: {
      maxLength: 50
    }
  },
  {
    id: 'field_9',
    name: 'requirements',
    label: '特殊要求/备注',
    type: 'textarea',
    required: false,
    display: {
      order: 9,
      group: 'order',
      helpText: '订单的特殊要求或备注信息',
      placeholder: '请输入特殊要求或备注'
    },
    config: {
      maxLength: 500
    }
  }
];

// 标准显示配置
const STANDARD_DISPLAY_CONFIG = {
  layout: 'form',
  groups: [
    {
      id: 'customer',
      label: '客户信息',
      order: 1,
      collapsible: false
    },
    {
      id: 'game',
      label: '游戏信息',
      order: 2,
      collapsible: false
    },
    {
      id: 'order',
      label: '订单信息',
      order: 3,
      collapsible: false
    }
  ],
  theme: 'default'
};

// 标准业务规则
const STANDARD_BUSINESS_RULES = {
  validation: {
    required: ['customerName', 'gameAccount', 'gamePassword', 'price'],
    conditional: []
  },
  workflow: {
    autoAssign: true,
    requireApproval: false
  }
};

module.exports = {
  STANDARD_ORDER_FIELDS,
  STANDARD_DISPLAY_CONFIG,
  STANDARD_BUSINESS_RULES
};
