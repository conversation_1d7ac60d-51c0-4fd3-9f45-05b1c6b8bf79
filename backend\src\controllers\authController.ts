import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { LoginRequest, RegisterRequest, UpdatePasswordRequest } from '../types/auth';

const authService = new AuthService();

// 用户登录
export const login = asyncHandler(async (req: Request, res: Response) => {
  const loginData: LoginRequest = req.body;

  // 获取客户端信息
  const clientInfo = {
    ipAddress: req.ip || req.socket.remoteAddress || '未知',
    userAgent: req.get('User-Agent'),
    deviceInfo: {
      platform: req.get('X-Platform'),
      version: req.get('X-Version'),
      timestamp: new Date().toISOString(),
    },
  };

  const result = await authService.login(loginData, clientInfo);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: '登录成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 用户注册
export const register = asyncHandler(async (req: Request, res: Response) => {
  const registerData: RegisterRequest = req.body;
  
  const result = await authService.register(registerData);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '注册成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// 获取当前用户信息
export const getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  
  const user = await authService.getCurrentUser(userId);
  
  const response: ApiResponse = {
    success: true,
    data: user,
    message: '获取用户信息成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新密码
export const updatePassword = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const passwordData: UpdatePasswordRequest = req.body;
  
  const result = await authService.updatePassword(userId, passwordData);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '密码更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});



// 用户登出
export const logout = asyncHandler(async (req: Request, res: Response) => {
  // 从Authorization header获取token
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    const result = await authService.logout(token);
    const response: ApiResponse = {
      success: true,
      data: result,
      message: '登出成功',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  } else {
    const response: ApiResponse = {
      success: true,
      message: '登出成功',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(response);
  }
});
