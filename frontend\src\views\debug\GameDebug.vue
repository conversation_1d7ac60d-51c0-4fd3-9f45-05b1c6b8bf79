<template>
  <div class="game-debug">
    <h2>游戏API调试页面</h2>
    
    <div class="debug-section">
      <h3>认证状态</h3>
      <p>Token: {{ token ? token.substring(0, 20) + '...' : '无' }}</p>
      <p>用户角色: {{ userRole }}</p>
    </div>
    
    <div class="debug-section">
      <h3>API测试</h3>
      <el-button @click="testGamesAPI" type="primary" :loading="loading">
        测试游戏API
      </el-button>
      <el-button @click="testDirectAPI" type="success" :loading="loading">
        直接API调用
      </el-button>
    </div>
    
    <div class="debug-section">
      <h3>响应结果</h3>
      <pre>{{ responseData }}</pre>
    </div>
    
    <div class="debug-section">
      <h3>错误信息</h3>
      <pre v-if="errorData">{{ errorData }}</pre>
    </div>
  </div></template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { useGameStore } from '@/stores/game'
import { useAuthStore } from '@/stores/auth'
import axios from 'axios'

const gameStore = useGameStore()
const authStore = useAuthStore()

const loading = ref(false)
const responseData = ref('')
const errorData = ref('')
const token = ref('')
const userRole = ref('')

onMounted(() => {
  token.value = localStorage.getItem('token') || ''
  userRole.value = authStore.user?.role || '未知'
})

const testGamesAPI = async () => {
  try {
    loading.value = true
    responseData.value = '正在调用游戏Store...'
    
    const result = await gameStore.fetchGames({
      page: 1,
      limit: 10,
      sortBy: 'sortOrder',
      sortOrder: 'asc'
    })
    
    responseData.value = JSON.stringify({
      storeResult: result,
      storeGames: gameStore.games,
      storePagination: gameStore.gamesPagination
    }, null, 2)
    
    ElMessage.success('Store API调用成功')
  } catch (error: any) {
    errorData.value = JSON.stringify(error, null, 2)
    ElMessage.error('Store API调用失败')
  } finally {
    loading.value = false
  }
}

const testDirectAPI = async () => {
  try {
    loading.value = true
    responseData.value = '正在直接调用API...'
    
    const response = await axios.get('http://localhost:3000/api/v1/games', {
      headers: {
        'Authorization': `Bearer ${token.value}`
      }
    })
    
    responseData.value = JSON.stringify(response.data, null, 2)
    ElMessage.success('直接API调用成功')
  } catch (error: any) {
    errorData.value = JSON.stringify({
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    }, null, 2)
    ElMessage.error('直接API调用失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.game-debug {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  pre {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 400px;
    font-size: 12px;
  }
}
</style>
