<execution>
  <constraint>
    ## 宝塔环境限制
    - **系统要求**：CentOS 7+/Ubuntu 18+，2GB+内存，20GB+磁盘空间
    - **端口要求**：80、443、3000、3306、6379端口可用
    - **权限要求**：root权限或sudo权限
    - **网络要求**：外网访问和域名解析配置
    
    ## 项目技术约束
    - **Node.js版本**：必须18.0.0+，支持ES模块和最新特性
    - **MySQL版本**：8.0+，支持JSON字段和新认证方式
    - **Redis版本**：7.0+，支持持久化和集群模式
    - **Nginx版本**：1.18+，支持HTTP/2和WebSocket代理
  </constraint>

  <rule>
    ## 强制执行规则
    - **分步验证**：每个步骤完成后必须验证结果再进行下一步
    - **配置备份**：修改任何配置文件前必须备份原文件
    - **权限最小化**：数据库用户和文件权限遵循最小权限原则
    - **安全优先**：所有对外服务必须配置防火墙和访问控制
    - **日志记录**：所有操作和错误必须有详细的日志记录
  </rule>

  <guideline>
    ## 部署指导原则
    - **图形界面优先**：优先使用宝塔面板的图形界面操作
    - **命令行补充**：复杂操作提供命令行备选方案
    - **模板化配置**：提供标准化的配置文件模板
    - **故障预案**：为每个步骤提供故障排除指南
    - **性能监控**：部署完成后配置监控和告警
  </guideline>

  <process>
    ## 完整部署流程
    
    ### Step 1: 宝塔环境准备
    ```bash
    # 1.1 安装宝塔面板（如未安装）
    wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh
    sudo bash install.sh
    
    # 1.2 登录宝塔面板，安装必要组件
    # - Nginx 1.20+
    # - MySQL 8.0
    # - Redis 7.0
    # - Node.js 18+
    # - PM2管理器
    ```
    
    ### Step 2: 数据库配置
    ```sql
    -- 2.1 创建数据库和用户
    CREATE DATABASE game_boost_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE USER 'gameuser'@'localhost' IDENTIFIED BY 'gamepass123';
    GRANT ALL PRIVILEGES ON game_boost_db.* TO 'gameuser'@'localhost';
    FLUSH PRIVILEGES;
    
    -- 2.2 Redis配置（宝塔面板 -> Redis -> 配置修改）
    # maxmemory 512mb
    # maxmemory-policy allkeys-lru
    # save 900 1
    ```
    
    ### Step 3: 后端部署
    ```bash
    # 3.1 上传代码到 /www/wwwroot/game-boost-backend
    cd /www/wwwroot/game-boost-backend
    
    # 3.2 安装依赖
    npm install --production
    
    # 3.3 配置环境变量 (.env)
    NODE_ENV=production
    DATABASE_URL="mysql://gameuser:gamepass123@localhost:3306/game_boost_db"
    REDIS_HOST=localhost
    REDIS_PORT=6379
    JWT_SECRET=your-super-secret-jwt-key-here
    PORT=3000
    
    # 3.4 数据库迁移
    npx prisma generate
    npx prisma migrate deploy
    
    # 3.5 构建项目
    npm run build
    ```
    
    ### Step 4: PM2进程管理
    ```javascript
    // ecosystem.config.js
    module.exports = {
      apps: [{
        name: 'game-boost-backend',
        script: 'dist/index.js',
        cwd: '/www/wwwroot/game-boost-backend',
        instances: 'max',
        exec_mode: 'cluster',
        env: {
          NODE_ENV: 'production',
          PORT: 3000
        },
        error_file: './logs/err.log',
        out_file: './logs/out.log',
        log_file: './logs/combined.log',
        time: true
      }]
    };
    ```
    
    ### Step 5: 前端部署
    ```bash
    # 5.1 本地构建（开发机器）
    cd frontend
    npm install
    npm run build
    
    # 5.2 上传dist文件到 /www/wwwroot/game-boost-frontend
    # 5.3 配置Nginx站点（宝塔面板 -> 网站 -> 添加站点）
    ```
    
    ### Step 6: Nginx配置
    ```nginx
    # /www/server/panel/vhost/nginx/yourdomain.com.conf
    server {
        listen 80;
        server_name yourdomain.com;
        root /www/wwwroot/game-boost-frontend;
        index index.html;
        
        # 前端路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # API代理
        location /api/ {
            proxy_pass http://localhost:3000/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        # Socket.io支持
        location /socket.io/ {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    ```
    
    ### Step 7: SSL和安全配置
    ```bash
    # 7.1 申请SSL证书（宝塔面板 -> SSL -> Let's Encrypt）
    # 7.2 配置防火墙规则
    # 开放端口：80, 443, 22
    # 限制端口：3000, 3306, 6379（仅本地访问）
    
    # 7.3 配置文件权限
    chown -R www:www /www/wwwroot/game-boost-backend/uploads
    chmod 755 /www/wwwroot/game-boost-backend/uploads
    ```
  </process>

  <criteria>
    ## 部署验证标准
    
    ### 功能验证
    - ✅ 前端页面正常加载和路由跳转
    - ✅ 用户注册、登录功能正常
    - ✅ API接口响应正常
    - ✅ 数据库读写操作正常
    - ✅ Redis缓存功能正常
    - ✅ 文件上传功能正常
    - ✅ Socket.io实时通信正常
    
    ### 性能验证
    - ✅ 页面加载时间 < 3秒
    - ✅ API响应时间 < 500ms
    - ✅ 数据库连接池正常
    - ✅ 内存使用率 < 80%
    - ✅ CPU使用率 < 70%
    
    ### 安全验证
    - ✅ HTTPS强制跳转正常
    - ✅ 防火墙规则生效
    - ✅ 数据库访问权限正确
    - ✅ 文件上传安全检查
    - ✅ API访问频率限制
    
    ### 运维验证
    - ✅ PM2进程管理正常
    - ✅ 日志记录和轮转
    - ✅ 监控告警配置
    - ✅ 备份策略执行
    - ✅ 自动重启机制
  </criteria>
</execution>
