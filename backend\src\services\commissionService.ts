import { CommissionCalculationParams } from '../types/task';

export class CommissionService {
  // 优先级系数
  private static readonly PRIORITY_MULTIPLIERS = {
    LOW: 0.8,
    NORMAL: 1.0,
    HIGH: 1.2,
    URGENT: 1.5
  };

  // 基础佣金费率（按订单价格的百分比）
  private static readonly BASE_COMMISSION_RATE = 0.3; // 30%

  /**
   * 自动计算佣金
   */
  static calculateCommission(params: CommissionCalculationParams): {
    commission: number;
    calculation: {
      baseCommission: number;
      difficultyBonus: number;
      priorityBonus: number;
      timeBonus: number;
      totalCommission: number;
      formula: string;
    };
  } {
    const {
      orderPrice = 0,
      estimatedHours = 0,
      baseRate,
      difficultyMultiplier,
      priorityMultiplier,
      timeMultiplier
    } = params;

    // 1. 计算基础佣金
    const effectiveBaseRate = baseRate || this.BASE_COMMISSION_RATE;
    const baseCommission = orderPrice * effectiveBaseRate;

    // 2. 计算难度系数（段位功能已移除，使用默认值）
    const effectiveDifficultyMultiplier = difficultyMultiplier || 1.0;
    const difficultyBonus = baseCommission * (effectiveDifficultyMultiplier - 1);

    // 3. 计算优先级系数（从订单优先级获取，这里使用默认值）
    const effectivePriorityMultiplier = priorityMultiplier || 1.0;
    const priorityBonus = baseCommission * (effectivePriorityMultiplier - 1);

    // 4. 计算时间系数
    const timeMultiplierValue = timeMultiplier || this.calculateTimeMultiplier(estimatedHours);
    const timeBonus = baseCommission * (timeMultiplierValue - 1);

    // 5. 计算总佣金
    const totalCommission = Math.round((baseCommission + difficultyBonus + priorityBonus + timeBonus) * 100) / 100;

    // 6. 生成计算公式说明
    const formula = this.generateFormula({
      orderPrice,
      baseRate: effectiveBaseRate,
      difficultyMultiplier: effectiveDifficultyMultiplier,
      priorityMultiplier: effectivePriorityMultiplier,
      timeMultiplier: timeMultiplierValue
    });

    return {
      commission: totalCommission,
      calculation: {
        baseCommission: Math.round(baseCommission * 100) / 100,
        difficultyBonus: Math.round(difficultyBonus * 100) / 100,
        priorityBonus: Math.round(priorityBonus * 100) / 100,
        timeBonus: Math.round(timeBonus * 100) / 100,
        totalCommission,
        formula
      }
    };
  }



  /**
   * 计算时间系数
   */
  private static calculateTimeMultiplier(estimatedHours: number): number {
    if (estimatedHours <= 0) return 1.0;
    if (estimatedHours <= 5) return 1.0;   // 5小时内，无加成
    if (estimatedHours <= 10) return 1.1;  // 5-10小时，10%加成
    if (estimatedHours <= 20) return 1.2;  // 10-20小时，20%加成
    return 1.3; // 20小时以上，30%加成
  }

  /**
   * 生成计算公式说明
   */
  private static generateFormula(params: {
    orderPrice: number;
    baseRate: number;
    difficultyMultiplier: number;
    priorityMultiplier: number;
    timeMultiplier: number;
  }): string {
    const { orderPrice, baseRate, difficultyMultiplier, priorityMultiplier, timeMultiplier } = params;
    
    return `基础佣金 = 订单价格(¥${orderPrice}) × 基础费率(${(baseRate * 100).toFixed(1)}%) = ¥${(orderPrice * baseRate).toFixed(2)}
难度加成 = 基础佣金 × (难度系数${difficultyMultiplier.toFixed(2)} - 1) = ¥${(orderPrice * baseRate * (difficultyMultiplier - 1)).toFixed(2)}
优先级加成 = 基础佣金 × (优先级系数${priorityMultiplier.toFixed(2)} - 1) = ¥${(orderPrice * baseRate * (priorityMultiplier - 1)).toFixed(2)}
时间加成 = 基础佣金 × (时间系数${timeMultiplier.toFixed(2)} - 1) = ¥${(orderPrice * baseRate * (timeMultiplier - 1)).toFixed(2)}
总佣金 = 基础佣金 + 各项加成 = ¥${(orderPrice * baseRate * (1 + (difficultyMultiplier - 1) + (priorityMultiplier - 1) + (timeMultiplier - 1))).toFixed(2)}`;
  }



  /**
   * 获取优先级系数
   */
  static getPriorityMultiplier(priority: string): number {
    return this.PRIORITY_MULTIPLIERS[priority as keyof typeof this.PRIORITY_MULTIPLIERS] || 1.0;
  }
}
