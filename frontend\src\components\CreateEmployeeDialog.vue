<template>
  <el-dialog
    v-model="visible"
    title="新增员工"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="用户名" prop="username">
        <el-input 
          v-model="form.username" 
          placeholder="请输入用户名（3-20个字符）"
          @blur="checkUsername"
        />
        <div class="form-tip">用户名用于登录，创建后不可修改</div>
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input 
          v-model="form.password" 
          type="password" 
          placeholder="请输入密码（至少6位）"
          show-password
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input 
          v-model="form.confirmPassword" 
          type="password" 
          placeholder="请再次输入密码"
          show-password
        />
      </el-form-item>

      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="form.nickname" placeholder="请输入昵称（可选）" />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号（可选）" />
      </el-form-item>

      <el-form-item label="员工等级" prop="level">
        <el-select v-model="form.level" placeholder="请选择员工等级" style="width: 100%">
          <el-option
            v-for="level in [1, 2, 3, 4, 5]"
            :key="level"
            :label="`等级 ${level}`"
            :value="level"
          >
            <div class="level-option">
              <span>等级 {{ level }}</span>
              <el-tag :type="getLevelType(level)" size="small">
                {{ getLevelDescription(level) }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
        <div class="form-tip">等级决定员工可接受的任务类型和佣金比例</div>
      </el-form-item>

      <el-form-item label="账户状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="ACTIVE">
            <el-tag type="success" size="small">激活</el-tag>
            <span class="status-desc">立即可用</span>
          </el-radio>
          <el-radio value="INACTIVE">
            <el-tag type="warning" size="small">未激活</el-tag>
            <span class="status-desc">需要激活后使用</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="添加备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建员工
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { authApi } from '@/api/auth'
import type { RegisterForm, UserStatus } from '@/types'

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  phone: '',
  level: 1,
  status: 'ACTIVE' as UserStatus,
  notes: ''
})

// 自定义验证器
const validateUsername = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入用户名'))
  } else if (value.length < 3 || value.length > 20) {
    callback(new Error('用户名长度应为3-20个字符'))
  } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线'))
  } else {
    callback()
  }
}



const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度至少6位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请确认密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const rules: FormRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择员工等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择账户状态', trigger: 'change' }
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 检查用户名是否可用
const checkUsername = async () => {
  // TODO: 实现用户名重复检查
  // 这里可以调用API检查用户名是否已存在
}



// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true

      // 准备注册数据
      const registerData: RegisterForm = {
        username: form.username,
        password: form.password,
        confirmPassword: form.confirmPassword,
        nickname: form.nickname || undefined,
        phone: form.phone || undefined
      }

      const response = await authApi.register(registerData)
      if (response.success) {
        ElMessage.success('员工创建成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('创建员工失败:', error)
    ElMessage.error('创建员工失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    username: '',
    password: '',
    confirmPassword: '',
    nickname: '',
    phone: '',
    level: 1,
    status: 'ACTIVE',
    notes: ''
  })
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 等级相关方法
const getLevelType = (level: number) => {
  if (level >= 4) return 'success'
  if (level >= 3) return 'primary'
  if (level >= 2) return 'warning'
  return 'info'
}

const getLevelDescription = (level: number) => {
  const descriptions = {
    1: '初级',
    2: '中级',
    3: '高级',
    4: '专家',
    5: '大师'
  }
  return descriptions[level] || '未知'
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--text-placeholder);
  margin-top: 4px;
}

.level-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.status-desc {
  margin-left: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.dialog-footer {
  text-align: right;
}

// 单选按钮组样式优化
:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .el-radio {
    margin-right: 0;
    display: flex;
    align-items: center;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// 表单项样式优化
:deep(.el-form-item) {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 500;
    color: var(--text-primary);
  }
}

// 选择器选项样式
:deep(.el-select-dropdown) {
  .el-select-dropdown__item {
    height: auto;
    padding: 8px 20px;

    .level-option {
      line-height: 1.5;
    }
  }
}

// 密码输入框样式
:deep(.el-input) {
  .el-input__wrapper {
    &.is-focus {
      box-shadow: 0 0 0 1px var(--color-primary) inset;
    }
  }
}
</style>
