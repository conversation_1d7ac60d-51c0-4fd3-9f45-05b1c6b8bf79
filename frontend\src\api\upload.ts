import { uploadRequest } from './http'
import type { FileUploadResponse, ApiResponse } from '@/types'

export const uploadApi = {
  // 单文件上传
  uploadSingle(file: File, type?: string, onProgress?: (progress: number) => void): Promise<ApiResponse<FileUploadResponse>> {
    const formData = new FormData()
    formData.append('file', file)
    if (type) {
      formData.append('type', type)
    }
    return uploadRequest('/upload/single', formData, onProgress)
  },

  // 多文件上传
  uploadMultiple(files: File[], type?: string, onProgress?: (progress: number) => void): Promise<ApiResponse<FileUploadResponse[]>> {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    if (type) {
      formData.append('type', type)
    }
    return uploadRequest('/upload/multiple', formData, onProgress)
  },



  // 上传任务截图
  uploadScreenshots(files: File[], onProgress?: (progress: number) => void): Promise<ApiResponse<FileUploadResponse[]>> {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('screenshots', file)
    })
    return uploadRequest('/upload/screenshots', formData, onProgress)
  }
}
