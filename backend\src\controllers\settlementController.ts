import { Request, Response } from 'express';
import { SettlementService } from '../services/settlementService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { getSocketService } from '../services/socketService';

const settlementService = new SettlementService();

// 获取结算列表
export const getSettlements = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  // 如果是员工，只能查看自己的结算记录
  if (req.user!.role === 'EMPLOYEE') {
    query.userId = req.user!.id;
  }
  
  const result = await settlementService.getSettlements(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取结算列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取结算详情
export const getSettlementById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const settlement = await settlementService.getSettlementById(id);
  
  // 权限检查：员工只能查看自己的结算记录
  if (req.user!.role === 'EMPLOYEE' && settlement.userId !== req.user!.id) {
    return res.status(403).json({
      success: false,
      message: '无权访问此结算记录',
      timestamp: new Date().toISOString(),
    });
  }
  
  const response: ApiResponse = {
    success: true,
    data: settlement,
    message: '获取结算详情成功',
    timestamp: new Date().toISOString(),
  };

  return res.status(200).json(response);
});

// 执行结算
export const settleSettlement = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { notes } = req.body;

  const settlement = await settlementService.settleSettlement(id, notes);

  // 发送结算完成通知
  try {
    const socketService = getSocketService();
    await socketService.notifySettlementCompleted(settlement);
  } catch (error) {
    console.error('发送结算完成通知失败:', error);
  }

  const response: ApiResponse = {
    success: true,
    data: settlement,
    message: '结算成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 取消结算
export const cancelSettlement = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const settlement = await settlementService.cancelSettlement(id);
  
  const response: ApiResponse = {
    success: true,
    data: settlement,
    message: '取消结算成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取结算统计
export const getSettlementStats = asyncHandler(async (req: Request, res: Response) => {
  let userId: string | undefined;
  
  // 如果是员工，只能查看自己的统计
  if (req.user!.role === 'EMPLOYEE') {
    userId = req.user!.id;
  }
  
  const stats = await settlementService.getSettlementStats(userId);
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取结算统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});
