import cron from 'node-cron';
import { NotificationService } from './notificationService';
import { getSocketService } from './socketService';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { onlineUserService } from './onlineUserService';

const prisma = new PrismaClient();

export class SchedulerService {
  private notificationService: NotificationService;

  constructor() {
    this.notificationService = new NotificationService();
    this.initializeScheduledTasks();
  }

  private initializeScheduledTasks() {
    // 每天凌晨2点清理过期通知
    cron.schedule('0 2 * * *', async () => {
      try {
        logger.info('开始执行定时清理过期通知任务');
        const result = await this.notificationService.cleanupOldNotifications();
        logger.info(`定时清理过期通知完成，清理了 ${result.count} 条通知`);
      } catch (error) {
        logger.error('定时清理过期通知失败:', error);
      }
    });

    // 每小时检查任务超时
    cron.schedule('0 * * * *', async () => {
      try {
        logger.info('开始执行任务超时检查');
        await this.checkTaskDeadlines();
        logger.info('任务超时检查完成');
      } catch (error) {
        logger.error('任务超时检查失败:', error);
      }
    });

    // 每30分钟清理过期会话
    cron.schedule('*/30 * * * *', async () => {
      try {
        logger.info('开始执行过期会话清理');
        await onlineUserService.cleanupExpiredSessions();
        logger.info('过期会话清理完成');
      } catch (error) {
        logger.error('过期会话清理失败:', error);
      }
    });

    logger.info('定时任务初始化完成');
  }

  // 手动触发清理过期通知
  public async cleanupNotifications() {
    try {
      const result = await this.notificationService.cleanupOldNotifications();
      logger.info(`手动清理过期通知完成，清理了 ${result.count} 条通知`);
      return result;
    } catch (error) {
      logger.error('手动清理过期通知失败:', error);
      throw error;
    }
  }

  // 检查任务超时
  private async checkTaskDeadlines() {
    try {
      // 获取即将超时的任务（24小时内）
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const tasks = await prisma.task.findMany({
        where: {
          status: {
            in: ['ACCEPTED', 'IN_PROGRESS']
          },
          order: {
            deadline: {
              lte: tomorrow
            }
          }
        },
        include: {
          order: true,
          assignee: true
        }
      });

      // 检查Socket服务是否可用
      try {
        const socketService = getSocketService();

        for (const task of tasks) {
          if (task.assigneeId && task.order?.deadline) {
            const deadline = new Date(task.order.deadline);
            const now = new Date();
            const hoursLeft = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60));

            if (hoursLeft <= 24 && hoursLeft > 0) {
              await socketService.notifyTaskDeadlineWarning({
                ...task,
                hoursLeft
              });
            }
          }
        }
      } catch (socketError) {
        logger.warn('Socket服务不可用，跳过通知发送:', socketError);
      }

      logger.info(`检查了 ${tasks.length} 个即将超时的任务`);
    } catch (error) {
      logger.error('检查任务超时失败:', error);
      throw error;
    }
  }

  // 手动触发任务超时检查
  public async checkTaskDeadlinesManually() {
    try {
      await this.checkTaskDeadlines();
      logger.info('手动任务超时检查完成');
    } catch (error) {
      logger.error('手动任务超时检查失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
let schedulerService: SchedulerService | null = null;

export const initializeSchedulerService = (): SchedulerService => {
  if (!schedulerService) {
    schedulerService = new SchedulerService();
  }
  return schedulerService;
};

export const getSchedulerService = (): SchedulerService => {
  if (!schedulerService) {
    throw new Error('SchedulerService 未初始化');
  }
  return schedulerService;
};
