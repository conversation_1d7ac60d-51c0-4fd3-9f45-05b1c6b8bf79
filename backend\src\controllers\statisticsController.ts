import { Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { StatisticsService } from '../services/statisticsService';

const statisticsService = new StatisticsService();

// 获取综合统计数据
export const getOverviewStats = asyncHandler(async (req: Request, res: Response) => {
  let createdById: string | undefined;
  
  // 如果是老板，只能查看自己创建的数据统计
  if (req.user!.role === 'BOSS') {
    createdById = req.user!.id;
  }
  // 管理员可以查看所有数据统计
  
  const stats = await statisticsService.getOverviewStats(createdById);
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取统计数据成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取收益分析数据
export const getRevenueAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { period = '30d' } = req.query;
  let createdById: string | undefined;
  
  if (req.user!.role === 'BOSS') {
    createdById = req.user!.id;
  }
  
  const analysis = await statisticsService.getRevenueAnalysis(period as string, createdById);
  
  const response: ApiResponse = {
    success: true,
    data: analysis,
    message: '获取收益分析成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取员工绩效数据
export const getEmployeePerformance = asyncHandler(async (req: Request, res: Response) => {
  let createdById: string | undefined;
  
  if (req.user!.role === 'BOSS') {
    createdById = req.user!.id;
  }
  
  const performance = await statisticsService.getEmployeePerformance(createdById);
  
  const response: ApiResponse = {
    success: true,
    data: performance,
    message: '获取员工绩效成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取订单趋势数据
export const getOrderTrends = asyncHandler(async (req: Request, res: Response) => {
  const { period = '30d' } = req.query;
  let createdById: string | undefined;
  
  if (req.user!.role === 'BOSS') {
    createdById = req.user!.id;
  }
  
  const trends = await statisticsService.getOrderTrends(period as string, createdById);
  
  const response: ApiResponse = {
    success: true,
    data: trends,
    message: '获取订单趋势成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});
