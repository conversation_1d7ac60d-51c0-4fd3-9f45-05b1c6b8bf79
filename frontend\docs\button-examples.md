# 按钮组件使用示例

## 📋 修复前后对比

### 修复前（错误）
```vue
<!-- ❌ Element Plus 2.4.4 不支持 type="link" -->
<el-button type="link" @click="refreshOnlineStats">
  <el-icon><Refresh /></el-icon>
</el-button>
```

### 修复后（正确）
```vue
<!-- ✅ 使用支持的 type="text" 并添加统一样式类 -->
<el-button type="text" @click="refreshOnlineStats" class="refresh-button">
  <el-icon><Refresh /></el-icon>
</el-button>
```

## 🎨 统一样式效果

### 刷新按钮样式
```scss
.refresh-button {
  padding: 4px;
  color: #606266;
  border: none;
  background: transparent;
  
  &:hover {
    color: #409EFF;
    background-color: #f0f9ff;
  }
  
  .el-icon {
    font-size: 16px;
  }
}
```

## 📝 实际应用示例

### 1. 监控仪表板刷新按钮
```vue
<template>
  <el-card class="chart-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">在线用户角色分布</span>
        <el-button type="text" @click="refreshOnlineStats" class="refresh-button">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    <div class="chart-container">
      <div ref="roleChartRef" class="chart"></div>
    </div>
  </el-card>
</template>
```

### 2. 表格操作按钮
```vue
<el-table-column label="操作" width="200" fixed="right">
  <template #default="{ row }">
    <el-button type="text" size="small" @click="handleView(row)" class="table-action-btn">
      查看
    </el-button>
    <el-button type="text" size="small" @click="handleEdit(row)" class="table-action-btn">
      编辑
    </el-button>
    <el-button type="text" size="small" type="danger" @click="handleDelete(row)" class="table-action-btn">
      删除
    </el-button>
  </template>
</el-table-column>
```

### 3. 对话框按钮
```vue
<template #footer>
  <div class="dialog-footer">
    <el-button @click="handleClose" class="dialog-btn">取消</el-button>
    <el-button type="primary" @click="handleConfirm" :loading="loading" class="dialog-btn">
      确认
    </el-button>
  </div>
</template>
```

### 4. 页面头部按钮
```vue
<div class="page-header">
  <div class="header-content">
    <h1 class="page-title">
      <el-icon><Monitor /></el-icon>
      监控仪表板
    </h1>
    <p class="page-description">实时监控系统状态和用户活动</p>
  </div>
  <div class="header-actions">
    <el-button type="default" :icon="Refresh" @click="refreshAllData" class="header-btn">
      刷新数据
    </el-button>
    <el-button type="primary" :icon="Setting" @click="openSettings" class="header-btn">
      设置
    </el-button>
  </div>
</div>
```

### 5. 工具栏按钮
```vue
<div class="toolbar">
  <div class="toolbar-left">
    <el-select v-model="selectedPeriod" @change="handlePeriodChange">
      <el-option label="近7天" value="7" />
      <el-option label="近30天" value="30" />
      <el-option label="近90天" value="90" />
    </el-select>
  </div>
  <div class="toolbar-right">
    <el-button type="default" :icon="Download" size="small">
      导出数据
    </el-button>
    <el-button type="text" :icon="Refresh" @click="handleRefresh" class="refresh-button">
      刷新
    </el-button>
  </div>
</div>
```

## 🔧 常用按钮组合

### 确认对话框
```vue
<el-message-box
  title="确认删除"
  message="此操作将永久删除该记录，是否继续？"
  type="warning"
  show-cancel-button
  confirm-button-text="确定删除"
  cancel-button-text="取消"
  confirm-button-type="danger"
/>
```

### 加载状态按钮
```vue
<el-button 
  type="primary" 
  :loading="submitLoading"
  @click="handleSubmit"
  class="loading-button"
>
  {{ submitLoading ? '提交中...' : '提交' }}
</el-button>
```

### 权限控制按钮
```vue
<el-button 
  type="danger" 
  v-permission="['ADMIN', 'BOSS']"
  @click="handleDelete"
  class="dialog-btn"
>
  删除
</el-button>
```

### 响应式按钮
```vue
<el-button 
  :type="isMobile ? 'text' : 'primary'"
  :size="isMobile ? 'small' : 'default'"
  @click="handleAction"
>
  操作
</el-button>
```

## 📱 移动端适配

### 响应式按钮组
```vue
<div class="button-group" :class="{ 'mobile-buttons': isMobile }">
  <el-button type="primary">主要操作</el-button>
  <el-button type="default">次要操作</el-button>
</div>

<style lang="scss" scoped>
.button-group {
  display: flex;
  gap: 12px;
  
  &.mobile-buttons {
    flex-direction: column;
    gap: 8px;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
```

## ⚠️ 注意事项

1. **版本兼容性**: 确保使用 Element Plus 2.4.4 支持的按钮类型
2. **样式一致性**: 相同功能的按钮使用相同的样式类
3. **可访问性**: 为图标按钮添加适当的 aria-label
4. **加载状态**: 异步操作必须显示加载状态
5. **权限控制**: 敏感操作添加权限验证

## 🎯 最佳实践总结

- ✅ 使用 `type="text"` 替代不支持的 `type="link"`
- ✅ 为按钮添加统一的样式类名
- ✅ 保持按钮尺寸和间距的一致性
- ✅ 合理使用按钮类型表达操作的重要性
- ✅ 在移动端提供良好的触摸体验
