import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElNotification } from 'element-plus'

export interface Notification {
  id: string
  title: string
  message: string
  type: 'success' | 'warning' | 'info' | 'error'
  duration?: number
  timestamp: Date
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const globalError = ref<string | null>(null)
  const notifications = ref<Notification[]>([])
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark'>('light')

  // 计算属性
  const hasError = computed(() => !!globalError.value)
  const hasNotifications = computed(() => notifications.value.length > 0)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.timestamp)
  )

  // 设置加载状态
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  // 设置全局错误
  const setError = (error: string | null) => {
    globalError.value = error
  }

  // 清除错误
  const clearError = () => {
    globalError.value = null
  }

  // 显示通知
  const showNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = Date.now().toString()
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date()
    }
    
    notifications.value.unshift(newNotification)
    
    // 使用Element Plus的通知组件
    ElNotification({
      title: notification.title,
      message: notification.message,
      type: notification.type,
      duration: notification.duration || 4500,
      position: 'top-right'
    })
    
    // 自动清除通知（保留在列表中一段时间）
    setTimeout(() => {
      removeNotification(id)
    }, 30000) // 30秒后从列表中移除
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearNotifications = () => {
    notifications.value = []
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    // 保存到本地存储
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme', theme.value)
    applyTheme()
  }

  // 设置主题
  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    document.documentElement.setAttribute('data-theme', theme.value)
    if (theme.value === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 初始化应用设置
  const initializeApp = () => {
    // 恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarState !== null) {
      sidebarCollapsed.value = savedSidebarState === 'true'
    }
    
    // 恢复主题设置
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (savedTheme) {
      theme.value = savedTheme
    }
    applyTheme()
  }

  // 显示成功消息
  const showSuccess = (title: string, message: string = '') => {
    showNotification({
      title,
      message,
      type: 'success'
    })
  }

  // 显示错误消息
  const showError = (title: string, message: string = '') => {
    showNotification({
      title,
      message,
      type: 'error',
      duration: 6000 // 错误消息显示更长时间
    })
  }

  // 显示警告消息
  const showWarning = (title: string, message: string = '') => {
    showNotification({
      title,
      message,
      type: 'warning'
    })
  }

  // 显示信息消息
  const showInfo = (title: string, message: string = '') => {
    showNotification({
      title,
      message,
      type: 'info'
    })
  }

  // 处理API错误
  const handleApiError = (error: any) => {
    const message = error?.response?.data?.error?.message || error?.message || '操作失败'
    showError('错误', message)
    setError(message)
  }

  // 初始化应用
  initializeApp()

  return {
    // 状态
    loading,
    globalError,
    notifications,
    sidebarCollapsed,
    theme,
    
    // 计算属性
    hasError,
    hasNotifications,
    unreadNotifications,
    
    // 方法
    setLoading,
    setError,
    clearError,
    showNotification,
    removeNotification,
    clearNotifications,
    toggleSidebar,
    setSidebarCollapsed,
    toggleTheme,
    setTheme,
    initializeApp,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    handleApiError
  }
})
