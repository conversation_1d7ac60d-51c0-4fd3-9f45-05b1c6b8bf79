import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server } from 'socket.io';
import rateLimit from 'express-rate-limit';

import { config } from './config/env';
import { connectDatabase } from './config/database';
import { setupLogger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

// 导入路由
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import orderRoutes from './routes/orders';


import taskRoutes from './routes/tasks';
import uploadRoutes from './routes/upload';
import healthRoutes from './routes/health';
import statisticsRoutes from './routes/statistics';
import settlementRoutes from './routes/settlements';
import notificationRoutes from './routes/notifications';
import onlineUserRoutes from './routes/onlineUserRoutes';
import gameRoutes from './routes/games';
import employeeSkillRoutes from './routes/employeeSkills';
import gameFormFieldRoutes from './routes/gameFormFields';
import excelExportRoutes from './routes/excelExport';

// 导入Socket服务
import { initializeSocketService } from './services/socketService';
import { initializeSchedulerService } from './services/schedulerService';

// 初始化日志
const logger = setupLogger();

// 创建Express应用
const app = express();
const server = createServer(app);

// 创建Socket.IO实例
const io = new Server(server, {
  cors: {
    origin: config.SOCKET_CORS_ORIGIN,
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// 中间件配置
app.use(helmet({
  crossOriginResourcePolicy: { policy: 'cross-origin' },
}));

app.use(cors({
  origin: config.CORS_ORIGIN,
  credentials: true,
}));

app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim()),
  },
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(config.UPLOAD_PATH));

// 速率限制 - 调整为更宽松的限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 500, // 增加到500个请求，适应正常使用场景
  message: {
    success: false,
    error: {
      message: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true, // 返回速率限制信息在 `RateLimit-*` headers
  legacyHeaders: false, // 禁用 `X-RateLimit-*` headers
  skip: (req) => {
    // 跳过健康检查和静态文件的速率限制
    return req.path === '/health' || req.path.startsWith('/uploads/')
  }
});

// 为登录接口设置更严格的速率限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 登录接口限制更严格
  message: {
    success: false,
    error: {
      message: '登录尝试过于频繁，请15分钟后再试',
      code: 'LOGIN_RATE_LIMIT_EXCEEDED'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// API路由 - 为登录接口应用特殊的速率限制
app.use(config.API_PREFIX + '/auth', loginLimiter, authRoutes);
app.use(config.API_PREFIX + '/users', userRoutes);
app.use(config.API_PREFIX + '/orders', orderRoutes);


app.use(config.API_PREFIX + '/tasks', taskRoutes);
app.use(config.API_PREFIX + '/upload', uploadRoutes);
app.use(config.API_PREFIX + '/health', healthRoutes);
app.use(config.API_PREFIX + '/statistics', statisticsRoutes);
app.use(config.API_PREFIX + '/settlements', settlementRoutes);
app.use(config.API_PREFIX + '/notifications', notificationRoutes);
app.use(config.API_PREFIX + '/monitoring', onlineUserRoutes);
app.use(config.API_PREFIX + '/games', gameRoutes);
app.use(config.API_PREFIX + '/employee-skills', employeeSkillRoutes);
app.use(config.API_PREFIX + '/game-form-fields', gameFormFieldRoutes);
app.use(config.API_PREFIX + '/excel', excelExportRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '王者荣耀代练任务分发管理系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// 初始化Socket服务
const socketService = initializeSocketService(io);

// 将Socket.IO实例和服务添加到app中，供其他模块使用
app.set('io', io);
app.set('socketService', socketService);

// 错误处理中间件
app.use(notFoundHandler);
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    const dbConnected = await connectDatabase();
    if (!dbConnected) {
      logger.error('数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 初始化定时任务服务
    initializeSchedulerService();
    logger.info('定时任务服务已初始化');

    // 添加服务器错误处理
    server.on('error', (error) => {
      logger.error('服务器错误:', error);
      process.exit(1);
    });

    // 启动HTTP服务器
    server.listen(config.PORT, config.HOST, () => {
      logger.info(`🚀 服务器启动成功`);
      logger.info(`   地址: http://${config.HOST}:${config.PORT}`);
      logger.info(`   环境: ${config.NODE_ENV}`);
      logger.info(`   API前缀: ${config.API_PREFIX}`);
      logger.info(`   Socket.IO: 已启用`);
      logger.info(`   定时任务: 已启用`);
    });

  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

// 启动服务器
startServer();

export { app, io };
