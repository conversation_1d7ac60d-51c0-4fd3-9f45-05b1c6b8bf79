# 前端UI设计规范指南

## 📋 目录
1. [按钮组件规范](#按钮组件规范)
2. [颜色系统](#颜色系统)
3. [尺寸规范](#尺寸规范)
4. [间距系统](#间距系统)
5. [组件使用最佳实践](#组件使用最佳实践)

## 🔘 按钮组件规范

### Element Plus 2.4.4 支持的按钮类型

```typescript
// 支持的按钮类型
type ButtonType = 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' | ''

// 支持的按钮尺寸
type ButtonSize = 'large' | 'default' | 'small'
```

### 按钮使用场景

#### 1. 主要操作按钮
```vue
<!-- 主要操作：提交、确认、保存等 -->
<el-button type="primary" size="default">
  确认提交
</el-button>

<!-- 登录按钮 -->
<el-button type="primary" size="large" :loading="loading">
  {{ loading ? '登录中...' : '登录' }}
</el-button>
```

#### 2. 次要操作按钮
```vue
<!-- 取消、关闭等次要操作 -->
<el-button type="default">取消</el-button>
<el-button>关闭</el-button>
```

#### 3. 危险操作按钮
```vue
<!-- 删除、强制下线等危险操作 -->
<el-button type="danger" size="small">删除</el-button>
<el-button type="danger">强制下线</el-button>
```

#### 4. 文本按钮（链接样式）
```vue
<!-- 刷新、编辑等轻量操作 -->
<el-button type="text" class="refresh-button">
  <el-icon><Refresh /></el-icon>
</el-button>

<!-- 表格操作按钮 -->
<el-button type="text" size="small">编辑</el-button>
<el-button type="text" size="small">查看详情</el-button>
```

#### 5. 状态按钮
```vue
<!-- 成功状态 -->
<el-button type="success">已完成</el-button>

<!-- 警告状态 -->
<el-button type="warning">待审核</el-button>

<!-- 信息状态 -->
<el-button type="info">查看信息</el-button>
```

### 按钮组合规范

#### 对话框底部按钮
```vue
<template #footer>
  <div class="dialog-footer">
    <el-button @click="handleClose">取消</el-button>
    <el-button type="primary" @click="handleConfirm" :loading="loading">
      确认
    </el-button>
  </div>
</template>
```

#### 表格操作按钮
```vue
<el-table-column label="操作" width="200" fixed="right">
  <template #default="{ row }">
    <el-button type="text" size="small" @click="handleView(row)">
      查看
    </el-button>
    <el-button type="text" size="small" @click="handleEdit(row)">
      编辑
    </el-button>
    <el-button type="text" size="small" type="danger" @click="handleDelete(row)">
      删除
    </el-button>
  </template>
</el-table-column>
```

#### 页面头部操作按钮
```vue
<div class="page-header">
  <div class="header-content">
    <h1 class="page-title">页面标题</h1>
  </div>
  <div class="header-actions">
    <el-button type="default" :icon="Refresh" @click="handleRefresh">
      刷新
    </el-button>
    <el-button type="primary" :icon="Plus" @click="handleCreate">
      新增
    </el-button>
  </div>
</div>
```

## 🎨 颜色系统

### 主题色彩
```scss
// 主色调
$primary-color: #409EFF;
$primary-light: #79bbff;
$primary-dark: #337ecc;

// 功能色彩
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// 边框颜色
$border-base: #DCDFE6;
$border-light: #E4E7ED;
$border-lighter: #EBEEF5;
$border-extra-light: #F2F6FC;

// 背景颜色
$bg-color: #FFFFFF;
$bg-page: #F2F3F5;
$bg-overlay: rgba(0, 0, 0, 0.8);
```

### 按钮颜色使用指南
- **Primary (蓝色)**: 主要操作、确认操作
- **Success (绿色)**: 成功状态、完成操作
- **Warning (橙色)**: 警告状态、需要注意的操作
- **Danger (红色)**: 危险操作、删除操作
- **Info (灰色)**: 信息展示、中性操作
- **Text**: 轻量操作、链接样式

## 📏 尺寸规范

### 按钮尺寸
```scss
// 按钮尺寸
$button-large-height: 40px;
$button-default-height: 32px;
$button-small-height: 24px;

// 按钮内边距
$button-large-padding: 12px 19px;
$button-default-padding: 8px 15px;
$button-small-padding: 5px 11px;
```

### 使用场景
- **Large**: 登录页面、重要的主操作按钮
- **Default**: 常规页面的主要操作按钮
- **Small**: 表格操作、卡片操作、次要操作

## 📐 间距系统

### 标准间距
```scss
$spacing-xs: 4px;   // 极小间距
$spacing-sm: 8px;   // 小间距
$spacing-md: 12px;  // 中等间距
$spacing-lg: 16px;  // 大间距
$spacing-xl: 20px;  // 超大间距
$spacing-xxl: 24px; // 极大间距
```

### 按钮间距应用
```vue
<!-- 按钮组间距 -->
<div class="button-group">
  <el-button type="primary">确认</el-button>
  <el-button style="margin-left: 12px;">取消</el-button>
</div>

<!-- 或使用CSS类 -->
<div class="button-group">
  <el-button type="primary">确认</el-button>
  <el-button class="ml-md">取消</el-button>
</div>
```

## 🛠 组件使用最佳实践

### 1. 加载状态
```vue
<el-button type="primary" :loading="loading" @click="handleSubmit">
  {{ loading ? '提交中...' : '提交' }}
</el-button>
```

### 2. 禁用状态
```vue
<el-button type="primary" :disabled="!isValid">
  提交
</el-button>
```

### 3. 图标按钮
```vue
<!-- 带图标的按钮 -->
<el-button type="primary" :icon="Plus">
  新增
</el-button>

<!-- 纯图标按钮 -->
<el-button type="text" circle :icon="Refresh" @click="handleRefresh" />
```

### 4. 权限控制
```vue
<el-button 
  type="danger" 
  v-permission="['ADMIN', 'BOSS']"
  @click="handleDelete"
>
  删除
</el-button>
```

### 5. 响应式设计
```vue
<el-button 
  :size="isMobile ? 'small' : 'default'"
  type="primary"
>
  操作
</el-button>
```

## 📝 代码示例

### 完整的按钮组件示例
```vue
<template>
  <div class="action-buttons">
    <!-- 主要操作 -->
    <el-button 
      type="primary" 
      :loading="loading"
      :disabled="!canSubmit"
      @click="handleSubmit"
    >
      {{ loading ? '提交中...' : '提交' }}
    </el-button>
    
    <!-- 次要操作 -->
    <el-button @click="handleCancel">
      取消
    </el-button>
    
    <!-- 危险操作 -->
    <el-button 
      type="danger" 
      v-permission="['ADMIN']"
      @click="handleDelete"
    >
      删除
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  
  .el-button {
    min-width: 80px;
  }
}
</style>
```

## ⚠️ 注意事项

1. **版本兼容性**: 当前使用 Element Plus 2.4.4，不支持 `type="link"`
2. **一致性**: 同类型操作使用相同的按钮样式
3. **可访问性**: 确保按钮有明确的文字说明或 aria-label
4. **响应式**: 在移动端适当调整按钮尺寸
5. **加载状态**: 异步操作必须显示加载状态
