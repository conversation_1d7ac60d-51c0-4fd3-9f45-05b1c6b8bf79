<template>
  <div class="dynamic-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      @submit.prevent
    >
      <!-- 按分组渲染字段 -->
      <div
        v-for="group in groupedFields"
        :key="group.name"
        class="form-group"
      >
        <!-- 分组标题 -->
        <div v-if="group.label" class="group-title">
          <el-divider content-position="left">
            <span class="group-label">{{ group.label }}</span>
          </el-divider>
        </div>

        <!-- 分组字段 -->
        <el-row :gutter="16">
          <el-col
            v-for="field in group.fields"
            :key="field.id"
            :span="getFieldSpan(group.columns)"
            class="form-field-col"
          >
            <el-form-item
              :label="field.label"
              :prop="field.name"
              :required="field.required"
              class="dynamic-form-item"
            >
              <DynamicFormField
                v-model="formData[field.name]"
                :field="field"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表单操作按钮 -->
      <div v-if="showActions" class="form-actions">
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="disabled"
            @click="handleSubmit"
          >
            {{ submitText }}
          </el-button>
          <el-button
            v-if="showReset"
            :disabled="disabled"
            @click="handleReset"
          >
            重置
          </el-button>
          <el-button
            v-if="showCancel"
            :disabled="disabled"
            @click="handleCancel"
          >
            取消
          </el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import DynamicFormField from './DynamicFormField.vue'

interface FieldOption {
  value: string | number
  label: string
  disabled?: boolean
  children?: FieldOption[]
}

interface CascaderOption {
  value: string
  label: string
  children?: CascaderOption[]
}

interface OrderField {
  id: string
  name: string
  label: string
  type: string
  required: boolean
  defaultValue?: any
  config?: {
    options?: FieldOption[]
    min?: number
    max?: number
    step?: number
    precision?: number
    minLength?: number
    maxLength?: number
    pattern?: string
    accept?: string[]
    maxSize?: number
    maxCount?: number
    cascaderOptions?: CascaderOption[]
    component?: string
    props?: Record<string, any>
  }
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    custom?: string
    message?: string
  }
  display: {
    order: number
    group?: string
    helpText?: string
    placeholder?: string
  }
}

interface FormGroup {
  name: string
  label: string
  columns: number
  collapsed?: boolean
}

interface DisplayConfig {
  createForm: {
    groups: FormGroup[]
  }
}

interface OrderTemplate {
  id: string
  name: string
  gameType: string
  fields: OrderField[]
  displayConfig: DisplayConfig
}

interface Props {
  template: OrderTemplate
  modelValue?: Record<string, any>
  disabled?: boolean
  loading?: boolean
  showActions?: boolean
  showReset?: boolean
  showCancel?: boolean
  submitText?: string
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'submit', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  loading: false,
  showActions: true,
  showReset: true,
  showCancel: false,
  submitText: '提交'
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = ref<Record<string, any>>({})

// 计算表单验证规则
const formRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  props.template.fields.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: ['blur', 'change']
      })
    }
    
    // 长度验证
    if (field.config?.minLength || field.config?.maxLength) {
      fieldRules.push({
        min: field.config.minLength,
        max: field.config.maxLength,
        message: `${field.label}长度应在 ${field.config.minLength || 0} 到 ${field.config.maxLength || '∞'} 个字符之间`,
        trigger: 'blur'
      })
    }
    
    // 正则验证
    if (field.config?.pattern) {
      fieldRules.push({
        pattern: new RegExp(field.config.pattern),
        message: field.validation?.message || `${field.label}格式不正确`,
        trigger: 'blur'
      })
    }
    
    // 数字范围验证
    if (field.type === 'number' && (field.config?.min !== undefined || field.config?.max !== undefined)) {
      fieldRules.push({
        type: 'number',
        min: field.config.min,
        max: field.config.max,
        message: `${field.label}应在 ${field.config.min || '-∞'} 到 ${field.config.max || '∞'} 之间`,
        trigger: 'blur'
      })
    }
    
    // 自定义验证
    if (field.validation?.custom) {
      fieldRules.push({
        validator: (rule: any, value: any, callback: Function) => {
          // 这里可以实现自定义验证逻辑
          // 例如：targetRankMustBeHigher
          if (field.validation?.custom === 'targetRankMustBeHigher') {
            const currentRank = formData.value.currentRank
            const targetRank = formData.value.targetRank
            if (currentRank && targetRank) {
              const rankLevels = {
                'bronze': 1, 'silver': 2, 'gold': 3, 
                'platinum': 4, 'diamond': 5, 'master': 6, 'king': 7
              }
              if (rankLevels[targetRank] <= rankLevels[currentRank]) {
                callback(new Error(field.validation?.message || '目标段位必须高于当前段位'))
                return
              }
            }
          }
          callback()
        },
        trigger: 'change'
      })
    }
    
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 按分组组织字段
const groupedFields = computed(() => {
  const groups = props.template.displayConfig.createForm.groups
  const fieldsMap = new Map(props.template.fields.map(f => [f.display.group || 'default', f]))
  
  return groups.map(group => ({
    ...group,
    fields: props.template.fields
      .filter(field => (field.display.group || 'default') === group.name)
      .sort((a, b) => a.display.order - b.display.order)
  })).filter(group => group.fields.length > 0)
})

// 计算字段占用的列数
const getFieldSpan = (columns: number): number => {
  return Math.floor(24 / columns)
}

// 初始化表单数据
const initFormData = () => {
  const data: Record<string, any> = {}
  
  props.template.fields.forEach(field => {
    if (field.defaultValue !== undefined) {
      data[field.name] = field.defaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
        case 'multi_select':
          data[field.name] = []
          break
        case 'number':
          data[field.name] = field.config?.min || 0
          break
        default:
          data[field.name] = ''
      }
    }
  })
  
  // 合并外部传入的数据
  if (props.modelValue) {
    Object.assign(data, props.modelValue)
  }
  
  formData.value = data
}

// 监听模板变化，重新初始化表单
watch(() => props.template, () => {
  initFormData()
}, { immediate: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(formData.value, newValue)
  }
}, { deep: true })

// 监听表单数据变化，向外发送
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData.value })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  initFormData()
  emit('reset')
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 暴露验证方法
const validate = () => {
  return formRef.value?.validate()
}

const validateField = (prop: string) => {
  return formRef.value?.validateField(prop)
}

const clearValidation = (props?: string | string[]) => {
  return formRef.value?.clearValidation(props)
}

defineExpose({
  validate,
  validateField,
  clearValidation,
  formData
})
</script>

<style scoped>
.dynamic-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.group-title {
  margin-bottom: 16px;
}

.group-label {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.form-field-col {
  margin-bottom: 16px;
}

.dynamic-form-item {
  margin-bottom: 0;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
  text-align: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  background-color: var(--el-bg-color);
}
</style>
