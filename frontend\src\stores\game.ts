import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { gameApi } from '@/api/game';
import type {
  Game,
  SimpleGame,
  CreateGameRequest,
  UpdateGameRequest,
  GameQuery,
  GameStatistics,
  GameHotness,
  PriceCalculationResult
} from '@/types/game';
import type { PaginatedResponse } from '@/types/common';

export const useGameStore = defineStore('game', () => {
  // 状态
  const games = ref<Game[]>([]);
  const activeGames = ref<SimpleGame[]>([]);
  const currentGame = ref<Game | null>(null);
  const gameStatistics = ref<GameStatistics | null>(null);
  const gameHotness = ref<GameHotness[]>([]);
  const priceCalculation = ref<PriceCalculationResult | null>(null);

  // 加载状态
  const loading = ref(false);
  const gamesLoading = ref(false);
  const statisticsLoading = ref(false);

  // 分页信息
  const gamesPagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  // 计算属性
  const gameOptions = computed(() => {
    // 确保activeGames.value是数组
    if (!Array.isArray(activeGames.value)) {
      return [];
    }
    return activeGames.value.map(game => ({
      value: game.id,
      label: game.displayName,
      icon: game.icon
    }));
  });



  // 获取活跃游戏列表
  const fetchActiveGames = async () => {
    try {
      loading.value = true;
      const response = await gameApi.getActiveGames();
      // 从API响应中提取data字段，确保是数组
      const gameList = Array.isArray(response?.data) ? response.data : [];
      activeGames.value = gameList;
      return gameList;
    } catch (error) {
      console.error('获取活跃游戏列表失败:', error);
      // 发生错误时设置为空数组，避免map函数报错
      activeGames.value = [];
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取游戏列表
  const fetchGames = async (query?: GameQuery) => {
    try {
      gamesLoading.value = true;
      const response = await gameApi.getGames(query);
      console.log('🎮 Store收到的响应:', response);

      // 处理不同的响应格式
      const data = response.data || response;
      games.value = data.items || [];
      gamesPagination.value = data.pagination || { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false };

      console.log('🎮 Store设置的游戏数据:', games.value);
      console.log('🎮 Store设置的分页数据:', gamesPagination.value);

      return response;
    } catch (error) {
      console.error('获取游戏列表失败:', error);
      throw error;
    } finally {
      gamesLoading.value = false;
    }
  };

  // 获取游戏详情
  const fetchGameById = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameApi.getGameById(id);
      currentGame.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 创建游戏
  const createGame = async (data: CreateGameRequest) => {
    try {
      loading.value = true;
      console.log('🎮 Store收到创建游戏请求:', data);
      const response = await gameApi.createGame(data);
      console.log('🎮 Store收到创建游戏响应:', response);
      // 重新获取游戏列表
      await fetchGames();
      await fetchActiveGames();
      return response;
    } catch (error) {
      console.error('❌ Store创建游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 更新游戏
  const updateGame = async (id: string, data: UpdateGameRequest) => {
    try {
      loading.value = true;
      const response = await gameApi.updateGame(id, data);
      // 更新本地数据
      const index = games.value.findIndex(game => game.id === id);
      if (index !== -1) {
        games.value[index] = response;
      }
      if (currentGame.value?.id === id) {
        currentGame.value = response;
      }
      // 重新获取活跃游戏列表
      await fetchActiveGames();
      return response;
    } catch (error) {
      console.error('更新游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 删除游戏
  const deleteGame = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameApi.deleteGame(id);
      // 从本地数据中移除
      games.value = games.value.filter(game => game.id !== id);
      activeGames.value = activeGames.value.filter(game => game.id !== id);
      if (currentGame.value?.id === id) {
        currentGame.value = null;
      }
      return response;
    } catch (error) {
      console.error('删除游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取游戏统计信息
  const fetchGameStatistics = async (gameId: string) => {
    try {
      statisticsLoading.value = true;
      const response = await gameApi.getGameStatistics(gameId);
      gameStatistics.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏统计信息失败:', error);
      throw error;
    } finally {
      statisticsLoading.value = false;
    }
  };

  // 获取游戏热度分析
  const fetchGameHotness = async () => {
    try {
      loading.value = true;
      const response = await gameApi.getGameHotness();
      gameHotness.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏热度分析失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 计算订单价格
  const calculateOrderPrice = async (data: {
    gameId: string;
    currentRankId: string;
    targetRankId: string;
    priority?: string;
  }) => {
    try {
      loading.value = true;
      const response = await gameApi.calculateOrderPrice(data);
      priceCalculation.value = response;
      return response;
    } catch (error) {
      console.error('计算订单价格失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 清空当前游戏段位
  const clearCurrentGameRanks = () => {
    currentGameRanks.value = [];
  };

  // 重置状态
  const resetState = () => {
    games.value = [];
    activeGames.value = [];
    currentGame.value = null;
    gameStatistics.value = null;
    gameHotness.value = [];
    priceCalculation.value = null;
    loading.value = false;
    gamesLoading.value = false;
    statisticsLoading.value = false;
  };

  return {
    // 状态
    games,
    activeGames,
    currentGame,
    gameStatistics,
    gameHotness,
    priceCalculation,
    loading,
    gamesLoading,
    statisticsLoading,
    gamesPagination,

    // 计算属性
    gameOptions,

    // 方法
    fetchActiveGames,
    fetchGames,
    fetchGameById,
    createGame,
    updateGame,
    deleteGame,
    fetchGameStatistics,
    fetchGameHotness,
    calculateOrderPrice,
    resetState
  };
});
