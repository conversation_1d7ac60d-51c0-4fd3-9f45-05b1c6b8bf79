import { AssignType, TaskStatus, OrderStatus, UserRole } from '@prisma/client';
import { prisma } from '../config/database';
import { NotFoundError, AuthorizationError, ConflictError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';
import { CreateTaskRequest, UpdateTaskRequest, UpdateTaskProgressRequest, ReviewTaskRequest } from '../types/task';
import { CommissionService } from './commissionService';

export class TaskService {
  // 生成任务编号
  private generateTaskNo(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `TASK${timestamp.slice(-6)}${random}`;
  }

  // 创建任务
  async createTask(taskData: CreateTaskRequest) {
    const { orderId, assigneeId, assignType, estimatedHours, commission, commissionType, commissionParams, description, notes } = taskData;
    
    // 检查订单是否存在
    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new NotFoundError('订单不存在');
    }

    // 检查订单状态
    if (order.status !== OrderStatus.PENDING) {
      throw new ConflictError('只能为待处理的订单创建任务');
    }

    // 如果是直接分配，检查员工是否存在
    if (assignType === AssignType.DIRECT && assigneeId) {
      const employee = await prisma.user.findUnique({
        where: { id: assigneeId, role: UserRole.EMPLOYEE },
      });

      if (!employee) {
        throw new NotFoundError('指定的员工不存在');
      }
    }

    const taskNo = this.generateTaskNo();

    // 处理佣金计算
    let finalCommission = commission;
    let finalCommissionParams = commissionParams;

    // TODO: 等待Prisma客户端生成后启用佣金计算功能
    // if (commissionType === 'AUTO') {
    //   // 自动计算佣金
    //   const calculationParams = {
    //     orderPrice: order.price,
    //     estimatedHours,
    //     currentRank: order.currentRank,
    //     targetRank: order.targetRank,
    //     priorityMultiplier: CommissionService.getPriorityMultiplier(order.priority),
    //     ...commissionParams
    //   };
    //
    //   const result = CommissionService.calculateCommission(calculationParams);
    //   finalCommission = result.commission;
    //   finalCommissionParams = calculationParams;
    // }

    // 创建任务
    const task = await prisma.task.create({
      data: {
        taskNo,
        orderId,
        assigneeId: assignType === AssignType.DIRECT ? assigneeId : null,
        assignType,
        status: assignType === AssignType.DIRECT ? TaskStatus.ACCEPTED : TaskStatus.PENDING,
        startTime: assignType === AssignType.DIRECT ? new Date() : null,
        estimatedHours,
        commission: finalCommission,
        // TODO: 等待数据库迁移后启用
        // commissionType,
        // commissionParams: finalCommissionParams,
        description,
        notes,
      },
      include: {
        order: {
          select: {
            id: true,
            orderNo: true,
            customerName: true,
            gameType: true,
            gameAccount: true,
            gamePassword: true,
            price: true,
            priority: true,
            deadline: true,
          },
        },
        assignee: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true,
          },
        },
      },
    });

    // 更新订单状态
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: assignType === AssignType.DIRECT ? OrderStatus.ASSIGNED : OrderStatus.ASSIGNED,
      },
    });

    return task;
  }

  // 获取任务列表
  async getTasks(query: PaginationQuery & {
    status?: TaskStatus;
    assignType?: AssignType;
    assigneeId?: string;
    orderId?: string;
    keyword?: string;
  }) {
    const { 
      page = 1, 
      limit = 10, 
      sortBy = 'createdAt', 
      sortOrder = 'desc',
      status,
      assignType,
      assigneeId,
      orderId,
      keyword
    } = query;
    
    const skip = (page - 1) * limit;
    
    // 构建查询条件
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (assignType) {
      where.assignType = assignType;
    }
    
    if (assigneeId) {
      where.assigneeId = assigneeId;
    }
    
    if (orderId) {
      where.orderId = orderId;
    }
    
    if (keyword) {
      where.OR = [
        { taskNo: { contains: keyword } },
        { description: { contains: keyword } },
        { order: { orderNo: { contains: keyword } } },
        { order: { customerName: { contains: keyword } } },
      ];
    }
    
    // 查询任务
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          order: {
            select: {
              id: true,
              orderNo: true,
              customerName: true,
              gameType: true,
              gameAccount: true,
              gamePassword: true,
              price: true,
              priority: true,
              deadline: true,
            },
          },
          assignee: {
            select: {
              id: true,
              username: true,
              nickname: true,
              level: true,
            },
          },
          progress: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
            select: {
              progress: true,
              createdAt: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.task.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / limit);

    // 格式化任务数据中的screenshots字段
    const formattedTasks = tasks.map(task => ({
      ...task,
      progress: task.progress?.map((p: any) => ({
        ...p,
        screenshots: p.screenshots ? JSON.parse(p.screenshots) : []
      })) || []
    }));

    const result: PaginatedResponse<typeof tasks[0]> = {
      items: formattedTasks,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    return result;
  }

  // 获取可接单的任务列表（系统挂单）
  async getAvailableTasks(query: PaginationQuery) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const where = {
      assignType: AssignType.SYSTEM,
      status: TaskStatus.PENDING,
    };

    // 使用数据库排序：URGENT > HIGH > NORMAL > LOW

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          order: {
            select: {
              id: true,
              orderNo: true,
              customerName: true,
              gameType: true,
              price: true,
              priority: true,
              deadline: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: [
          // 首先按优先级排序（通过关联的order表）
          // Prisma会按枚举定义顺序排序：LOW < NORMAL < HIGH < URGENT
          // 使用desc让URGENT排在最前面
          {
            order: {
              priority: 'desc',
            },
          },
          // 相同优先级内按创建时间倒序排列（最新的在前）
          {
            createdAt: 'desc',
          },
        ],
      }),
      prisma.task.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / limit);

    // 格式化任务数据中的screenshots字段（可接单任务通常没有进度记录）
    const formattedTasks = tasks.map(task => ({
      ...task,
      progress: []
    }));

    const result: PaginatedResponse<typeof tasks[0]> = {
      items: formattedTasks,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    return result;
  }

  // 接单（员工抢单）
  async acceptTask(taskId: string, employeeId: string) {
    // 检查任务是否存在
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        order: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 检查任务状态
    if (task.status !== TaskStatus.PENDING) {
      throw new ConflictError('任务已被接受或不可接受');
    }

    // 检查任务类型
    if (task.assignType !== AssignType.SYSTEM) {
      throw new ConflictError('该任务不支持抢单');
    }

    // 检查员工是否存在
    const employee = await prisma.user.findUnique({
      where: { id: employeeId, role: UserRole.EMPLOYEE },
    });

    if (!employee) {
      throw new NotFoundError('员工不存在');
    }

    // 更新任务状态
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assigneeId: employeeId,
        status: TaskStatus.ACCEPTED,
        startTime: new Date(),
      },
      include: {
        order: {
          select: {
            id: true,
            orderNo: true,
            customerName: true,
            createdById: true,
          },
        },
        assignee: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true,
          },
        },
      },
    });

    return updatedTask;
  }

  // 更新任务进度
  async updateTaskProgress(progressData: UpdateTaskProgressRequest, userId: string) {
    const { taskId, progress, description, screenshots } = progressData;
    
    // 检查任务是否存在
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: {
        id: true,
        assigneeId: true,
        status: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 检查权限
    if (task.assigneeId !== userId) {
      throw new AuthorizationError('只能更新自己的任务进度');
    }

    // 检查任务状态
    if (task.status !== TaskStatus.ACCEPTED && task.status !== TaskStatus.IN_PROGRESS) {
      throw new ConflictError('任务状态不允许更新进度');
    }

    // 创建进度记录
    const progressRecord = await prisma.taskProgress.create({
      data: {
        taskId,
        userId,
        progress,
        description,
        screenshots: screenshots ? JSON.stringify(screenshots) : null,
      },
    });

    // 如果进度达到100%，更新任务状态为已提交
    if (progress >= 100) {
      // 获取任务的开始时间以计算工时
      const taskForHours = await prisma.task.findUnique({
        where: { id: taskId },
        select: { startTime: true }
      });

      const updateData: any = {
        status: TaskStatus.SUBMITTED,
        endTime: new Date(),
      };

      // 计算实际工时
      if (taskForHours?.startTime) {
        updateData.actualHours = this.calculateActualHours(taskForHours.startTime, new Date());
      }

      await prisma.task.update({
        where: { id: taskId },
        data: updateData,
      });
    } else if (task.status === TaskStatus.ACCEPTED) {
      // 首次更新进度时，将状态改为进行中，并设置开始时间
      await prisma.task.update({
        where: { id: taskId },
        data: {
          status: TaskStatus.IN_PROGRESS,
          startTime: new Date(),
        },
      });
    }

    return progressRecord;
  }

  // 审核任务
  async reviewTask(reviewData: ReviewTaskRequest, reviewerId: string) {
    const { taskId, approved, feedback } = reviewData;
    
    // 检查任务是否存在
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignee: true,
        order: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 检查任务状态
    if (task.status !== TaskStatus.SUBMITTED) {
      throw new ConflictError('只能审核已提交的任务');
    }

    const newStatus = approved ? TaskStatus.APPROVED : TaskStatus.REJECTED;

    // 准备更新数据
    const updateData: any = {
      status: newStatus,
      notes: feedback,
    };

    // 如果审核通过且没有实际工时，尝试计算工时
    if (approved && !task.actualHours && task.startTime && task.endTime) {
      updateData.actualHours = this.calculateActualHours(task.startTime, task.endTime);
    }

    // 更新任务状态
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: updateData,
    });

    // 如果审核通过，创建结算记录
    if (approved && task.commission && task.assignee) {
      await prisma.settlement.create({
        data: {
          taskId,
          userId: task.assignee.id,
          amount: task.commission,
          status: 'PENDING',
        },
      });

      // 更新订单状态为已完成
      await prisma.order.update({
        where: { id: task.orderId },
        data: {
          status: OrderStatus.COMPLETED,
        },
      });
    }

    return updatedTask;
  }

  // 获取任务详情
  async getTaskById(id: string) {
    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            id: true,
            orderNo: true,
            customerName: true,
            customerContact: true,
            gameAccount: true,
            gamePassword: true,
            gameType: true,
            price: true,
            priority: true,
            deadline: true,
            requirements: true,
          },
        },
        assignee: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true,
          },
        },
        progress: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                nickname: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        settlement: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 格式化进度数据中的screenshots字段
    const formattedTask = {
      ...task,
      progress: task.progress.map((p: any) => ({
        ...p,
        screenshots: p.screenshots ? JSON.parse(p.screenshots) : []
      }))
    };

    return formattedTask;
  }

  // 更新任务信息
  async updateTask(id: string, updateData: UpdateTaskRequest) {
    // 检查任务是否存在
    const existingTask = await prisma.task.findUnique({
      where: { id },
    });

    if (!existingTask) {
      throw new NotFoundError('任务不存在');
    }

    // 检查已审核任务的佣金修改限制
    if (existingTask.status === TaskStatus.APPROVED && 'commission' in updateData) {
      throw new ConflictError('已审核任务的佣金不可修改，如需调整请联系管理员');
    }

    // 更新任务
    const updatedTask = await prisma.task.update({
      where: { id },
      data: updateData as any,
      include: {
        order: {
          select: {
            id: true,
            orderNo: true,
            customerName: true,
          },
        },
        assignee: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true,
          },
        },
      },
    });

    return updatedTask;
  }

  // 员工更新任务状态
  async updateTaskStatus(id: string, status: TaskStatus, userId: string) {
    // 检查任务是否存在
    const task = await prisma.task.findUnique({
      where: { id },
      select: {
        id: true,
        assigneeId: true,
        status: true,
        startTime: true,
        endTime: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 检查权限：只能更新分配给自己的任务
    if (task.assigneeId !== userId) {
      throw new AuthorizationError('只能更新分配给自己的任务');
    }

    // 验证状态转换的合法性
    this.validateStatusTransition(task.status, status);

    // 准备更新数据
    const updateData: any = { status };

    // 根据状态设置相应的时间戳
    if (status === TaskStatus.IN_PROGRESS && task.status === TaskStatus.ACCEPTED) {
      // 开始任务
      updateData.startTime = new Date();
    } else if (status === TaskStatus.SUBMITTED) {
      // 提交任务
      updateData.endTime = new Date();

      // 计算实际工时（如果有开始时间）
      if (task.startTime) {
        updateData.actualHours = this.calculateActualHours(task.startTime, new Date());
      }
    }

    // 更新任务状态
    const updatedTask = await prisma.task.update({
      where: { id },
      data: updateData,
      include: {
        order: {
          select: {
            id: true,
            orderNo: true,
            customerName: true,
            createdById: true,
          },
        },
        assignee: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true,
          },
        },
      },
    });

    return updatedTask;
  }

  // 验证任务状态转换的合法性
  private validateStatusTransition(currentStatus: TaskStatus, newStatus: TaskStatus) {
    const validTransitions: Record<TaskStatus, TaskStatus[]> = {
      [TaskStatus.PENDING]: [], // 待接单状态不能由员工直接修改
      [TaskStatus.ACCEPTED]: [TaskStatus.IN_PROGRESS], // 已接单 -> 进行中
      [TaskStatus.IN_PROGRESS]: [TaskStatus.PAUSED, TaskStatus.SUBMITTED], // 进行中 -> 暂停/提交
      [TaskStatus.PAUSED]: [TaskStatus.IN_PROGRESS], // 暂停 -> 进行中
      [TaskStatus.SUBMITTED]: [], // 已提交状态不能由员工修改
      [TaskStatus.APPROVED]: [], // 已通过状态不能修改
      [TaskStatus.REJECTED]: [TaskStatus.IN_PROGRESS], // 被拒绝 -> 重新开始
      [TaskStatus.COMPLETED]: [], // 已完成状态不能修改
      [TaskStatus.CANCELLED]: [], // 已取消状态不能修改
    };

    const allowedStatuses = validTransitions[currentStatus] || [];

    if (!allowedStatuses.includes(newStatus)) {
      throw new ConflictError(`不能从状态 ${currentStatus} 转换到 ${newStatus}`);
    }
  }

  // 计算任务实际工时（小时）
  private calculateActualHours(startTime: Date | string, endTime: Date | string): number {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffInMs = end.getTime() - start.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);
    return Math.round(diffInHours * 100) / 100; // 保留两位小数
  }

  // 批量修复现有任务的工时数据
  async fixExistingTaskHours() {
    // 查找所有已完成但没有工时记录的任务
    const tasksToFix = await prisma.task.findMany({
      where: {
        OR: [
          { status: TaskStatus.APPROVED },
          { status: TaskStatus.COMPLETED }
        ],
        actualHours: null,
        startTime: { not: null },
        endTime: { not: null }
      },
      select: {
        id: true,
        startTime: true,
        endTime: true,
        taskNo: true
      }
    });

    console.log(`找到 ${tasksToFix.length} 个需要修复工时的任务`);

    let fixedCount = 0;
    for (const task of tasksToFix) {
      if (task.startTime && task.endTime) {
        const actualHours = this.calculateActualHours(task.startTime, task.endTime);

        await prisma.task.update({
          where: { id: task.id },
          data: { actualHours }
        });

        console.log(`修复任务 ${task.taskNo} 的工时: ${actualHours}h`);
        fixedCount++;
      }
    }

    console.log(`成功修复 ${fixedCount} 个任务的工时数据`);
    return { totalTasks: tasksToFix.length, fixedTasks: fixedCount };
  }

  // 取消任务
  async cancelTask(id: string, userId: string, userRole: UserRole) {
    // 检查任务是否存在
    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        assignee: true,
      },
    });

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 权限检查
    if (userRole === UserRole.EMPLOYEE && task.assigneeId !== userId) {
      throw new AuthorizationError('只能取消自己的任务');
    }

    // 检查任务状态
    if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CANCELLED) {
      throw new ConflictError('任务已完成或已取消，无法再次取消');
    }

    // 更新任务状态
    const updatedTask = await prisma.task.update({
      where: { id },
      data: {
        status: TaskStatus.CANCELLED,
        endTime: new Date(),
      },
    });

    // 如果是系统挂单任务，释放任务供其他员工接单
    if (task.assignType === AssignType.SYSTEM && task.assigneeId) {
      await prisma.task.update({
        where: { id },
        data: {
          assigneeId: null,
          status: TaskStatus.PENDING,
          startTime: null,
          endTime: null,
        },
      });
    }

    return updatedTask;
  }

  // 获取任务统计信息
  async getTaskStats(assigneeId?: string) {
    const where = assigneeId ? { assigneeId } : {};

    const stats = await prisma.task.groupBy({
      by: ['status'],
      where,
      _count: {
        status: true,
      },
    });

    const totalTasks = await prisma.task.count({ where });
    const totalCommission = await prisma.task.aggregate({
      where: { ...where, status: TaskStatus.COMPLETED },
      _sum: {
        commission: true,
      },
    });

    const avgHours = await prisma.task.aggregate({
      where: { ...where, status: TaskStatus.COMPLETED },
      _avg: {
        actualHours: true,
      },
    });

    const result = {
      totalTasks,
      totalCommission: totalCommission._sum.commission || 0,
      avgHours: avgHours._avg.actualHours || 0,
      statusBreakdown: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      }, {} as Record<string, number>),
    };

    return result;
  }
}
