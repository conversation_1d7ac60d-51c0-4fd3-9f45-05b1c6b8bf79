import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],

  // 1. 配置路径别名，解决 @ 符号无法识别的问题
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },

  // 2. 配置全局 SCSS 变量和 mixin，解决 undefined mixin 的问题
  css: {
    preprocessorOptions: {
      scss: {
        // 自动导入我们定义的 SCSS 文件
        // 注意：这里需要使用分号结尾
        additionalData: `
          @use "@/styles/variables.scss" as *;
          @use "@/styles/mixins.scss" as *;
        `
      }
    }
  },

  // 3. 配置打包选项，解决 Element Plus 初始化顺序问题
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('element-plus')) {
            return 'element-plus';
          }
        }
      }
    }
  }
})