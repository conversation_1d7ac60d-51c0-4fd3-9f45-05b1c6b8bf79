import { request } from './http'
import type { ApiResponse } from '@/types'

// 统计数据接口
export interface OverviewStats {
  overview: {
    totalOrders: number
    pendingOrders: number
    inProgressOrders: number
    completedOrders: number
    totalTasks: number
    pendingTasks: number
    activeTasks: number
    completedTasks: number
    totalEmployees: number
    activeEmployees: number
    totalRevenue: number
    totalCommission: number
  }
  monthly: {
    orders: number
    revenue: number
  }
  rates: {
    orderCompletionRate: number
    taskCompletionRate: number
    employeeUtilizationRate: number
  }
}

export interface RevenueAnalysis {
  dailyRevenue: Array<{
    date: string
    value: number
  }>
  revenueByPriority: Array<{
    priority: string
    revenue: number
    count: number
  }>
  totalPeriodRevenue: number
}

export interface EmployeePerformance {
  id: string
  username: string
  nickname: string
  level: number
  totalEarnings: number
  performance: {
    totalTasks: number
    completedTasks: number
    totalCommission: number
    totalHours: number
    avgHoursPerTask: number
    completionRate: number
    efficiency: number
  }
}

export interface OrderTrends {
  dailyOrders: Array<{
    date: string
    value: number
  }>
  ordersByStatus: Array<{
    status: string
    count: number
  }>
}

// 统计API
export const statisticsApi = {
  // 获取综合统计数据
  getOverviewStats(): Promise<ApiResponse<OverviewStats>> {
    return request.get('/statistics/overview')
  },

  // 获取收益分析数据
  getRevenueAnalysis(period: string = '30d'): Promise<ApiResponse<RevenueAnalysis>> {
    return request.get('/statistics/revenue', { params: { period } })
  },

  // 获取员工绩效数据
  getEmployeePerformance(): Promise<ApiResponse<EmployeePerformance[]>> {
    return request.get('/statistics/employees')
  },

  // 获取订单趋势数据
  getOrderTrends(period: string = '30d'): Promise<ApiResponse<OrderTrends>> {
    return request.get('/statistics/trends', { params: { period } })
  }
}
