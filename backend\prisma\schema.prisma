// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表 - 包含老板和员工
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  password    String
  nickname    String?
  phone       String?
  role        UserRole @default(EMPLOYEE)
  status      UserStatus @default(ACTIVE)
  level       Int      @default(1) // 员工等级
  totalEarnings Float  @default(0) // 总收益
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  createdOrders Order[] @relation("OrderCreator")
  createdTemplates OrderTemplate[] @relation("TemplateCreator")

  assignedTasks Task[]  @relation("TaskAssignee")
  taskProgress  TaskProgress[]
  settlements   Settlement[]
  notifications Notification[]
  userSessions  UserSession[]
  loginLogs     LoginLog[]
  gameSkills    EmployeeGameSkill[]
  certifiedSkills EmployeeGameSkill[] @relation("SkillCertifier")

  @@map("users")
}

// 游戏配置表 - 支持多游戏类型
model Game {
  id            String      @id @default(cuid())
  name          String      @unique // 游戏名称
  displayName   String      // 显示名称
  description   String?     // 游戏描述
  icon          String?     // 游戏图标URL
  isActive      Boolean     @default(true) // 是否启用
  sortOrder     Int         @default(0) // 排序权重

  // 游戏配置
  config        Json?       // 游戏特殊配置（JSON格式）

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  ranks         GameRank[]
  priceRules    GamePriceRule[]
  orders        Order[]
  employeeSkills EmployeeGameSkill[]
  formFields    GameFormField[] // 动态表单字段

  @@map("games")
}

// 游戏表单字段配置表 - 动态表单字段定义
model GameFormField {
  id            String      @id @default(cuid())
  gameId        String      // 关联游戏ID
  fieldKey      String      // 字段键名（程序内部使用的唯一标识）
  fieldLabel    String      // 字段标签（显示给用户看的名字）
  fieldType     FormFieldType // 字段类型
  isRequired    Boolean     @default(false) // 是否必填
  placeholder   String?     // 占位提示
  sortOrder     Int         @default(0) // 显示顺序

  // 字段选项（当字段类型是select或checkbox时使用）
  options       Json?       // 选项列表（JSON数组格式）

  // 字段配置（扩展配置）
  config        Json?       // 字段特殊配置（如最大长度、最小长度等）

  isActive      Boolean     @default(true) // 是否启用
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  game          Game        @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@unique([gameId, fieldKey]) // 同一游戏内字段键名唯一
  @@index([gameId, sortOrder]) // 按游戏和排序查询
  @@map("game_form_fields")
}

// 游戏段位表 - 支持不同游戏的段位系统
model GameRank {
  id            String      @id @default(cuid())
  gameId        String      // 关联游戏ID
  name          String      // 段位名称
  displayName   String      // 显示名称
  level         Int         // 段位等级（数字越大等级越高）
  difficultyMultiplier Float @default(1.0) // 难度系数
  icon          String?     // 段位图标URL
  description   String?     // 段位描述
  isActive      Boolean     @default(true) // 是否启用

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  game          Game        @relation(fields: [gameId], references: [id], onDelete: Cascade)
  ordersFrom    Order[]     @relation("OrderCurrentRank")
  ordersTo      Order[]     @relation("OrderTargetRank")

  @@unique([gameId, name])
  @@index([gameId, level])
  @@map("game_ranks")
}

// 游戏价格规则表 - 支持灵活的价格策略
model GamePriceRule {
  id            String      @id @default(cuid())
  gameId        String      // 关联游戏ID
  name          String      // 规则名称
  description   String?     // 规则描述

  // 价格计算规则
  ruleType      PriceRuleType @default(RANK_BASED) // 规则类型
  basePrice     Float       @default(0) // 基础价格
  pricePerLevel Float       @default(0) // 每级价格
  multiplier    Float       @default(1.0) // 价格倍数

  // 适用条件
  minLevel      Int?        // 最小段位等级
  maxLevel      Int?        // 最大段位等级
  priority      OrderPriority? // 适用优先级

  isActive      Boolean     @default(true) // 是否启用
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  game          Game        @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@index([gameId, isActive])
  @@map("game_price_rules")
}

// 员工游戏技能表 - 员工擅长的游戏类型
model EmployeeGameSkill {
  id            String      @id @default(cuid())
  userId        String      // 员工ID
  gameId        String      // 游戏ID
  skillLevel    SkillLevel  @default(BEGINNER) // 技能等级
  maxRankLevel  Int?        // 能够代练的最高段位等级
  isActive      Boolean     @default(true) // 是否启用

  // 技能认证
  certifiedAt   DateTime?   // 认证时间
  certifiedBy   String?     // 认证人ID

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  game          Game        @relation(fields: [gameId], references: [id], onDelete: Cascade)
  certifier     User?       @relation("SkillCertifier", fields: [certifiedBy], references: [id])

  @@unique([userId, gameId])
  @@index([gameId, skillLevel])
  @@map("employee_game_skills")
}

// 订单模板表 - 配置化订单模板
model OrderTemplate {
  id            String      @id @default(cuid())
  name          String      // 模板名称
  gameType      String      // 游戏类型标识
  version       Int         @default(1) // 模板版本号
  status        TemplateStatus @default(ACTIVE) // 模板状态
  description   String?     // 模板描述

  // 模板配置（JSON存储）
  fields        Json        // 字段定义配置
  displayConfig Json        // 显示配置
  businessRules Json?       // 业务规则配置

  // 元数据
  createdById   String      // 创建者ID
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  createdBy     User        @relation("TemplateCreator", fields: [createdById], references: [id])
  orders        Order[]     // 使用此模板的订单

  @@unique([gameType, version])
  @@index([gameType, status])
  @@map("order_templates")
}



// 订单表 - 客户订单信息（重构支持多游戏）
model Order {
  id            String      @id @default(cuid())
  orderNo       String      @unique // 订单编号

  // 动态表单驱动系统核心字段
  details       Json?       // 核心：灵活性的关键！存储动态表单数据
  formData      Json?       // 兼容字段：保持向后兼容

  // 模板相关（保留兼容性）
  templateId    String?     // 关联的订单模板ID
  templateVersion Int?      // 使用的模板版本

  // 游戏相关（灵活字段）
  gameType      String      // 游戏类型（字符串，支持任意游戏）
  currentRank   String?     // 当前段位（字符串，可选）
  targetRank    String?     // 目标段位（字符串，可选）

  // 游戏相关（关联字段，保留兼容性）
  gameId        String?     // 关联游戏ID（可选）
  currentRankId String?     // 当前段位ID（可选）
  targetRankId  String?     // 目标段位ID（可选）

  // 客户信息
  customerName  String      // 客户姓名
  customerContact String?   // 客户联系方式
  gameAccount   String      // 游戏账号
  gamePassword  String      // 游戏密码

  // 订单信息
  price         Float       // 订单价格
  deadline      DateTime?   // 完成期限
  requirements  String?     // 特殊要求
  status        OrderStatus @default(PENDING)
  priority      OrderPriority @default(NORMAL)

  // 元数据
  createdById   String      // 创建者ID（老板）
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  template      OrderTemplate? @relation(fields: [templateId], references: [id])
  game          Game?       @relation(fields: [gameId], references: [id])
  currentRankRef GameRank?  @relation("OrderCurrentRank", fields: [currentRankId], references: [id])
  targetRankRef GameRank?   @relation("OrderTargetRank", fields: [targetRankId], references: [id])
  createdBy     User        @relation("OrderCreator", fields: [createdById], references: [id])
  tasks         Task[]

  @@index([gameType, status])
  @@index([createdById])
  @@index([createdAt])
  @@map("orders")
}

// 任务表 - 分发的任务
model Task {
  id            String         @id @default(cuid())
  taskNo        String         @unique // 任务编号
  orderId       String         // 关联订单ID
  assigneeId    String?        // 分配的员工ID
  assignType    AssignType     // 分配方式：直接分配或系统挂单
  status        TaskStatus     @default(PENDING)
  startTime     DateTime?      // 开始时间
  endTime       DateTime?      // 结束时间
  estimatedHours Int?          // 预计工时
  actualHours   Int?           // 实际工时
  commission    Float?         // 佣金
  commissionType CommissionType @default(MANUAL) // 佣金计算方式
  commissionParams Json?       // 佣金计算参数（JSON格式）
  description   String?        // 任务描述
  notes         String?        // 备注
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // 关联关系
  order         Order          @relation(fields: [orderId], references: [id])
  assignee      User?          @relation("TaskAssignee", fields: [assigneeId], references: [id])
  progress      TaskProgress[]
  settlement    Settlement?

  @@map("tasks")
}

// 任务进度表 - 任务执行进度记录
model TaskProgress {
  id          String   @id @default(cuid())
  taskId      String   // 关联任务ID
  userId      String   // 操作用户ID
  progress    Int      @default(0) // 进度百分比 0-100
  currentRank String?  // 当前段位
  description String?  // 进度描述
  screenshots String?  // 截图证明（JSON数组）
  createdAt   DateTime @default(now())

  // 关联关系
  task        Task     @relation(fields: [taskId], references: [id])
  user        User     @relation(fields: [userId], references: [id])

  @@map("task_progress")
}

// 结算记录表 - 佣金结算
model Settlement {
  id          String           @id @default(cuid())
  taskId      String           @unique // 关联任务ID
  userId      String           // 员工ID
  amount      Float            // 结算金额
  status      SettlementStatus @default(PENDING)
  settledAt   DateTime?        // 结算时间
  notes       String?          // 结算备注
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // 关联关系
  task        Task             @relation(fields: [taskId], references: [id])
  user        User             @relation(fields: [userId], references: [id])

  @@map("settlements")
}

// 通知表 - 系统通知
model Notification {
  id        String           @id @default(cuid())
  userId    String           // 接收用户ID
  title     String           // 通知标题
  content   String           @db.Text // 通知内容
  type      NotificationType @default(INFO) // 通知类型
  isRead    Boolean          @default(false) // 是否已读
  data      String?          @db.Text // 额外数据（JSON格式）
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // 关联关系
  user      User             @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([isRead])
  @@index([createdAt])
  @@map("notifications")
}

// 用户会话表 - 在线用户监控
model UserSession {
  id            String      @id @default(cuid())
  userId        String      // 用户ID
  sessionToken  String      @unique // 会话令牌
  socketId      String?     // Socket.IO连接ID
  ipAddress     String      // IP地址
  userAgent     String?     @db.Text // 用户代理
  loginTime     DateTime    @default(now()) // 登录时间
  lastActivity  DateTime    @default(now()) // 最后活动时间
  logoutTime    DateTime?   // 登出时间
  status        SessionStatus @default(ACTIVE) // 会话状态
  deviceInfo    String?     @db.Text // 设备信息（JSON格式）

  // 地理位置信息
  locationCountry  String?  // 国家
  locationRegion   String?  // 区域
  locationProvince String?  // 省份
  locationCity     String?  // 城市
  locationIsp      String?  // ISP运营商
  locationFull     String?  // 完整位置信息

  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关联关系
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([lastActivity])
  @@index([ipAddress])
  @@map("user_sessions")
}

// 登录日志表 - 登录记录
model LoginLog {
  id            String      @id @default(cuid())
  userId        String?     // 用户ID（登录失败时可能为空）
  username      String      // 用户名
  ipAddress     String      // IP地址
  userAgent     String?     @db.Text // 用户代理
  loginTime     DateTime    @default(now()) // 登录时间
  logoutTime    DateTime?   // 登出时间
  loginResult   LoginResult // 登录结果
  failureReason String?     // 失败原因
  deviceInfo    String?     @db.Text // 设备信息（JSON格式）
  sessionDuration Int?      // 会话时长（秒）

  // 地理位置信息
  locationCountry  String?  // 国家
  locationRegion   String?  // 区域
  locationProvince String?  // 省份
  locationCity     String?  // 城市
  locationIsp      String?  // ISP运营商
  locationFull     String?  // 完整位置信息

  createdAt     DateTime    @default(now())

  // 关联关系
  user          User?       @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([loginTime])
  @@index([ipAddress])
  @@index([loginResult])
  @@map("login_logs")
}

// 枚举定义
enum UserRole {
  ADMIN    // 管理员
  BOSS     // 老板
  EMPLOYEE // 员工
}

enum UserStatus {
  ACTIVE   // 活跃
  INACTIVE // 非活跃
  BANNED   // 禁用
}

enum TemplateStatus {
  ACTIVE   // 活跃
  INACTIVE // 非活跃
  DRAFT    // 草稿
}

enum OrderStatus {
  PENDING    // 待处理
  ASSIGNED   // 已分配
  IN_PROGRESS // 进行中
  COMPLETED  // 已完成
  CANCELLED  // 已取消
}

enum OrderPriority {
  LOW    // 低优先级
  NORMAL // 普通
  HIGH   // 高优先级
  URGENT // 紧急
}

enum AssignType {
  DIRECT // 直接分配
  SYSTEM // 系统挂单
}

enum TaskStatus {
  PENDING     // 待接单
  ACCEPTED    // 已接单
  IN_PROGRESS // 进行中
  PAUSED      // 已暂停
  SUBMITTED   // 已提交
  APPROVED    // 已审核通过
  REJECTED    // 审核不通过
  COMPLETED   // 已完成
  CANCELLED   // 已取消
}

enum SettlementStatus {
  PENDING   // 待结算
  COMPLETED // 已结算
  CANCELLED // 已取消
}

enum CommissionType {
  AUTO   // 自动计算
  MANUAL // 手动输入
}

enum NotificationType {
  INFO    // 信息
  SUCCESS // 成功
  WARNING // 警告
  ERROR   // 错误
}

enum SessionStatus {
  ACTIVE        // 活跃
  INACTIVE      // 非活跃
  FORCED_LOGOUT // 强制下线
}

enum LoginResult {
  SUCCESS // 成功
  FAILED  // 失败
  BLOCKED // 被阻止
}

// 新增枚举类型

// 价格规则类型
enum PriceRuleType {
  RANK_BASED    // 基于段位
  TIME_BASED    // 基于时间
  FIXED_PRICE   // 固定价格
  CUSTOM        // 自定义规则
}

// 员工技能等级
enum SkillLevel {
  BEGINNER      // 初级
  INTERMEDIATE  // 中级
  ADVANCED      // 高级
  EXPERT        // 专家
  MASTER        // 大师
}

// 表单字段类型
enum FormFieldType {
  TEXT          // 单行文本框
  TEXTAREA      // 多行文本框
  SELECT        // 下拉选择框
  CHECKBOX      // 多选框
  NUMBER        // 数字输入框
  PASSWORD      // 密码框
  IMAGE         // 图片上传控件
}
