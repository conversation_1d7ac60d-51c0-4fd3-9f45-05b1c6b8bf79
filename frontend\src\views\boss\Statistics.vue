<template>
  <div class="statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>数据统计</h1>
      <div class="header-actions">
        <el-select v-model="selectedPeriod" @change="handlePeriodChange" style="width: 120px; margin-right: 12px;">
          <el-option label="7天" value="7d" />
          <el-option label="30天" value="30d" />
          <el-option label="90天" value="90d" />
        </el-select>
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 概览统计卡片 -->
    <div class="overview-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon orders">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats?.overview.totalOrders || 0 }}</div>
                <div class="stat-label">总订单数</div>
                <div class="stat-detail">
                  待处理: {{ overviewStats?.overview.pendingOrders || 0 }} |
                  进行中: {{ overviewStats?.overview.inProgressOrders || 0 }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon tasks">
                <el-icon><List /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats?.overview.totalTasks || 0 }}</div>
                <div class="stat-label">总任务数</div>
                <div class="stat-detail">
                  进行中: {{ overviewStats?.overview.activeTasks || 0 }} |
                  已完成: {{ overviewStats?.overview.completedTasks || 0 }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon employees">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats?.overview.totalEmployees || 0 }}</div>
                <div class="stat-label">员工总数</div>
                <div class="stat-detail">
                  活跃: {{ overviewStats?.overview.activeEmployees || 0 }} |
                  利用率: {{ overviewStats?.rates.employeeUtilizationRate || 0 }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ formatCurrency(safeNumber(overviewStats?.overview.totalRevenue, 0), { compact: true }) }}</div>
                <div class="stat-label">总收益</div>
                <div class="stat-detail">
                  本月: {{ formatCurrency(safeNumber(overviewStats?.monthly.revenue, 0), { compact: true }) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 收益趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>收益趋势</span>
              <span class="period-text">{{ getPeriodText() }}</span>
            </div></template>
          <div class="chart-container" ref="revenueChartRef" style="touch-action: pan-y;"></div>
        </el-card>
      </el-col>

      <!-- 订单趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
              <span class="period-text">{{ getPeriodText() }}</span>
            </div>
          </template>
          <div class="chart-container" ref="orderChartRef" style="touch-action: pan-y;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <!-- 订单状态分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>订单状态分布</span>
          </template>
          <div class="chart-container" ref="orderStatusChartRef" style="touch-action: pan-y;"></div>
        </el-card>
      </el-col>

      <!-- 收益来源分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>收益来源分布</span>
          </template>
          <div class="chart-container" ref="revenuePriorityChartRef" style="touch-action: pan-y;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 员工绩效表格 -->
    <el-card class="performance-section">
      <template #header>
        <div class="card-header">
          <span>员工绩效排行</span>
          <el-button
            type="primary"
            size="small"
            @click="refreshData"
            :loading="loading"
            :icon="Refresh"
          >
            刷新
          </el-button>
        </div>
      </template>
      <el-table
        :data="employeePerformance"
        stripe
        v-loading="loading"
        :empty-text="employeePerformance.length === 0 ? '暂无员工绩效数据' : ''"
      >
        <el-table-column prop="nickname" label="员工姓名" width="120">
          <template #default="{ row }">
            <div class="employee-info">
              <span>{{ row.nickname || row.username }}</span>
              <el-tag size="small" type="info">Lv.{{ row.level }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="performance.totalTasks" label="总任务数" width="100" align="center" />
        <el-table-column prop="performance.completedTasks" label="完成任务" width="100" align="center" />
        <el-table-column label="完成率" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getCompletionRateType(row.performance.completionRate)">
              {{ row.performance.completionRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="总收益" width="120" align="center">
          <template #default="{ row }">
            <span class="money-text">¥{{ formatNumber(row.performance.totalCommission) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总工时" width="100" align="center">
          <template #default="{ row }">
            {{ row.performance.totalHours }}h
          </template>
        </el-table-column>
        <el-table-column label="平均工时" width="100" align="center">
          <template #default="{ row }">
            {{ row.performance.avgHoursPerTask }}h
          </template>
        </el-table-column>
        <el-table-column label="效率" width="120" align="center">
          <template #default="{ row }">
            <div class="efficiency-container">
              <span class="efficiency-text">¥{{ formatNumber(row.performance.efficiency) }}/h</span>
              <el-tooltip
                v-if="row.performance.efficiency >= 1000"
                content="效率值已达到上限显示"
                placement="top"
              >
                <el-icon class="efficiency-warning"><Warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useStatisticsStore } from '@/stores/statistics'
import { storeToRefs } from 'pinia'
import { Refresh, Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  formatCurrency,
  formatNumber,
  createYAxisFormatter,
  createTooltipFormatter,
  formatPeriodText,
  formatDateForChart,
  validateChartData,
  safeNumber
} from '@/utils/format'

// Store
const statisticsStore = useStatisticsStore()
const { loading, overviewStats, revenueAnalysis, employeePerformance, orderTrends } = storeToRefs(statisticsStore)

// 响应式数据
const selectedPeriod = ref('30d')
const revenueChartRef = ref<HTMLElement>()
const orderChartRef = ref<HTMLElement>()
const orderStatusChartRef = ref<HTMLElement>()
const revenuePriorityChartRef = ref<HTMLElement>()

// 图表实例
let revenueChart: echarts.ECharts | null = null
let orderChart: echarts.ECharts | null = null
let orderStatusChart: echarts.ECharts | null = null
let revenuePriorityChart: echarts.ECharts | null = null

// 计算属性
const getPeriodText = () => {
  return formatPeriodText(selectedPeriod.value)
}

// 数据验证和错误处理
const validateData = () => {
  if (!revenueAnalysis.value || !orderTrends.value) {
    ElMessage.warning('数据加载中，请稍候...')
    return false
  }

  if (!revenueAnalysis.value.dailyRevenue || !Array.isArray(revenueAnalysis.value.dailyRevenue)) {
    ElMessage.error('收益数据格式错误')
    return false
  }

  if (!orderTrends.value.dailyOrders || !Array.isArray(orderTrends.value.dailyOrders)) {
    ElMessage.error('订单数据格式错误')
    return false
  }

  return true
}

// 获取完成率类型
const getCompletionRateType = (rate: number) => {
  if (rate >= 90) return 'success'
  if (rate >= 70) return 'warning'
  return 'danger'
}

// 刷新数据
const refreshData = async () => {
  await statisticsStore.refreshAllStats(selectedPeriod.value)
  await nextTick()
  initCharts()
}

// 周期变化处理
const handlePeriodChange = async () => {
  await statisticsStore.fetchRevenueAnalysis(selectedPeriod.value)
  await statisticsStore.fetchOrderTrends(selectedPeriod.value)
  await nextTick()
  updateCharts()
}

// 初始化图表
const initCharts = () => {
  if (!validateData()) {
    return
  }

  try {
    initRevenueChart()
    initOrderChart()
    initOrderStatusChart()
    initRevenuePriorityChart()
  } catch (error) {
    console.error('初始化图表失败:', error)
    ElMessage.error('图表初始化失败，请刷新页面重试')
  }
}

// 更新图表
const updateCharts = () => {
  if (!validateData()) {
    return
  }

  try {
    initRevenueChart()
    initOrderChart()
  } catch (error) {
    console.error('更新图表失败:', error)
    ElMessage.error('图表更新失败，请刷新页面重试')
  }
}

// 初始化收益趋势图
const initRevenueChart = () => {
  if (!revenueChartRef.value || !revenueAnalysis.value) return

  if (revenueChart) {
    revenueChart.dispose()
  }

  revenueChart = echarts.init(revenueChartRef.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })

  // 验证和清理数据
  const validData = validateChartData(revenueAnalysis.value.dailyRevenue)

  if (validData.length === 0) {
    ElMessage.warning('暂无收益数据')
    return
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: createTooltipFormatter('currency')
    },
    dataZoom: [
      {
        type: 'inside',
        disabled: false,
        zoomOnMouseWheel: 'ctrl'
      }
    ],
    xAxis: {
      type: 'category',
      data: validData.map((item: any) => formatDateForChart(item.date)),
      axisLabel: {
        fontSize: 12,
        rotate: validData.length > 15 ? 45 : 0 // 数据点多时旋转标签
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: createYAxisFormatter('currency'),
        fontSize: 12
      }
    },
    series: [{
      data: validData.map((item: any) => safeNumber(item.value, 0)),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      }
    }],
    grid: {
      left: '8%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    }
  }

  revenueChart.setOption(option)
}

// 初始化订单趋势图
const initOrderChart = () => {
  if (!orderChartRef.value || !orderTrends.value) return

  if (orderChart) {
    orderChart.dispose()
  }

  orderChart = echarts.init(orderChartRef.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })

  // 验证和清理数据
  const validData = validateChartData(orderTrends.value.dailyOrders)

  if (validData.length === 0) {
    ElMessage.warning('暂无订单数据')
    return
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: createTooltipFormatter('number')
    },
    dataZoom: [
      {
        type: 'inside',
        disabled: false,
        zoomOnMouseWheel: 'ctrl'
      }
    ],
    xAxis: {
      type: 'category',
      data: validData.map((item: any) => formatDateForChart(item.date)),
      axisLabel: {
        fontSize: 12,
        rotate: validData.length > 15 ? 45 : 0 // 数据点多时旋转标签
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: createYAxisFormatter('number'),
        fontSize: 12
      }
    },
    series: [{
      data: validData.map((item: any) => safeNumber(item.value, 0)),
      type: 'bar',
      itemStyle: {
        color: '#67C23A'
      }
    }],
    grid: {
      left: '8%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    }
  }

  orderChart.setOption(option)
}

// 初始化订单状态分布图
const initOrderStatusChart = () => {
  if (!orderStatusChartRef.value || !orderTrends.value) return

  if (orderStatusChart) {
    orderStatusChart.dispose()
  }

  orderStatusChart = echarts.init(orderStatusChartRef.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })

  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'ASSIGNED': '已分配',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '订单状态',
      type: 'pie',
      radius: '60%',
      data: orderTrends.value.ordersByStatus.map((item: any) => ({
        value: item.count,
        name: statusMap[item.status] || item.status
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  orderStatusChart.setOption(option)
}

// 初始化收益来源分布图
const initRevenuePriorityChart = () => {
  if (!revenuePriorityChartRef.value || !revenueAnalysis.value) return

  if (revenuePriorityChart) {
    revenuePriorityChart.dispose()
  }

  revenuePriorityChart = echarts.init(revenuePriorityChartRef.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  })

  const priorityMap: Record<string, string> = {
    'LOW': '低优先级',
    'NORMAL': '普通',
    'HIGH': '高优先级',
    'URGENT': '紧急'
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const value = safeNumber(params.value, 0)
        const formattedValue = formatCurrency(value, { compact: false })
        return `${params.seriesName}<br/>${params.name}: ${formattedValue} (${params.percent}%)`
      }
    },
    series: [{
      name: '收益来源',
      type: 'pie',
      radius: ['40%', '70%'],
      data: revenueAnalysis.value.revenueByPriority
        .filter((item: any) => safeNumber(item.revenue, 0) > 0) // 过滤掉0收益的项目
        .map((item: any) => ({
          value: safeNumber(item.revenue, 0),
          name: priorityMap[item.priority] || item.priority
        })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  revenuePriorityChart.setOption(option)
}

// 窗口大小变化处理
const handleResize = () => {
  revenueChart?.resize()
  orderChart?.resize()
  orderStatusChart?.resize()
  revenuePriorityChart?.resize()
}

// 清理图表实例
const disposeCharts = () => {
  revenueChart?.dispose()
  orderChart?.dispose()
  orderStatusChart?.dispose()
  revenuePriorityChart?.dispose()
  revenueChart = null
  orderChart = null
  orderStatusChart = null
  revenuePriorityChart = null
}

// 页面挂载
onMounted(async () => {
  await refreshData()
  window.addEventListener('resize', handleResize, { passive: true })
})

// 页面卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeCharts()
})
</script>

<style lang="scss" scoped>
.statistics-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .overview-cards {
    margin-bottom: 20px;

    .stat-card {
      height: 120px;

      .stat-content {
        display: flex;
        align-items: center;
        height: 100%;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;

          &.orders {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.tasks {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.employees {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.revenue {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .stat-info {
          flex: 1;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }

          .stat-detail {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .period-text {
        font-size: 12px;
        color: #909399;
      }
    }

    .chart-container {
      height: 300px;
      width: 100%;
      touch-action: pan-y;
      /* 优化ECharts性能和触摸交互 */
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      /* 防止图表在移动设备上的意外缩放 */
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
    }
  }

  .performance-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-table {
      .el-tag {
        font-weight: 500;
      }

      .employee-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;

        .el-tag {
          font-size: 10px;
        }
      }

      .money-text {
        color: #67c23a;
        font-weight: 600;
      }

      .efficiency-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
      }

      .efficiency-text {
        color: #409eff;
        font-weight: 600;
      }

      .efficiency-warning {
        color: #e6a23c;
        font-size: 14px;
      }
    }
  }
}
</style>
