<template>
  <div class="online-monitoring">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Monitor /></el-icon>
          在线用户监控
        </h1>
        <p class="page-description">实时监控在线用户状态、登录记录和系统统计</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="refreshData"
          :loading="monitoringStore.loading"
        >
          刷新数据
        </el-button>
        <el-button 
          type="warning" 
          :icon="Delete" 
          @click="cleanupSessions"
          :loading="monitoringStore.loading"
        >
          清理过期会话
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon online">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ monitoringStore.onlineUserCount }}</div>
                <div class="stats-label">在线用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon boss">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ monitoringStore.onlineUsersByRole.BOSS }}</div>
                <div class="stats-label">在线老板</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon employee">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ monitoringStore.onlineUsersByRole.EMPLOYEE }}</div>
                <div class="stats-label">在线员工</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon admin">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ monitoringStore.onlineUsersByRole.ADMIN }}</div>
                <div class="stats-label">在线管理员</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filters" inline>
          <el-form-item label="用户角色">
            <el-select v-model="filters.role" placeholder="选择角色" clearable style="width: 120px">
              <el-option label="管理员" value="ADMIN" />
              <el-option label="老板" value="BOSS" />
              <el-option label="员工" value="EMPLOYEE" />
            </el-select>
          </el-form-item>
          <el-form-item label="IP地址">
            <el-input 
              v-model="filters.ipAddress" 
              placeholder="输入IP地址" 
              clearable 
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="省份">
            <el-input 
              v-model="filters.locationProvince" 
              placeholder="输入省份" 
              clearable 
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item label="城市">
            <el-input 
              v-model="filters.locationCity" 
              placeholder="输入城市" 
              clearable 
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item label="最小在线时长">
            <el-input-number 
              v-model="filters.minOnlineTime" 
              :min="0" 
              placeholder="分钟"
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilters" :loading="monitoringStore.loading">
              筛选
            </el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 在线用户列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">在线用户列表</span>
          <span class="user-count">共 {{ monitoringStore.onlineUserCount }} 人在线</span>
        </div></template>

      <el-table 
        :data="monitoringStore.onlineUsers" 
        v-loading="monitoringStore.loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <span class="username">{{ row.username }}</span>
              <span v-if="row.nickname" class="nickname">({{ row.nickname }})</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getRoleTagType(row.role)" 
              size="small"
            >
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        
        <el-table-column label="地理位置" width="200">
          <template #default="{ row }">
            <div class="location-info">
              <el-icon><Location /></el-icon>
              <span>{{ monitoringStore.formatLocation(row) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="在线时长" width="120">
          <template #default="{ row }">
            <span class="online-time">{{ monitoringStore.formatOnlineTime(row.onlineDuration) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="loginTime" label="登录时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.loginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastActivity" label="最后活动" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastActivity) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewUserSessions(row)"
            >
              查看会话
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="confirmForceLogout(row)"
              :disabled="row.role === 'ADMIN' || row.userId === authStore.user?.id"
              :title="row.userId === authStore.user?.id ? '不能强制下线自己' : (row.role === 'ADMIN' ? '不能强制下线管理员' : '')"
            >
              强制下线
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 用户会话详情对话框 -->
    <UserSessionDialog 
      v-model="sessionDialogVisible"
      :user-id="selectedUserId"
      :username="selectedUsername"
    />

    <!-- 强制下线确认对话框 -->
    <ForceLogoutDialog 
      v-model="logoutDialogVisible"
      :user="selectedUser"
      @confirm="handleForceLogout"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  Monitor,
  Refresh,
  Delete,
  User,
  UserFilled,
  Setting,
  Location
} from '@element-plus/icons-vue'
import { useMonitoringStore } from '@/stores/monitoring'
import { useAuthStore } from '@/stores/auth'
import { formatDateTime } from '@/utils/date'
import type { OnlineUser, OnlineUserFilters, UserRole } from '@/types'
import UserSessionDialog from '@/components/UserSessionDialog.vue'
import ForceLogoutDialog from '@/components/ForceLogoutDialog.vue'

const monitoringStore = useMonitoringStore()
const authStore = useAuthStore()

// 筛选条件
const filters = reactive<OnlineUserFilters>({
  role: undefined,
  ipAddress: '',
  locationProvince: '',
  locationCity: '',
  minOnlineTime: undefined
})

// 对话框状态
const sessionDialogVisible = ref(false)
const logoutDialogVisible = ref(false)
const selectedUserId = ref('')
const selectedUsername = ref('')
const selectedUser = ref<OnlineUser | null>(null)

// 获取角色标签类型
const getRoleTagType = (role: UserRole) => {
  switch (role) {
    case 'ADMIN': return 'danger'
    case 'BOSS': return 'warning'
    case 'EMPLOYEE': return 'success'
    default: return 'info'
  }
}

// 获取角色文本
const getRoleText = (role: UserRole) => {
  switch (role) {
    case 'ADMIN': return '管理员'
    case 'BOSS': return '老板'
    case 'EMPLOYEE': return '员工'
    default: return '未知'
  }
}

// 应用筛选
const applyFilters = () => {
  const cleanFilters = Object.fromEntries(
    Object.entries(filters).filter(([_, value]) => value !== '' && value !== undefined)
  )
  monitoringStore.fetchOnlineUsers(cleanFilters)
}

// 重置筛选
const resetFilters = () => {
  Object.assign(filters, {
    role: undefined,
    ipAddress: '',
    locationProvince: '',
    locationCity: '',
    minOnlineTime: undefined
  })
  monitoringStore.fetchOnlineUsers()
}

// 刷新数据
const refreshData = () => {
  applyFilters()
  monitoringStore.fetchOnlineUserStats()
}

// 清理过期会话
const cleanupSessions = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有过期会话吗？这将清理24小时内无活动的会话。',
      '清理过期会话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await monitoringStore.cleanupExpiredSessions()
  } catch (error) {
    // 用户取消操作
  }
}

// 查看用户会话
const viewUserSessions = (user: OnlineUser) => {
  selectedUserId.value = user.userId
  selectedUsername.value = user.username
  sessionDialogVisible.value = true
}

// 确认强制下线
const confirmForceLogout = (user: OnlineUser) => {
  selectedUser.value = user
  logoutDialogVisible.value = true
}

// 处理强制下线
const handleForceLogout = async (sessionId: string, reason: string) => {
  await monitoringStore.forceUserLogout(sessionId, reason)
  logoutDialogVisible.value = false
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.online-monitoring {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .page-description {
        color: #606266;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stats-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;

          &.online {
            background-color: #f0f9ff;
            color: #67c23a;
          }

          &.boss {
            background-color: #fdf6ec;
            color: #e6a23c;
          }

          &.employee {
            background-color: #f0f9ff;
            color: #409eff;
          }

          &.admin {
            background-color: #fef0f0;
            color: #f56c6c;
          }
        }

        .stats-info {
          .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stats-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;

    .filter-content {
      .el-form {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .user-count {
        font-size: 14px;
        color: #606266;
      }
    }

    .user-info {
      .username {
        font-weight: 500;
        color: #303133;
      }

      .nickname {
        font-size: 12px;
        color: #909399;
        margin-left: 4px;
      }
    }

    .location-info {
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        color: #909399;
      }
    }

    .online-time {
      color: #67c23a;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .online-monitoring {
    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .online-monitoring {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-actions {
        justify-content: flex-end;
      }
    }

    .filter-card {
      .filter-content {
        .el-form {
          .el-form-item {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .table-card {
      .el-table {
        font-size: 12px;
      }
    }
  }
}
</style>
