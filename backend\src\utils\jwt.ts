import jwt from 'jsonwebtoken';
import { UserRole } from '@prisma/client';
import { config } from '../config/env';
import { JwtPayload } from '../types/auth';

// 生成访问令牌
export function generateAccessToken(user: {
  id: string;
  username: string;
  role: UserRole;
}): string {
  const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
    userId: user.id,
    username: user.username,
    role: user.role,
  };

  return jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN,
  } as jwt.SignOptions);
}

// 验证令牌
export function verifyToken(token: string): JwtPayload {
  return jwt.verify(token, config.JWT_SECRET) as JwtPayload;
}

// 解码令牌（不验证）
export function decodeToken(token: string): JwtPayload | null {
  try {
    return jwt.decode(token) as JwtPayload;
  } catch {
    return null;
  }
}

// 获取令牌过期时间
export function getTokenExpiration(token: string): Date | null {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return null;
  }
  return new Date(decoded.exp * 1000);
}

// 检查令牌是否即将过期（30分钟内）
export function isTokenExpiringSoon(token: string): boolean {
  const expiration = getTokenExpiration(token);
  if (!expiration) {
    return true;
  }
  
  const now = new Date();
  const thirtyMinutes = 30 * 60 * 1000; // 30分钟的毫秒数
  
  return expiration.getTime() - now.getTime() < thirtyMinutes;
}
