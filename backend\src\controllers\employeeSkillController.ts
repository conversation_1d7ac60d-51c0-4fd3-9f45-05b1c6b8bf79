import { Request, Response } from 'express';
import { EmployeeSkillService } from '../services/employeeSkillService';
import { SmartTaskAssignmentService } from '../services/smartTaskAssignmentService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import {
  CreateEmployeeGameSkillRequest,
  UpdateEmployeeGameSkillRequest
} from '../types/game';

const employeeSkillService = new EmployeeSkillService();
const smartAssignmentService = new SmartTaskAssignmentService();

// 创建员工游戏技能
export const createEmployeeGameSkill = asyncHandler(async (req: Request, res: Response) => {
  const skillData: CreateEmployeeGameSkillRequest = req.body;
  
  const skill = await employeeSkillService.createEmployeeGameSkill(skillData);
  
  const response: ApiResponse = {
    success: true,
    data: skill,
    message: '员工游戏技能创建成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// 获取员工游戏技能列表
export const getEmployeeGameSkills = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  const result = await employeeSkillService.getEmployeeGameSkills(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取员工游戏技能列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取员工游戏技能详情
export const getEmployeeGameSkillById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const skill = await employeeSkillService.getEmployeeGameSkillById(id);
  
  const response: ApiResponse = {
    success: true,
    data: skill,
    message: '获取员工游戏技能详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新员工游戏技能
export const updateEmployeeGameSkill = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateEmployeeGameSkillRequest = req.body;
  
  const skill = await employeeSkillService.updateEmployeeGameSkill(id, updateData);
  
  const response: ApiResponse = {
    success: true,
    data: skill,
    message: '员工游戏技能更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 删除员工游戏技能
export const deleteEmployeeGameSkill = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const result = await employeeSkillService.deleteEmployeeGameSkill(id);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '员工游戏技能删除成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 认证员工技能
export const certifyEmployeeSkill = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const certifierId = req.user!.id;
  
  const skill = await employeeSkillService.certifyEmployeeSkill(id, certifierId);
  
  const response: ApiResponse = {
    success: true,
    data: skill,
    message: '员工技能认证成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取指定游戏的技能员工匹配
export const getSkillMatchesForGame = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.params;
  const { targetRankLevel } = req.query;
  
  const matches = await employeeSkillService.getSkillMatchesForGame(
    gameId,
    targetRankLevel ? parseInt(targetRankLevel as string) : undefined
  );
  
  const response: ApiResponse = {
    success: true,
    data: matches,
    message: '获取技能匹配员工成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取任务分配推荐
export const getTaskAssignmentRecommendations = asyncHandler(async (req: Request, res: Response) => {
  const { orderId } = req.params;
  const options = req.body;
  
  const recommendations = await smartAssignmentService.getTaskAssignmentRecommendations({
    orderId,
    ...options
  });
  
  const response: ApiResponse = {
    success: true,
    data: recommendations,
    message: '获取任务分配推荐成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 自动分配任务
export const autoAssignTask = asyncHandler(async (req: Request, res: Response) => {
  const { orderId } = req.params;
  const options = req.body;
  
  const result = await smartAssignmentService.autoAssignTask(orderId, options);
  
  const response: ApiResponse = {
    success: result.success,
    data: result,
    message: result.message,
    timestamp: new Date().toISOString(),
  };
  
  res.status(result.success ? 200 : 400).json(response);
});

// 获取游戏热度统计
export const getGameHotnessStats = asyncHandler(async (req: Request, res: Response) => {
  const stats = await smartAssignmentService.getGameHotnessStats();
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取游戏热度统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取员工技能分布统计
export const getEmployeeSkillDistribution = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.query;
  
  // 获取技能等级分布
  const skillDistribution = await employeeSkillService.getEmployeeGameSkills({
    gameId: gameId as string,
    isActive: true,
    limit: 1000 // 获取所有记录用于统计
  });
  
  // 统计各技能等级的数量
  const distribution = skillDistribution.items.reduce((acc, skill) => {
    const level = skill.skillLevel;
    acc[level] = (acc[level] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const total = skillDistribution.items.length;
  
  // 转换为百分比格式
  const result = Object.entries(distribution).map(([skillLevel, count]) => ({
    skillLevel,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0
  }));
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取员工技能分布统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取员工个人技能概览
export const getEmployeeSkillOverview = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  
  // 获取员工的所有游戏技能
  const skills = await employeeSkillService.getEmployeeGameSkills({
    userId,
    isActive: true,
    limit: 100
  });
  
  // 获取员工的任务完成统计
  const taskStats = await Promise.all(
    skills.items.map(async (skill) => {
      const completedTasks = await employeeSkillService.getEmployeeGameSkills({
        userId,
        gameId: skill.gameId,
        limit: 1
      });
      
      // 这里应该查询实际的任务完成数据，暂时返回模拟数据
      return {
        gameId: skill.gameId,
        gameName: skill.game?.displayName || '',
        skillLevel: skill.skillLevel,
        isActive: skill.isActive,
        isCertified: !!skill.certifiedAt,
        maxRankLevel: skill.maxRankLevel,
        completedTasks: 0, // TODO: 实现实际的任务统计
        totalEarnings: 0   // TODO: 实现实际的收益统计
      };
    })
  );
  
  const response: ApiResponse = {
    success: true,
    data: {
      skills: skills.items,
      statistics: taskStats,
      summary: {
        totalGames: skills.items.length,
        certifiedGames: skills.items.filter(s => s.certifiedAt).length,
        activeGames: skills.items.filter(s => s.isActive).length
      }
    },
    message: '获取员工技能概览成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});
