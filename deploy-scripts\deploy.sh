#!/bin/bash

# 王者荣耀代练任务分发管理系统 - 宝塔面板一键部署脚本
# 适配MySQL 5.7 + 包含ip2region离线IP解析功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
PROJECT_NAME="game-boost"
BACKEND_DIR="/www/wwwroot/${PROJECT_NAME}-backend"
FRONTEND_DIR="/www/wwwroot/${PROJECT_NAME}-frontend"
IP2REGION_DIR="/www/wwwroot/${PROJECT_NAME}-ip2region"
DB_NAME="game_boost_db"
DB_USER="gameuser"
DB_PASS=""  # 将在脚本中询问
MYSQL_ROOT_PASS=""  # MySQL root密码
SERVER_IP=""  # 将在脚本中获取

# 获取服务器IP
get_server_ip() {
    log_step "获取服务器公网IP..."
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
    if [ -z "$SERVER_IP" ]; then
        log_error "无法获取服务器IP，请手动输入："
        read -p "请输入服务器公网IP: " SERVER_IP
    fi
    log_info "服务器IP: $SERVER_IP"
}

# 检查宝塔面板版本和环境
check_baota_environment() {
    log_step "检查宝塔面板环境..."
    
    # 检查是否安装宝塔面板
    if ! command -v bt &> /dev/null; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请在宝塔面板安装Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低($NODE_VERSION)，需要18+版本"
        exit 1
    fi
    log_info "Node.js版本检查通过: $(node --version)"
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL未安装，请在宝塔面板安装MySQL"
        exit 1
    fi
    log_info "MySQL版本: $(mysql --version)"
    
    # 检查Redis
    if ! command -v redis-server &> /dev/null; then
        log_error "Redis未安装，请在宝塔面板安装Redis"
        exit 1
    fi
    log_info "Redis版本: $(redis-server --version)"
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装，请在宝塔面板安装Nginx"
        exit 1
    fi
    log_info "Nginx版本: $(nginx -v 2>&1)"
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_warn "PM2未安装，正在安装..."
        npm install -g pm2
    fi
    log_info "PM2版本: $(pm2 --version)"
}

# 获取数据库密码
get_database_password() {
    log_step "配置数据库密码..."

    # 获取MySQL root密码
    echo -n "请输入MySQL root密码（默认：123456）: "
    read -s MYSQL_ROOT_PASS
    echo
    if [ -z "$MYSQL_ROOT_PASS" ]; then
        MYSQL_ROOT_PASS="123456"
        log_info "使用默认MySQL root密码: 123456"
    fi

    # 设置gameuser密码
    echo -n "请输入数据库用户密码（用于gameuser用户，默认：GameUser123!）: "
    read -s DB_PASS
    echo
    if [ -z "$DB_PASS" ]; then
        DB_PASS="GameUser123!"
        log_info "使用默认数据库用户密码: GameUser123!"
    fi
}

# 创建数据库和用户
setup_database() {
    log_step "配置数据库..."
    
    # 创建数据库和用户的SQL
    mysql -u root -p${MYSQL_ROOT_PASS} << EOF
CREATE DATABASE IF NOT EXISTS ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        log_info "数据库配置完成"
    else
        log_error "数据库配置失败"
        exit 1
    fi
}

# 部署后端
deploy_backend() {
    log_step "部署后端应用..."
    
    # 创建后端目录
    mkdir -p "$BACKEND_DIR"
    cd "$BACKEND_DIR"
    
    # 复制后端代码
    if [ -d "../backend" ]; then
        cp -r ../backend/* .
        log_info "后端代码复制完成"
    else
        log_error "未找到backend目录"
        exit 1
    fi
    
    # 复制ip2region文件
    if [ -d "../ip2region-master" ]; then
        cp -r ../ip2region-master "$IP2REGION_DIR"
        log_info "ip2region文件复制完成"
    else
        log_error "未找到ip2region-master目录，离线IP功能将无法使用"
        exit 1
    fi
    
    # 创建环境变量文件
    cat > .env << EOF
NODE_ENV=production
DATABASE_URL="mysql://${DB_USER}:${DB_PASS}@localhost:3306/${DB_NAME}?schema=public&sslmode=prefer"
REDIS_HOST=localhost
REDIS_PORT=6379
JWT_SECRET=$(openssl rand -base64 32)
PORT=3000
IP2REGION_PATH=${IP2REGION_DIR}/data/ip2region.xdb
EOF
    
    log_info "环境变量配置完成"
    
    # 安装依赖
    log_info "安装后端依赖..."
    npm install --production
    
    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    npx prisma generate
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    npx prisma migrate deploy
    
    # 构建项目
    log_info "构建后端项目..."
    npm run build
    
    # 设置文件权限
    chown -R www:www "$BACKEND_DIR"
    chmod -R 755 "$BACKEND_DIR"
    mkdir -p uploads logs
    chmod 777 uploads logs
    
    log_info "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log_step "部署前端应用..."
    
    # 创建临时构建目录
    TEMP_BUILD_DIR="/tmp/frontend-build"
    mkdir -p "$TEMP_BUILD_DIR"
    cd "$TEMP_BUILD_DIR"
    
    # 复制前端代码
    if [ -d "../frontend" ]; then
        cp -r ../frontend/* .
        log_info "前端代码复制完成"
    else
        log_error "未找到frontend目录"
        exit 1
    fi
    
    # 修改API基础URL为服务器IP
    if [ -f "src/utils/request.js" ] || [ -f "src/utils/request.ts" ]; then
        sed -i "s/localhost:3000/${SERVER_IP}:3000/g" src/utils/request.*
        log_info "API地址已更新为: ${SERVER_IP}:3000"
    fi
    
    # 安装依赖并构建
    log_info "安装前端依赖..."
    npm install
    
    log_info "构建前端项目..."
    npm run build
    
    # 复制构建结果到网站目录
    mkdir -p "$FRONTEND_DIR"
    cp -r dist/* "$FRONTEND_DIR/"
    
    # 设置权限
    chown -R www:www "$FRONTEND_DIR"
    chmod -R 755 "$FRONTEND_DIR"
    
    # 清理临时目录
    rm -rf "$TEMP_BUILD_DIR"
    
    log_info "前端部署完成"
}

# 配置PM2
setup_pm2() {
    log_step "配置PM2进程管理..."
    
    cd "$BACKEND_DIR"
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '${PROJECT_NAME}-backend',
    script: 'dist/index.js',
    cwd: '${BACKEND_DIR}',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000
  }]
};
EOF
    
    # 启动PM2应用
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    log_info "PM2配置完成"
}

# 主函数
main() {
    log_info "开始部署王者荣耀代练任务分发管理系统..."
    log_info "适配环境: 宝塔面板 + MySQL 5.7 + ip2region离线IP解析"
    
    get_server_ip
    check_baota_environment
    get_database_password
    setup_database
    deploy_backend
    deploy_frontend
    setup_pm2
    
    log_info "部署完成！"
    log_info "前端访问地址: http://${SERVER_IP}"
    log_info "后端API地址: http://${SERVER_IP}:3000"
    log_info "请在宝塔面板配置Nginx反向代理"
}

# 执行主函数
main "$@"
