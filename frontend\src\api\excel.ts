import { request } from './http';

// Excel导出相关类型定义
export interface ExportOptions {
  monthly_settlement: {
    name: string;
    description: string;
    permissions: string[];
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      description: string;
    }>;
  };
  order_summary: {
    name: string;
    description: string;
    permissions: string[];
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      description: string;
    }>;
  };
  employee_earnings: {
    name: string;
    description: string;
    permissions: string[];
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      description: string;
    }>;
  };
}

export interface CustomReportRequest {
  reportType: 'monthly_settlement' | 'order_summary' | 'employee_earnings';
  startDate?: string;
  endDate?: string;
  filters?: Record<string, any>;
}

// Excel导出API
export const excelApi = {
  /**
   * 获取可用的导出选项
   */
  getExportOptions(): Promise<{ success: boolean; data: ExportOptions }> {
    return request.get('/excel/export-options');
  },

  /**
   * 导出月度结算报表
   */
  exportMonthlySettlement(year: number, month: number): Promise<Blob> {
    return request.get(`/excel/monthly-settlement?year=${year}&month=${month}`, {
      responseType: 'blob'
    }).then(response => {
      // 直接返回blob数据
      return response as any;
    });
  },

  /**
   * 导出员工个人收益明细
   */
  exportEmployeeEarnings(
    employeeId: string, 
    startDate?: string, 
    endDate?: string
  ): Promise<Blob> {
    let url = `/excel/employee-earnings/${employeeId}`;
    const params = new URLSearchParams();
    
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return request.get(url, {
      responseType: 'blob'
    }).then(response => {
      return response as any;
    });
  },

  /**
   * 导出订单完成情况汇总
   */
  exportOrderSummary(startDate?: string, endDate?: string): Promise<Blob> {
    let url = '/excel/order-summary';
    const params = new URLSearchParams();
    
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return request.get(url, {
      responseType: 'blob'
    }).then(response => {
      return response as any;
    });
  },

  /**
   * 导出自定义报表
   */
  exportCustomReport(data: CustomReportRequest): Promise<Blob> {
    return request.post('/excel/custom-report', data, {
      responseType: 'blob'
    }).then(response => {
      return response as any;
    });
  }
};

/**
 * 下载文件的通用方法
 */
export const downloadFile = (blob: Blob, filename: string) => {
  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * 从响应头中提取文件名
 */
export const getFilenameFromResponse = (response: any): string => {
  const contentDisposition = response.headers?.['content-disposition'];
  if (contentDisposition) {
    const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (matches && matches[1]) {
      return decodeURIComponent(matches[1].replace(/['"]/g, ''));
    }
  }
  
  // 默认文件名
  return `导出文件_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
};
