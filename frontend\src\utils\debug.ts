import { toRaw, isRef, isReactive, isProxy } from 'vue'

/**
 * 调试工具函数 - 用于正确输出Vue响应式对象
 */

/**
 * 安全地输出响应式对象到控制台
 * 自动处理Vue 3的响应式代理对象，输出原始值
 * @param label - 调试标签
 * @param data - 要输出的数据
 * @param level - 日志级别 ('log' | 'info' | 'warn' | 'error')
 */
export function debugLog(
  label: string, 
  data: any, 
  level: 'log' | 'info' | 'warn' | 'error' = 'log'
) {
  // 只在开发环境输出调试信息
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  let processedData = data

  // 处理Vue响应式对象
  if (isProxy(data) || isReactive(data) || isRef(data)) {
    processedData = toRaw(data)
  }

  // 处理数组中的响应式对象
  if (Array.isArray(data)) {
    processedData = data.map(item => {
      if (isProxy(item) || isReactive(item) || isRef(item)) {
        return toRaw(item)
      }
      return item
    })
  }

  // 处理对象中的响应式属性
  if (data && typeof data === 'object' && !Array.isArray(data)) {
    processedData = {}
    for (const [key, value] of Object.entries(data)) {
      if (isProxy(value) || isReactive(value) || isRef(value)) {
        processedData[key] = toRaw(value)
      } else {
        processedData[key] = value
      }
    }
  }

  // 输出到控制台
  console[level](`[DEBUG] ${label}:`, processedData)
}

/**
 * 输出响应式对象的详细信息
 * @param label - 调试标签
 * @param data - 要分析的数据
 */
export function debugReactiveInfo(label: string, data: any) {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  console.group(`[REACTIVE DEBUG] ${label}`)
  console.log('原始数据:', data)
  console.log('是否为代理对象:', isProxy(data))
  console.log('是否为响应式对象:', isReactive(data))
  console.log('是否为ref对象:', isRef(data))
  
  if (isProxy(data) || isReactive(data) || isRef(data)) {
    console.log('原始值:', toRaw(data))
  }
  
  console.groupEnd()
}

/**
 * 格式化输出对象的关键信息
 * @param label - 调试标签
 * @param obj - 要输出的对象
 * @param keys - 要显示的关键字段
 */
export function debugObjectKeys(label: string, obj: any, keys: string[]) {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const rawObj = isProxy(obj) || isReactive(obj) || isRef(obj) ? toRaw(obj) : obj
  const keyInfo: Record<string, any> = {}
  
  keys.forEach(key => {
    if (key in rawObj) {
      keyInfo[key] = rawObj[key]
    }
  })

  console.log(`[DEBUG] ${label} 关键信息:`, keyInfo)
}

/**
 * 性能调试 - 测量函数执行时间
 * @param label - 调试标签
 * @param fn - 要测量的函数
 */
export async function debugPerformance<T>(
  label: string, 
  fn: () => T | Promise<T>
): Promise<T> {
  if (process.env.NODE_ENV !== 'development') {
    return await fn()
  }

  const startTime = performance.now()
  console.time(`[PERFORMANCE] ${label}`)
  
  try {
    const result = await fn()
    const endTime = performance.now()
    console.timeEnd(`[PERFORMANCE] ${label}`)
    console.log(`[PERFORMANCE] ${label} 执行时间: ${(endTime - startTime).toFixed(2)}ms`)
    return result
  } catch (error) {
    console.timeEnd(`[PERFORMANCE] ${label}`)
    console.error(`[PERFORMANCE] ${label} 执行失败:`, error)
    throw error
  }
}

/**
 * 条件调试 - 只在满足条件时输出
 * @param condition - 调试条件
 * @param label - 调试标签
 * @param data - 要输出的数据
 */
export function debugIf(condition: boolean, label: string, data: any) {
  if (condition) {
    debugLog(label, data)
  }
}

/**
 * 调试表格 - 以表格形式输出数组数据
 * @param label - 调试标签
 * @param data - 数组数据
 * @param columns - 要显示的列
 */
export function debugTable(label: string, data: any[], columns?: string[]) {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const processedData = data.map(item => {
    const rawItem = isProxy(item) || isReactive(item) || isRef(item) ? toRaw(item) : item
    
    if (columns) {
      const filteredItem: Record<string, any> = {}
      columns.forEach(col => {
        if (col in rawItem) {
          filteredItem[col] = rawItem[col]
        }
      })
      return filteredItem
    }
    
    return rawItem
  })

  console.log(`[DEBUG TABLE] ${label}:`)
  console.table(processedData)
}

// 导出常用的调试方法
export const debug = {
  log: debugLog,
  info: (label: string, data: any) => debugLog(label, data, 'info'),
  warn: (label: string, data: any) => debugLog(label, data, 'warn'),
  error: (label: string, data: any) => debugLog(label, data, 'error'),
  reactive: debugReactiveInfo,
  keys: debugObjectKeys,
  performance: debugPerformance,
  if: debugIf,
  table: debugTable
}

export default debug
