import { Request, Response } from 'express';
import { NotificationService } from '../services/notificationService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse, CreateNotificationRequest, NotificationQuery } from '../types/common';

const notificationService = new NotificationService();

// 获取当前用户的通知列表
export const getNotifications = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const query = req.query as NotificationQuery;

  const result = await notificationService.getUserNotifications(userId, query);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取通知列表成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 创建通知（管理员功能）
export const createNotification = asyncHandler(async (req: Request, res: Response) => {
  const notificationData: CreateNotificationRequest = req.body;

  const notification = await notificationService.createNotification(notificationData);

  const response: ApiResponse = {
    success: true,
    data: notification,
    message: '创建通知成功',
    timestamp: new Date().toISOString(),
  };

  res.status(201).json(response);
});

// 获取通知详情
export const getNotificationById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const notification = await notificationService.getNotificationById(id, userId);

  if (!notification) {
    const response: ApiResponse = {
      success: false,
      message: '通知不存在',
      timestamp: new Date().toISOString(),
    };
    return res.status(404).json(response);
  }

  const response: ApiResponse = {
    success: true,
    data: notification,
    message: '获取通知详情成功',
    timestamp: new Date().toISOString(),
  };

  return res.status(200).json(response);
});

// 标记通知为已读
export const markNotificationAsRead = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const notification = await notificationService.markAsRead(id, userId);

  if (!notification) {
    const response: ApiResponse = {
      success: false,
      message: '通知不存在',
      timestamp: new Date().toISOString(),
    };
    return res.status(404).json(response);
  }

  const response: ApiResponse = {
    success: true,
    data: notification,
    message: '标记已读成功',
    timestamp: new Date().toISOString(),
  };

  return res.status(200).json(response);
});

// 标记所有通知为已读
export const markAllNotificationsAsRead = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const result = await notificationService.markAllAsRead(userId);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: `成功标记 ${result.count} 条通知为已读`,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 删除通知
export const deleteNotification = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const success = await notificationService.deleteNotification(id, userId);

  if (!success) {
    const response: ApiResponse = {
      success: false,
      message: '通知不存在',
      timestamp: new Date().toISOString(),
    };
    return res.status(404).json(response);
  }

  const response: ApiResponse = {
    success: true,
    message: '删除通知成功',
    timestamp: new Date().toISOString(),
  };

  return res.status(200).json(response);
});

// 获取未读通知数量
export const getUnreadCount = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const count = await notificationService.getUnreadCount(userId);

  const response: ApiResponse = {
    success: true,
    data: { count },
    message: '获取未读通知数量成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 清理过期通知（管理员功能）
export const cleanupOldNotifications = asyncHandler(async (req: Request, res: Response) => {
  const result = await notificationService.cleanupOldNotifications();

  const response: ApiResponse = {
    success: true,
    data: result,
    message: `成功清理 ${result.count} 条过期通知`,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});
