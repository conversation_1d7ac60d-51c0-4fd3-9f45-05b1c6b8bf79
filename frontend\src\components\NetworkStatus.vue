<template>
  <transition name="network-slide">
    <div v-if="!isOnline" class="network-status">
      <div class="status-content">
        <div class="status-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="status-message">
          <div class="status-title">网络连接已断开</div>
          <div class="status-detail">请检查您的网络连接</div>
        </div>
        <div class="status-indicator">
          <div class="indicator" :class="{ reconnecting: isReconnecting }"></div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'

const isOnline = ref(navigator.onLine)
const isReconnecting = ref(false)

let reconnectTimer: NodeJS.Timeout | null = null

const handleOnline = () => {
  isOnline.value = true
  isReconnecting.value = false
  
  if (reconnectTimer) {
    clearInterval(reconnectTimer)
    reconnectTimer = null
  }
  
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  isOnline.value = false
  isReconnecting.value = true

  ElMessage.warning('网络连接已断开')

  // 开始重连检测 - 减少检测频率，避免过多请求
  let retryCount = 0
  const maxRetries = 10

  const checkConnection = () => {
    retryCount++

    // 尝试发送一个简单的请求来检测网络状态
    fetch('/health', {
      method: 'HEAD',
      cache: 'no-cache'
    })
    .then(() => {
      if (!navigator.onLine) {
        // 手动触发online事件
        handleOnline()
      }
    })
    .catch(() => {
      // 网络仍然不可用
      if (retryCount >= maxRetries) {
        if (reconnectTimer) {
          clearInterval(reconnectTimer)
          reconnectTimer = null
        }
        isReconnecting.value = false
        ElMessage.error('网络连接检测已停止，请手动刷新页面')
        return
      }

      // 逐渐增加检测间隔，减少服务器压力
      const nextInterval = Math.min(10000 * Math.pow(1.2, retryCount), 60000)

      if (reconnectTimer) {
        clearInterval(reconnectTimer)
      }

      reconnectTimer = setInterval(checkConnection, nextInterval)
    })
  }

  reconnectTimer = setInterval(checkConnection, 15000) // 初始15秒检测一次
}

onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 初始检查
  if (!navigator.onLine) {
    handleOffline()
  }
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  
  if (reconnectTimer) {
    clearInterval(reconnectTimer)
  }
})
</script>

<style lang="scss" scoped>
.network-status {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9997;

  .status-content {
    display: flex;
    align-items: center;
    background: #f56c6c;
    color: white;
    border-radius: 8px;
    padding: 12px 20px;
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
    min-width: 300px;

    .status-icon {
      margin-right: 12px;
      font-size: 18px;
      flex-shrink: 0;
    }

    .status-message {
      flex: 1;

      .status-title {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 2px;
      }

      .status-detail {
        font-size: 12px;
        opacity: 0.9;
      }
    }

    .status-indicator {
      margin-left: 12px;
      flex-shrink: 0;

      .indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);

        &.reconnecting {
          animation: pulse 1.5s ease-in-out infinite;
        }
      }
    }
  }
}

.network-slide-enter-active,
.network-slide-leave-active {
  transition: all 0.3s ease;
}

.network-slide-enter-from {
  transform: translateX(-50%) translateY(-100%);
  opacity: 0;
}

.network-slide-leave-to {
  transform: translateX(-50%) translateY(-100%);
  opacity: 0;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}
</style>
