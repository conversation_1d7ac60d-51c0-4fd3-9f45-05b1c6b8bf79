import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { monitoringApi } from '@/api/monitoring'
import type {
  OnlineUser,
  OnlineUserFilters,
  OnlineUserStats,
  UserSession,
  LoginLog,
  LoginLogFilters,
  LoginStats
} from '@/types'

export const useMonitoringStore = defineStore('monitoring', () => {
  // 状态
  const onlineUsers = ref<OnlineUser[]>([])
  const onlineUserStats = ref<OnlineUserStats | null>(null)
  const loginLogs = ref<LoginLog[]>([])
  const loginStats = ref<LoginStats | null>(null)
  const userSessions = ref<UserSession[]>([])
  
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 分页信息
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  // 计算属性
  const onlineUserCount = computed(() => onlineUsers.value.length)
  const onlineUsersByRole = computed(() => {
    const result = { ADMIN: 0, BOSS: 0, EMPLOYEE: 0 }
    onlineUsers.value.forEach(user => {
      result[user.role]++
    })
    return result
  })

  // 获取在线用户列表
  const fetchOnlineUsers = async (filters?: OnlineUserFilters) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.getOnlineUsers(filters)
      
      if (response.success && response.data) {
        onlineUsers.value = response.data.users
      } else {
        throw new Error(response.message || '获取在线用户列表失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取在线用户列表失败'
      ElMessage.error(error.value)
      console.error('获取在线用户列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取在线用户统计
  const fetchOnlineUserStats = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.getOnlineUserStats()
      
      if (response.success && response.data) {
        onlineUserStats.value = response.data
      } else {
        throw new Error(response.message || '获取在线用户统计失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取在线用户统计失败'
      ElMessage.error(error.value)
      console.error('获取在线用户统计失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取用户会话历史
  const fetchUserSessions = async (userId: string) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.getUserSessions(userId)
      
      if (response.success && response.data) {
        userSessions.value = response.data
      } else {
        throw new Error(response.message || '获取用户会话历史失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取用户会话历史失败'
      ElMessage.error(error.value)
      console.error('获取用户会话历史失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 强制用户下线
  const forceUserLogout = async (sessionId: string, reason?: string) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.forceUserLogout(sessionId, reason)
      
      if (response.success) {
        ElMessage.success('用户已强制下线')
        ElNotification({
          title: '强制下线成功',
          message: response.data?.message || '用户已被强制下线',
          type: 'success'
        })
        
        // 刷新在线用户列表
        await fetchOnlineUsers()
      } else {
        throw new Error(response.message || '强制下线失败')
      }
    } catch (err: any) {
      error.value = err.message || '强制下线失败'
      ElMessage.error(error.value)
      console.error('强制下线失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取登录日志
  const fetchLoginLogs = async (filters?: LoginLogFilters) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.getLoginLogs(filters)
      
      if (response.success && response.data) {
        loginLogs.value = response.data.logs
        pagination.value = response.data.pagination
      } else {
        throw new Error(response.message || '获取登录日志失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取登录日志失败'
      ElMessage.error(error.value)
      console.error('获取登录日志失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取登录统计
  const fetchLoginStats = async (days: number = 7) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.getLoginStats(days)
      
      if (response.success && response.data) {
        loginStats.value = response.data
      } else {
        throw new Error(response.message || '获取登录统计失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取登录统计失败'
      ElMessage.error(error.value)
      console.error('获取登录统计失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 清理过期会话
  const cleanupExpiredSessions = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await monitoringApi.cleanupExpiredSessions()
      
      if (response.success) {
        ElMessage.success('过期会话清理完成')
        // 刷新在线用户列表
        await fetchOnlineUsers()
      } else {
        throw new Error(response.message || '清理过期会话失败')
      }
    } catch (err: any) {
      error.value = err.message || '清理过期会话失败'
      ElMessage.error(error.value)
      console.error('清理过期会话失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    onlineUsers.value = []
    onlineUserStats.value = null
    loginLogs.value = []
    loginStats.value = null
    userSessions.value = []
    error.value = null
    pagination.value = {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0
    }
  }

  // 格式化在线时长
  const formatOnlineTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  // 格式化地理位置
  const formatLocation = (user: OnlineUser | LoginLog): string => {
    const parts = []
    if (user.locationCountry && user.locationCountry !== '未知') {
      parts.push(user.locationCountry)
    }
    if (user.locationProvince && user.locationProvince !== '未知') {
      parts.push(user.locationProvince)
    }
    if (user.locationCity && user.locationCity !== '未知') {
      parts.push(user.locationCity)
    }
    if (user.locationIsp && user.locationIsp !== '未知') {
      parts.push(user.locationIsp)
    }
    
    return parts.length > 0 ? parts.join(' ') : '未知位置'
  }

  return {
    // 状态
    onlineUsers,
    onlineUserStats,
    loginLogs,
    loginStats,
    userSessions,
    loading,
    error,
    pagination,
    
    // 计算属性
    onlineUserCount,
    onlineUsersByRole,
    
    // 方法
    fetchOnlineUsers,
    fetchOnlineUserStats,
    fetchUserSessions,
    forceUserLogout,
    fetchLoginLogs,
    fetchLoginStats,
    cleanupExpiredSessions,
    resetState,
    formatOnlineTime,
    formatLocation
  }
})
