<template>
  <el-dialog
    v-model="visible"
    :title="`配置表单字段 - ${gameName}`"
    width="1200px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="form-field-config">
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="action-left">
          <el-button type="primary" @click="showAddDialog = true" :icon="Plus">
            添加字段
          </el-button>
          <el-button @click="refreshFields" :icon="Refresh">刷新</el-button>
        </div>
        <div class="action-center">
          <el-radio-group v-model="statusFilter" size="small">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="active">启用</el-radio-button>
            <el-radio-button label="inactive">禁用</el-radio-button>
          </el-radio-group>
        </div>
        <div class="tips">
          <el-alert
            title="提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              拖拽字段可以调整显示顺序，字段键名一旦创建不建议修改
            </template>
          </el-alert>
        </div>
      </div>

      <!-- 字段列表 -->
      <el-table
        ref="tableRef"
        :data="filteredFieldList"
        row-key="id"
        @sort-change="handleSortChange"
        class="sortable-table"
      >
        <el-table-column type="index" label="序号" width="60" />
        
        <el-table-column prop="sortOrder" label="排序" width="80" sortable="custom">
          <template #default="{ row }">
            <el-input-number
              v-model="row.sortOrder"
              :min="0"
              :max="999"
              size="small"
              @change="handleSortOrderChange(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="fieldLabel" label="字段标签" min-width="120">
          <template #default="{ row }">
            <div class="field-info">
              <div class="field-label">{{ row.fieldLabel }}</div>
              <div class="field-key">{{ row.fieldKey }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="fieldType" label="字段类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getFieldTypeTagType(row.fieldType)">
              {{ getFieldTypeLabel(row.fieldType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="isRequired" label="必填" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isRequired ? 'danger' : 'info'" size="small">
              {{ row.isRequired ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="placeholder" label="占位提示" min-width="150" show-overflow-tooltip />

        <el-table-column label="选项/配置" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.options && row.options.length > 0" class="field-options">
              <el-tag
                v-for="(option, index) in row.options.slice(0, 3)"
                :key="index"
                size="small"
                style="margin-right: 4px; margin-bottom: 2px;"
              >
                {{ option }}
              </el-tag>
              <span v-if="row.options.length > 3" class="more-options">
                等{{ row.options.length }}项
              </span>
            </div>
            <div v-else-if="row.config" class="field-config">
              <el-tag size="small" type="info">有配置</el-tag>
            </div>
            <span v-else class="no-config">无</span>
          </template>
        </el-table-column>

        <el-table-column prop="isActive" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleToggleActive(row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editField(row)" :icon="Edit">
              编辑
            </el-button>
            <el-button size="small" @click="copyField(row)" :icon="CopyDocument">
              复制
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteField(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑字段对话框 -->
    <FieldEditDialog
      v-model="showAddDialog"
      :game-id="gameId"
      :field="currentField"
      @success="handleFieldSaved"
    />

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Edit, Delete, CopyDocument } from '@element-plus/icons-vue'
import {
  getActiveFieldsByGameId,
  getGameFormFields,
  updateGameFormField,
  updateFieldsOrder,
  deleteGameFormField,
  copyFieldToGame,
  type GameFormField,
  type FormFieldType
} from '../api/gameFormFields'
import FieldEditDialog from './FieldEditDialog.vue'
import {
  debounce,
  formatErrorMessage,
  getFieldTypeTagType,
  handleAsyncOperation,
  showConfirmDialog
} from '../utils/common'


// Props
interface Props {
  modelValue: boolean
  gameId: string
  gameName: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const fieldList = ref<GameFormField[]>([])
const showAddDialog = ref(false)
const currentField = ref<GameFormField | null>(null)
const tableRef = ref()
const statusFilter = ref<'all' | 'active' | 'inactive'>('all')



// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 筛选后的字段列表
const filteredFieldList = computed(() => {
  if (statusFilter.value === 'all') {
    return fieldList.value
  }

  return fieldList.value.filter((field: GameFormField) => {
    if (statusFilter.value === 'active') {
      return field.isActive
    } else {
      return !field.isActive
    }
  })
})

// 方法
const loadFields = async () => {
  if (!props.gameId) return

  try {
    loading.value = true
    // 获取所有字段（包括非活跃字段），用于管理界面
    const result = await getGameFormFields({
      gameId: props.gameId,
      limit: 100 // 设置较大的限制以获取所有字段
    })
    fieldList.value = result.items
  } catch (error) {
    console.error('加载字段列表失败:', error)
    ElMessage.error('加载字段列表失败')
  } finally {
    loading.value = false
  }
}

const refreshFields = () => {
  loadFields()
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (prop === 'sortOrder') {
    fieldList.value.sort((a: GameFormField, b: GameFormField) => {
      if (order === 'ascending') {
        return a.sortOrder - b.sortOrder
      } else {
        return b.sortOrder - a.sortOrder
      }
    })
  }
}

const handleSortOrderChange = async (field: GameFormField) => {
  try {
    await updateGameFormField(field.id, { sortOrder: field.sortOrder })
    ElMessage.success('排序更新成功')
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
    loadFields() // 重新加载以恢复原状态
  }
}

const handleToggleActive = async (field: GameFormField) => {
  try {
    await updateGameFormField(field.id, { isActive: field.isActive })
    ElMessage.success(`字段已${field.isActive ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('更新字段状态失败:', error)
    ElMessage.error('更新字段状态失败')
    field.isActive = !field.isActive // 恢复原状态
  }
}

const editField = (field: GameFormField) => {
  currentField.value = field
  showAddDialog.value = true
}

const copyField = async (field: GameFormField) => {
  try {
    const { value: newFieldKey } = await ElMessageBox.prompt(
      '请输入新字段的键名',
      '复制字段',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
        inputErrorMessage: '字段键名只能包含字母、数字和下划线，且必须以字母开头'
      }
    )

    if (!newFieldKey || newFieldKey.trim() === '') {
      ElMessage.warning('字段键名不能为空')
      return
    }

    const result = await handleAsyncOperation(
      () => copyFieldToGame(field.id, {
        targetGameId: props.gameId,
        newFieldKey: newFieldKey.trim()
      }),
      loading,
      `字段 "${field.fieldLabel}" 复制成功`
    )

    if (result) {
      loadFields() // 重新加载字段列表
    }
  } catch (error) {
    // 用户取消操作
    if (error !== 'cancel') {
      console.error('复制字段操作失败:', error)
    }
  }
}

const deleteField = async (field: GameFormField) => {
  const confirmed = await showConfirmDialog(
    `确定要删除字段 "${field.fieldLabel}" 吗？此操作不可恢复！`,
    '确认删除',
    'error'
  )

  if (!confirmed) return

  await handleAsyncOperation(
    () => deleteGameFormField(field.id),
    loading,
    '字段删除成功',
    '删除字段失败'
  )

  loadFields()
}

const handleFieldSaved = () => {
  showAddDialog.value = false
  currentField.value = null
  loadFields()
}

const handleClose = () => {
  visible.value = false
  currentField.value = null
}



// 批量更新排序的防抖函数
const debouncedUpdateOrder = debounce(async (fieldOrders: { id: string; sortOrder: number }[]) => {
  try {
    loading.value = true
    await updateFieldsOrder(props.gameId, fieldOrders)
    ElMessage.success('字段排序更新成功')
  } catch (error: any) {
    console.error('更新排序失败:', error)
    const errorMessage = error?.response?.data?.message || error?.message || '更新排序失败'
    ElMessage.error(errorMessage)
    loadFields() // 恢复原状态
  } finally {
    loading.value = false
  }
}, 500)

// 拖拽排序功能
const initSortable = async () => {
  await nextTick()

  if (!tableRef.value) return

  const tbody = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody')
  if (!tbody) return

  // 动态导入sortablejs
  try {
    const { default: Sortable } = await import('sortablejs') as any

    Sortable.create(tbody, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      onEnd: async (evt: any) => {
        const { oldIndex, newIndex } = evt
        if (oldIndex === newIndex) return

        // 更新本地数据
        const movedItem = fieldList.value.splice(oldIndex!, 1)[0]
        fieldList.value.splice(newIndex!, 0, movedItem)

        // 重新计算sortOrder
        const fieldOrders = fieldList.value.map((field: GameFormField, index: number) => ({
          id: field.id,
          sortOrder: index + 1
        }))

        // 使用防抖的批量更新
        debouncedUpdateOrder(fieldOrders)
      }
    })
  } catch (error) {
    console.warn('Sortable.js 未安装，拖拽排序功能不可用')
  }
}

// 监听器
watch(() => props.modelValue, (newValue: boolean) => {
  if (newValue) {
    loadFields()
    nextTick(() => {
      initSortable()
    })
  }
})
</script>

<style scoped>
.form-field-config {
  min-height: 400px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-left {
  display: flex;
  gap: 8px;
}

.action-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.tips {
  flex: 1;
}

.field-info {
  display: flex;
  flex-direction: column;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-key {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.field-options {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.more-options {
  font-size: 12px;
  color: #909399;
}

.field-config {
  font-size: 12px;
}

.no-config {
  color: #c0c4cc;
  font-size: 12px;
}

/* 拖拽排序样式 */
.sortable-table :deep(.el-table__row) {
  cursor: move;
}

.sortable-ghost {
  opacity: 0.5;
  background-color: #f5f7fa;
}

.sortable-chosen {
  background-color: #ecf5ff;
}

.sortable-drag {
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
