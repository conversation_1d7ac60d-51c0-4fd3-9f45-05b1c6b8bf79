import ExcelJS from 'exceljs';
import { prisma } from '../config/database';
import { OrderStatus, TaskStatus, SettlementStatus, UserRole } from '@prisma/client';
import dayjs from 'dayjs';

export class ExcelExportService {
  
  /**
   * 导出月度结算报表
   */
  async exportMonthlySettlement(year: number, month: number, createdById?: string) {
    const workbook = new ExcelJS.Workbook();
    
    // 设置工作簿属性
    workbook.creator = '王者荣耀代练任务分发管理系统';
    workbook.lastModifiedBy = '系统';
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // 创建工作表
    const worksheet = workbook.addWorksheet(`${year}年${month}月结算报表`);
    
    // 设置列宽
    worksheet.columns = [
      { header: '员工姓名', key: 'employeeName', width: 15 },
      { header: '用户名', key: 'username', width: 15 },
      { header: '订单编号', key: 'orderNo', width: 20 },
      { header: '客户姓名', key: 'customerName', width: 15 },
      { header: '游戏类型', key: 'gameType', width: 15 },
      { header: '任务状态', key: 'taskStatus', width: 12 },
      { header: '开始时间', key: 'startTime', width: 20 },
      { header: '完成时间', key: 'endTime', width: 20 },
      { header: '实际工时', key: 'actualHours', width: 12 },
      { header: '佣金金额', key: 'commission', width: 12 },
      { header: '结算状态', key: 'settlementStatus', width: 12 },
      { header: '结算时间', key: 'settledAt', width: 20 }
    ];
    
    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    
    // 查询数据
    const startDate = dayjs(`${year}-${month}-01`).startOf('month').toDate();
    const endDate = dayjs(`${year}-${month}-01`).endOf('month').toDate();
    
    const whereCondition: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    };
    
    if (createdById) {
      whereCondition.task = {
        order: {
          createdById
        }
      };
    }
    
    const settlements = await prisma.settlement.findMany({
      where: whereCondition,
      include: {
        user: {
          select: {
            username: true,
            nickname: true
          }
        },
        task: {
          include: {
            order: {
              select: {
                orderNo: true,
                customerName: true,
                gameType: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });
    
    // 添加数据行
    settlements.forEach((settlement, index) => {
      const row = worksheet.addRow({
        employeeName: settlement.user.nickname || settlement.user.username,
        username: settlement.user.username,
        orderNo: settlement.task.order.orderNo,
        customerName: settlement.task.order.customerName,
        gameType: settlement.task.order.gameType,
        taskStatus: this.getTaskStatusText(settlement.task.status),
        startTime: settlement.task.startTime ? dayjs(settlement.task.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
        endTime: settlement.task.endTime ? dayjs(settlement.task.endTime).format('YYYY-MM-DD HH:mm:ss') : '',
        actualHours: settlement.task.actualHours || 0,
        commission: settlement.amount,
        settlementStatus: this.getSettlementStatusText(settlement.status),
        settledAt: settlement.settledAt ? dayjs(settlement.settledAt).format('YYYY-MM-DD HH:mm:ss') : ''
      });
      
      // 设置行样式
      if (index % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F2F2F2' }
        };
      }
      
      // 设置数字格式
      row.getCell('commission').numFmt = '¥#,##0.00';
    });
    
    // 添加统计行
    const totalCommission = settlements.reduce((sum, s) => sum + s.amount, 0);
    const completedCount = settlements.filter(s => s.status === SettlementStatus.COMPLETED).length;
    
    worksheet.addRow({});
    const summaryRow = worksheet.addRow({
      employeeName: '统计汇总',
      commission: totalCommission,
      settlementStatus: `已结算: ${completedCount}笔`
    });
    
    summaryRow.font = { bold: true };
    summaryRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFF00' }
    };
    summaryRow.getCell('commission').numFmt = '¥#,##0.00';
    
    return workbook;
  }
  
  /**
   * 导出员工个人收益明细
   */
  async exportEmployeeEarnings(employeeId: string, startDate: Date, endDate: Date) {
    const workbook = new ExcelJS.Workbook();
    
    // 获取员工信息
    const employee = await prisma.user.findUnique({
      where: { id: employeeId },
      select: {
        username: true,
        nickname: true,
        totalEarnings: true
      }
    });
    
    if (!employee) {
      throw new Error('员工不存在');
    }
    
    const worksheet = workbook.addWorksheet(`${employee.nickname || employee.username}收益明细`);
    
    // 设置列
    worksheet.columns = [
      { header: '日期', key: 'date', width: 15 },
      { header: '订单编号', key: 'orderNo', width: 20 },
      { header: '客户姓名', key: 'customerName', width: 15 },
      { header: '游戏类型', key: 'gameType', width: 15 },
      { header: '任务描述', key: 'taskDescription', width: 30 },
      { header: '工时', key: 'actualHours', width: 10 },
      { header: '佣金', key: 'commission', width: 12 },
      { header: '状态', key: 'status', width: 12 }
    ];
    
    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '70AD47' }
    };
    
    // 查询数据
    const settlements = await prisma.settlement.findMany({
      where: {
        userId: employeeId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        task: {
          include: {
            order: {
              select: {
                orderNo: true,
                customerName: true,
                gameType: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // 添加数据
    settlements.forEach(settlement => {
      worksheet.addRow({
        date: dayjs(settlement.createdAt).format('YYYY-MM-DD'),
        orderNo: settlement.task.order.orderNo,
        customerName: settlement.task.order.customerName,
        gameType: settlement.task.order.gameType,
        taskDescription: settlement.task.description || '代练任务',
        actualHours: settlement.task.actualHours || 0,
        commission: settlement.amount,
        status: this.getSettlementStatusText(settlement.status)
      });
    });
    
    // 添加统计
    const totalCommission = settlements.reduce((sum, s) => sum + s.amount, 0);
    worksheet.addRow({});
    worksheet.addRow({
      date: '总计',
      commission: totalCommission
    });
    
    return workbook;
  }
  
  /**
   * 导出订单完成情况汇总
   */
  async exportOrderSummary(startDate: Date, endDate: Date, createdById?: string) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('订单完成情况汇总');
    
    // 设置列
    worksheet.columns = [
      { header: '订单编号', key: 'orderNo', width: 20 },
      { header: '客户姓名', key: 'customerName', width: 15 },
      { header: '联系方式', key: 'customerContact', width: 15 },
      { header: '游戏类型', key: 'gameType', width: 15 },
      { header: '订单金额', key: 'price', width: 12 },
      { header: '订单状态', key: 'status', width: 12 },
      { header: '负责员工', key: 'assigneeName', width: 15 },
      { header: '创建时间', key: 'createdAt', width: 20 },
      { header: '完成时间', key: 'completedAt', width: 20 },
      { header: '特殊要求', key: 'requirements', width: 30 }
    ];
    
    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E74C3C' }
    };
    
    // 查询数据
    const whereCondition: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    };
    
    if (createdById) {
      whereCondition.createdById = createdById;
    }
    
    const orders = await prisma.order.findMany({
      where: whereCondition,
      include: {
        tasks: {
          include: {
            assignee: {
              select: {
                username: true,
                nickname: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // 添加数据
    orders.forEach(order => {
      const mainTask = order.tasks[0]; // 主要任务
      const assigneeName = mainTask?.assignee ? 
        (mainTask.assignee.nickname || mainTask.assignee.username) : '未分配';
      
      worksheet.addRow({
        orderNo: order.orderNo,
        customerName: order.customerName,
        customerContact: order.customerContact || '',
        gameType: order.gameType,
        price: order.price,
        status: this.getOrderStatusText(order.status),
        assigneeName,
        createdAt: dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        completedAt: order.status === OrderStatus.COMPLETED ? 
          dayjs(order.updatedAt).format('YYYY-MM-DD HH:mm:ss') : '',
        requirements: order.requirements || ''
      });
    });
    
    return workbook;
  }
  
  // 辅助方法
  private getTaskStatusText(status: TaskStatus): string {
    const statusMap: Record<TaskStatus, string> = {
      [TaskStatus.PENDING]: '待处理',
      [TaskStatus.ACCEPTED]: '已接受',
      [TaskStatus.IN_PROGRESS]: '进行中',
      [TaskStatus.PAUSED]: '已暂停',
      [TaskStatus.SUBMITTED]: '已提交',
      [TaskStatus.APPROVED]: '已审核',
      [TaskStatus.REJECTED]: '已拒绝',
      [TaskStatus.COMPLETED]: '已完成',
      [TaskStatus.CANCELLED]: '已取消'
    };
    return statusMap[status] || status;
  }
  
  private getSettlementStatusText(status: SettlementStatus): string {
    const statusMap: Record<SettlementStatus, string> = {
      [SettlementStatus.PENDING]: '待结算',
      [SettlementStatus.COMPLETED]: '已结算',
      [SettlementStatus.CANCELLED]: '已取消'
    };
    return statusMap[status] || status;
  }
  
  private getOrderStatusText(status: OrderStatus): string {
    const statusMap: Record<OrderStatus, string> = {
      [OrderStatus.PENDING]: '待处理',
      [OrderStatus.ASSIGNED]: '已分配',
      [OrderStatus.IN_PROGRESS]: '进行中',
      [OrderStatus.COMPLETED]: '已完成',
      [OrderStatus.CANCELLED]: '已取消'
    };
    return statusMap[status] || status;
  }
}
