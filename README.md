# 王者荣耀代练任务分发管理系统

## 项目概述

本系统是针对王者荣耀游戏代练业务的内部任务分发管理系统，支持老板接单后的两种分发模式：直接分配和系统挂单。

## 技术栈

### 前端
- Vue.js 3 + TypeScript
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 4 路由
- Vite 构建工具
- Sass/SCSS CSS预处理器
- Axios HTTP客户端
- Socket.IO Client 实时通信

### 后端
- Node.js 18+
- Express.js Web框架
- TypeScript 开发语言
- Prisma ORM
- MySQL 8.0+ 数据库
- Redis 7+ 缓存
- Socket.IO 实时通信
- JWT + Passport.js 身份认证
- Multer 文件上传
- Bull Queue 任务队列

### 开发工具与部署
- Git 版本控制
- ESLint + Prettier 代码规范
- Swagger/OpenAPI API文档
- Docker + Docker Compose 容器化
- Nginx 反向代理
- PM2 进程管理
- Winston 日志管理

## 项目结构

```
├── frontend/          # 前端项目
├── backend/           # 后端项目
├── database/          # 数据库相关文件
├── docker/            # Docker配置文件
├── docs/              # 项目文档
└── scripts/           # 部署和工具脚本
```

## 核心功能

1. **订单管理**：接收和管理王者荣耀代练订单
2. **任务分发**：支持直接分配和系统挂单两种模式
3. **员工管理**：员工接单、任务执行、进度跟踪
4. **审核结算**：任务审核和佣金结算
5. **实时通信**：任务状态实时更新和通知

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Redis 7+
- Docker (可选)

### 安装依赖
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 启动开发环境
```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务
cd frontend
npm run dev
```

## 开发指南

详细的开发指南请参考 `docs/` 目录下的相关文档。

## 许可证

本项目为内部使用系统，版权所有。

# 启动prisma studio
cd backend
npx prisma studio

# 终结3000占用端口
netstat -ano | findstr :3000
taskkill /PID 实际端口 /F

以下是报错内容，请从根源修复完善完整，不要遗留后续bug问题：