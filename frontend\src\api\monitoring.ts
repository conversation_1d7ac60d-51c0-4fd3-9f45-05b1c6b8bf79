import { request } from './http'
import type {
  ApiResponse,
  OnlineUser,
  OnlineUserFilters,
  OnlineUserStats,
  UserSession,
  LoginLog,
  LoginLogFilters,
  LoginStats
} from '@/types'

// 在线用户监控API
export const monitoringApi = {
  // 获取在线用户列表
  async getOnlineUsers(filters?: OnlineUserFilters): Promise<ApiResponse<{
    users: OnlineUser[]
    total: number
    filters: OnlineUserFilters
  }>> {
    const params = new URLSearchParams()
    if (filters?.role) params.append('role', filters.role)
    if (filters?.ipAddress) params.append('ipAddress', filters.ipAddress)
    if (filters?.locationProvince) params.append('locationProvince', filters.locationProvince)
    if (filters?.locationCity) params.append('locationCity', filters.locationCity)
    if (filters?.minOnlineTime) params.append('minOnlineTime', filters.minOnlineTime.toString())
    
    const queryString = params.toString()
    const url = queryString ? `/monitoring/online?${queryString}` : '/monitoring/online'
    
    return request.get(url)
  },

  // 获取在线用户统计
  async getOnlineUserStats(): Promise<ApiResponse<OnlineUserStats>> {
    return request.get('/monitoring/online/stats')
  },

  // 获取用户会话历史
  async getUserSessions(userId: string): Promise<ApiResponse<UserSession[]>> {
    return request.get(`/monitoring/sessions/${userId}`)
  },

  // 强制用户下线
  async forceUserLogout(sessionId: string, reason?: string): Promise<ApiResponse<{ message: string }>> {
    return request.post(`/monitoring/sessions/${sessionId}/force-logout`, { reason })
  },

  // 获取登录日志
  async getLoginLogs(filters?: LoginLogFilters): Promise<ApiResponse<{
    logs: LoginLog[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    filters: LoginLogFilters
  }>> {
    const params = new URLSearchParams()
    if (filters?.userId) params.append('userId', filters.userId)
    if (filters?.username) params.append('username', filters.username)
    if (filters?.ipAddress) params.append('ipAddress', filters.ipAddress)
    if (filters?.loginResult) params.append('loginResult', filters.loginResult)
    if (filters?.startDate) params.append('startDate', filters.startDate)
    if (filters?.endDate) params.append('endDate', filters.endDate)
    if (filters?.locationProvince) params.append('locationProvince', filters.locationProvince)
    if (filters?.locationCity) params.append('locationCity', filters.locationCity)
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.limit) params.append('limit', filters.limit.toString())
    
    const queryString = params.toString()
    const url = queryString ? `/monitoring/login-logs?${queryString}` : '/monitoring/login-logs'
    
    return request.get(url)
  },

  // 获取登录统计
  async getLoginStats(days: number = 7): Promise<ApiResponse<LoginStats>> {
    return request.get(`/monitoring/login-stats?days=${days}`)
  },

  // 清理过期会话
  async cleanupExpiredSessions(): Promise<ApiResponse<{ message: string }>> {
    return request.post('/monitoring/cleanup-sessions')
  },

  // 获取IP地理位置信息
  async getIpLocation(ip: string): Promise<ApiResponse<{
    ip: string
    country: string
    province: string
    city: string
    isp: string
  }>> {
    return request.get(`/monitoring/ip-location/${ip}`)
  }
}

export default monitoringApi
