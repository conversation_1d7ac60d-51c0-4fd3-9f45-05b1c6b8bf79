<template>
  <el-dialog
    v-model="visible"
    title="编辑任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="关联订单" prop="orderId">
        <el-select 
          v-model="form.orderId" 
          placeholder="请选择关联订单" 
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="order in orders"
            :key="order.id"
            :label="`${order.orderNo} - ${order.customerName}`"
            :value="order.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分配员工" prop="assigneeId">
        <el-select 
          v-model="form.assigneeId" 
          placeholder="请选择分配员工（可选）" 
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="employee in employees"
            :key="employee.id"
            :label="`${employee.nickname || employee.username} (等级${employee.level})`"
            :value="employee.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分配方式" prop="assignType">
        <el-radio-group v-model="form.assignType">
          <el-radio value="SYSTEM">系统挂单</el-radio>
          <el-radio value="DIRECT">直接分配</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="预计工时" prop="estimatedHours">
        <el-input-number
          v-model="form.estimatedHours"
          :min="0"
          placeholder="请输入预计工时（小时）"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="佣金" prop="commission">
        <el-input-number
          v-model="form.commission"
          :min="0"
          :precision="2"
          :disabled="isCommissionReadonly"
          placeholder="请输入佣金"
          style="width: 100%"
        />
        <div v-if="isCommissionReadonly" class="form-tip readonly-tip">
          <el-icon><InfoFilled /></el-icon>
          已审核任务的佣金不可修改，如需调整请联系管理员
        </div>
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述（可选）"
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import { orderApi } from '@/api/orders'
import { userApi } from '@/api/users'
import type { Task, Order, User, AssignType } from '@/types'

// Props
interface Props {
  modelValue: boolean
  taskId: string | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const orders = ref<Order[]>([])
const employees = ref<User[]>([])

// 表单数据
const form = reactive({
  orderId: '',
  assigneeId: '',
  assignType: 'SYSTEM' as AssignType,
  estimatedHours: 0,
  commission: 0,
  description: '',
  notes: ''
})

// 任务状态
const taskStatus = ref<string>('')

// 表单验证规则
const rules: FormRules = {
  orderId: [
    { required: true, message: '请选择关联订单', trigger: 'change' }
  ],
  assignType: [
    { required: true, message: '请选择分配方式', trigger: 'change' }
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.taskId) {
    fetchTaskDetail()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 监听分配方式变化
watch(() => form.assignType, (newVal) => {
  if (newVal === 'SYSTEM') {
    form.assigneeId = ''
  }
})

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders()
  fetchEmployees()
})

// 计算佣金字段是否只读
const isCommissionReadonly = computed(() => {
  return taskStatus.value === 'APPROVED'
})

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!props.taskId) return

  try {
    loading.value = true
    const response = await taskApi.getTaskById(props.taskId)

    if (response.success && response.data) {
      const task = response.data
      // 保存任务状态
      taskStatus.value = task.status

      Object.assign(form, {
        orderId: task.orderId,
        assigneeId: task.assigneeId || '',
        assignType: task.assignType,
        estimatedHours: task.estimatedHours || 0,
        commission: task.commission || 0,
        description: task.description || '',
        notes: task.notes || ''
      })
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  } finally {
    loading.value = false
  }
}

// 获取订单列表
const fetchOrders = async () => {
  try {
    const response = await orderApi.getOrders({
      limit: 100
    })
    
    if (response.success && response.data) {
      orders.value = response.data.items
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
  }
}

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await userApi.getUsers({
      role: 'EMPLOYEE',
      status: 'ACTIVE',
      limit: 100
    })
    
    if (response.success && response.data) {
      employees.value = response.data.items
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.taskId) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true

      // 过滤掉空字符串参数，只保留有效值
      const filteredData: any = {}
      Object.entries(form).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          filteredData[key] = value
        }
      })

      const response = await taskApi.updateTask(props.taskId, filteredData)
      if (response.success) {
        ElMessage.success('任务更新成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('更新任务失败:', error)
    ElMessage.error('更新任务失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    orderId: '',
    assigneeId: '',
    assignType: 'SYSTEM',
    estimatedHours: 0,
    commission: 0,
    description: '',
    notes: ''
  })
  // 重置任务状态
  taskStatus.value = ''
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;

  &.readonly-tip {
    color: #e6a23c;
    display: flex;
    align-items: center;
    gap: 4px;

    .el-icon {
      font-size: 14px;
    }
  }
}
</style>
