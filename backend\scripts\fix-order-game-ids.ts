/**
 * 修复订单数据：为没有gameId但有gameType的订单补充gameId
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixOrderGameIds() {
  console.log('开始修复订单gameId数据...');

  try {
    // 1. 获取所有游戏的映射关系
    const games = await prisma.game.findMany({
      select: {
        id: true,
        name: true,
        displayName: true,
      },
    });

    console.log('找到游戏:', games);

    // 创建gameType到gameId的映射
    const gameTypeToIdMap = {};
    games.forEach(game => {
      gameTypeToIdMap[game.name] = game.id;
    });

    console.log('游戏类型映射:', gameTypeToIdMap);

    // 2. 查找需要修复的订单（有gameType但没有gameId）
    const ordersToFix = await prisma.order.findMany({
      where: {
        gameId: null,
        gameType: {
          not: null,
        },
      },
      select: {
        id: true,
        orderNo: true,
        gameType: true,
        gameId: true,
      },
    });

    console.log(`找到需要修复的订单数量: ${ordersToFix.length}`);

    // 3. 修复每个订单
    let fixedCount = 0;
    for (const order of ordersToFix) {
      const gameId = gameTypeToIdMap[order.gameType];
      
      if (gameId) {
        console.log(`修复订单 ${order.orderNo}: gameType=${order.gameType} -> gameId=${gameId}`);
        
        await prisma.order.update({
          where: { id: order.id },
          data: { gameId },
        });
        
        fixedCount++;
      } else {
        console.warn(`订单 ${order.orderNo} 的gameType "${order.gameType}" 找不到对应的游戏`);
      }
    }

    console.log(`修复完成！成功修复 ${fixedCount} 个订单`);

  } catch (error) {
    console.error('修复过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行修复
if (require.main === module) {
  fixOrderGameIds();
}

module.exports = { fixOrderGameIds };
