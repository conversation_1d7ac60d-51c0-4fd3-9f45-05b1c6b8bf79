<template>
  <div class="settlements-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>结算管理</h1>
      <div class="header-actions">
        <!-- Excel导出按钮 -->
        <QuickExportButtons />
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="结算状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px;">
            <el-option label="待结算" value="PENDING" />
            <el-option label="已结算" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="员工">
          <el-select v-model="searchForm.userId" placeholder="全部员工" clearable style="width: 150px;">
            <el-option 
              v-for="employee in employees" 
              :key="employee.id" 
              :label="employee.nickname || employee.username" 
              :value="employee.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input v-model="searchForm.keyword" placeholder="任务编号" style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 结算列表 -->
    <el-card>
      <el-table :data="settlements" v-loading="loading" stripe>
        <el-table-column prop="id" label="结算ID" width="120" />
        <el-table-column label="任务信息" width="200">
          <template #default="{ row }">
            <div>
              <div class="task-no">{{ row.task?.taskNo }}</div>
              <div class="order-info">{{ row.task?.order?.customerName }}</div>
            </div></template>
        </el-table-column>
        <el-table-column label="员工" width="120">
          <template #default="{ row }">
            {{ row.user?.nickname || row.user?.username }}
          </template>
        </el-table-column>
        <el-table-column label="结算金额" width="120" align="center">
          <template #default="{ row }">
            <span class="amount">¥{{ row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="结算时间" width="160">
          <template #default="{ row }">
            {{ row.settledAt ? formatDate(row.settledAt) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" min-width="150">
          <template #default="{ row }">
            {{ row.notes || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'PENDING'" 
              type="primary" 
              size="small" 
              @click="handleSettle(row)"
            >
              结算
            </el-button>
            <el-button 
              v-if="row.status === 'PENDING'" 
              type="danger" 
              size="small" 
              @click="handleCancel(row)"
            >
              取消
            </el-button>
            <el-button size="small" @click="handleViewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 结算对话框 -->
    <el-dialog v-model="settleDialogVisible" title="确认结算" width="400px">
      <div class="settle-info">
        <p><strong>任务编号：</strong>{{ currentSettlement?.task?.taskNo }}</p>
        <p><strong>员工：</strong>{{ currentSettlement?.user?.nickname || currentSettlement?.user?.username }}</p>
        <p><strong>结算金额：</strong>¥{{ currentSettlement?.amount }}</p>
      </div>
      <el-form :model="settleForm" label-width="80px">
        <el-form-item label="备注">
          <el-input 
            v-model="settleForm.notes" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入结算备注（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="settleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSettle" :loading="settling">
          确认结算
        </el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="结算详情" width="600px">
      <div v-if="currentSettlement" class="settlement-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="结算ID">{{ currentSettlement.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentSettlement.status)">
              {{ getStatusText(currentSettlement.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务编号">{{ currentSettlement.task?.taskNo }}</el-descriptions-item>
          <el-descriptions-item label="订单客户">{{ currentSettlement.task?.order?.customerName }}</el-descriptions-item>
          <el-descriptions-item label="员工">{{ currentSettlement.user?.nickname || currentSettlement.user?.username }}</el-descriptions-item>
          <el-descriptions-item label="结算金额">¥{{ currentSettlement.amount }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentSettlement.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{ currentSettlement.settledAt ? formatDate(currentSettlement.settledAt) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentSettlement.notes || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { settlementApi } from '@/api/settlements'
import { userApi } from '@/api/users'
import QuickExportButtons from '@/components/ExcelExport/QuickExportButtons.vue'
import type { Settlement, User } from '@/types'

// 响应式数据
const loading = ref(false)
const settling = ref(false)
const settlements = ref<Settlement[]>([])
const employees = ref<User[]>([])
const settleDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentSettlement = ref<Settlement | null>(null)

const searchForm = ref({
  status: '',
  userId: '',
  keyword: ''
})

const settleForm = ref({
  notes: ''
})

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0
})

// 获取结算列表
const fetchSettlements = async () => {
  try {
    loading.value = true

    // 过滤掉空字符串参数
    const queryParams = Object.fromEntries(
      Object.entries(searchForm.value).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )

    const response = await settlementApi.getSettlements({
      ...queryParams,
      page: pagination.value.page,
      limit: pagination.value.limit
    })

    if (response.success && response.data) {
      settlements.value = response.data.items
      pagination.value.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取结算列表失败:', error)
    ElMessage.error('获取结算列表失败')
  } finally {
    loading.value = false
  }
}

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await userApi.getUsers({ role: 'EMPLOYEE', limit: 100 })
    if (response.success && response.data) {
      employees.value = response.data.items
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
  }
}

// 刷新数据
const refreshData = () => {
  fetchSettlements()
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  fetchSettlements()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm.value, {
    status: '',
    userId: '',
    keyword: ''
  })
  pagination.value.page = 1
  fetchSettlements()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.limit = size
  pagination.value.page = 1
  fetchSettlements()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchSettlements()
}

// 结算处理
const handleSettle = (settlement: Settlement) => {
  currentSettlement.value = settlement
  settleForm.value.notes = ''
  settleDialogVisible.value = true
}

// 确认结算
const confirmSettle = async () => {
  if (!currentSettlement.value) return

  try {
    settling.value = true
    const response = await settlementApi.settleSettlement(currentSettlement.value.id, {
      notes: settleForm.value.notes
    })

    if (response.success) {
      ElMessage.success('结算成功')
      settleDialogVisible.value = false
      fetchSettlements()
    }
  } catch (error) {
    console.error('结算失败:', error)
    ElMessage.error('结算失败')
  } finally {
    settling.value = false
  }
}

// 取消结算
const handleCancel = async (settlement: Settlement) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消结算 ${settlement.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await settlementApi.cancelSettlement(settlement.id)
    if (response.success) {
      ElMessage.success('取消成功')
      fetchSettlements()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消结算失败:', error)
      ElMessage.error('取消结算失败')
    }
  }
}

// 查看详情
const handleViewDetails = (settlement: Settlement) => {
  currentSettlement.value = settlement
  detailDialogVisible.value = true
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'COMPLETED': return 'success'
    case 'CANCELLED': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '待结算'
    case 'COMPLETED': return '已结算'
    case 'CANCELLED': return '已取消'
    default: return status
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 页面挂载
onMounted(() => {
  fetchEmployees()
  fetchSettlements()
})
</script>

<style lang="scss" scoped>
.settlements-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .task-no {
    font-weight: 600;
    color: #303133;
  }

  .order-info {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }

  .amount {
    font-weight: 600;
    color: #67C23A;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .settle-info {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;

    p {
      margin: 8px 0;
      color: #606266;
    }
  }

  .settlement-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
