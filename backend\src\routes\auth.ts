import { Router } from 'express';
import {
  login,
  register,
  getCurrentUser,
  updatePassword,
  logout,
} from '../controllers/authController';
import { authenticateToken } from '../middleware/auth';
import { validate } from '../utils/validation';
import { authSchemas } from '../utils/validation';

const router = Router();

// 用户登录
router.post('/login', validate(authSchemas.login), login);

// 用户注册
router.post('/register', validate(authSchemas.register), register);

// 获取当前用户信息（需要认证）
router.get('/me', authenticateToken, getCurrentUser);

// 更新密码（需要认证）
router.put('/password', authenticateToken, validate(authSchemas.updatePassword), updatePassword);



// 用户登出（需要认证）
router.post('/logout', authenticateToken, logout);

export default router;
