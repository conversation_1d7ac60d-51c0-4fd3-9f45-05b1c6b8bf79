version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: game-boost-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: game_boost_db
      MYSQL_USER: gameuser
      MYSQL_PASSWORD: gamepass123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - game-boost-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: game-boost-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - game-boost-network

  # 后端API服务
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile
    container_name: game-boost-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://gameuser:gamepass123@mysql:3306/game_boost_db
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-here
      PORT: 3000
    volumes:
      - ../backend/uploads:/app/uploads
      - ../backend/logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - game-boost-network

  # 前端Web服务
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/frontend/Dockerfile
    container_name: game-boost-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - game-boost-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: game-boost-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - frontend
      - backend
    networks:
      - game-boost-network

volumes:
  mysql_data:
  redis_data:

networks:
  game-boost-network:
    driver: bridge
