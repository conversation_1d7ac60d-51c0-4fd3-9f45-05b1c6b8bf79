import { PrismaClient, UserRole, OrderStatus, OrderPriority, AssignType, TaskStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { seedMultiGameData } from './seeds/multi-game-seed';

const prisma = new PrismaClient();

async function main() {
  console.log('开始初始化数据库...');

  // 清理现有数据（开发环境）
  if (process.env.NODE_ENV === 'development') {
    await prisma.settlement.deleteMany();
    await prisma.taskProgress.deleteMany();
    await prisma.task.deleteMany();
    await prisma.order.deleteMany();
    await prisma.user.deleteMany();
    console.log('已清理现有数据');
  }

  // 创建默认用户
  const hashedPassword = await bcrypt.hash('123456', 10);

  // 创建管理员
  const admin = await prisma.user.create({
    data: {
      username: 'admin',
      password: hashedPassword,
      nickname: '系统管理员',
      role: UserRole.ADMIN,
    },
  });

  // 创建老板账号
  const boss = await prisma.user.create({
    data: {
      username: 'boss',
      password: hashedPassword,
      nickname: '老板',
      role: UserRole.BOSS,
    },
  });

  // 创建员工账号
  const employees = await Promise.all([
    prisma.user.create({
      data: {
        username: 'employee1',
        password: hashedPassword,
        nickname: '代练员工1',
        role: UserRole.EMPLOYEE,
        level: 3,
      },
    }),
    prisma.user.create({
      data: {
        username: 'employee2',
        password: hashedPassword,
        nickname: '代练员工2',
        role: UserRole.EMPLOYEE,
        level: 2,
      },
    }),
    prisma.user.create({
      data: {
        username: 'employee3',
        password: hashedPassword,
        nickname: '代练员工3',
        role: UserRole.EMPLOYEE,
        level: 4,
      },
    }),
  ]);

  console.log('已创建用户:', { admin: admin.id, boss: boss.id, employees: employees.map(e => e.id) });

  // 创建示例订单
  const orders = await Promise.all([
    prisma.order.create({
      data: {
        orderNo: 'ORD001',
        customerName: '张三',
        customerContact: '***********',
        gameAccount: 'zhangsan123',
        gamePassword: 'password123',
        gameType: '王者荣耀',
        currentRank: '黄金III',
        targetRank: '铂金IV',
        price: 150.00,
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        requirements: '要求胜率保持在70%以上',
        status: OrderStatus.PENDING,
        priority: OrderPriority.NORMAL,
        createdById: boss.id,
      },
    }),
    prisma.order.create({
      data: {
        orderNo: 'ORD002',
        customerName: '李四',
        customerContact: '***********',
        gameAccount: 'lisi456',
        gamePassword: 'password456',
        gameType: '英雄联盟',
        currentRank: '白银I',
        targetRank: '黄金III',
        price: 200.00,
        deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5天后
        requirements: '需要保持KDA在2.0以上',
        status: OrderStatus.ASSIGNED,
        priority: OrderPriority.HIGH,
        createdById: boss.id,
      },
    }),
    prisma.order.create({
      data: {
        orderNo: 'ORD003',
        customerName: '王五',
        customerContact: '***********',
        gameAccount: 'wangwu789',
        gamePassword: 'password789',
        gameType: '和平精英',
        currentRank: '铂金II',
        targetRank: '钻石V',
        price: 300.00,
        deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10天后
        requirements: '只能在晚上8点后进行游戏',
        status: OrderStatus.PENDING,
        priority: OrderPriority.URGENT,
        createdById: boss.id,
      },
    }),
  ]);

  console.log('已创建订单:', orders.map(o => o.id));

  // 创建示例任务
  const tasks = await Promise.all([
    // 直接分配的任务
    prisma.task.create({
      data: {
        taskNo: 'TASK001',
        orderId: orders[1].id,
        assigneeId: employees[0].id,
        assignType: AssignType.DIRECT,
        status: TaskStatus.IN_PROGRESS,
        startTime: new Date(),
        estimatedHours: 20,
        actualHours: 12, // 添加已进行的工时
        commission: 80.00,
        description: '黄金段位代练任务',
      },
    }),
    // 系统挂单任务
    prisma.task.create({
      data: {
        taskNo: 'TASK002',
        orderId: orders[0].id,
        assignType: AssignType.SYSTEM,
        status: TaskStatus.PENDING,
        estimatedHours: 15,
        commission: 60.00,
        description: '铂金段位代练任务',
      },
    }),
    prisma.task.create({
      data: {
        taskNo: 'TASK003',
        orderId: orders[2].id,
        assignType: AssignType.SYSTEM,
        status: TaskStatus.PENDING,
        estimatedHours: 30,
        commission: 120.00,
        description: '钻石段位代练任务',
      },
    }),
  ]);

  console.log('已创建任务:', tasks.map(t => t.id));

  // 为进行中的任务创建进度记录
  await prisma.taskProgress.create({
    data: {
      taskId: tasks[0].id,
      userId: employees[0].id,
      progress: 30,
      currentRank: '白银II',
      description: '已完成3场排位赛，当前胜率75%',
      screenshots: JSON.stringify(['screenshot1.jpg', 'screenshot2.jpg']),
    },
  });

  console.log('已创建任务进度记录');

  // 创建一个已完成的任务和相应的结算记录
  const completedTask = await prisma.task.create({
    data: {
      taskNo: 'TASK004',
      orderId: orders[0].id,
      assigneeId: employees[1].id,
      assignType: AssignType.DIRECT,
      status: TaskStatus.APPROVED,
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前开始
      endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前完成
      estimatedHours: 25,
      actualHours: 22, // 添加实际工时
      commission: 100.00,
      description: '铂金段位代练任务（已完成）',
    },
  });

  // 为已完成的任务创建结算记录
  const settlement1 = await prisma.settlement.create({
    data: {
      taskId: completedTask.id,
      userId: employees[1].id,
      amount: 100.00,
      status: 'PENDING',
    },
  });

  // 创建另一个已结算的任务
  const settledTask = await prisma.task.create({
    data: {
      taskNo: 'TASK005',
      orderId: orders[2].id,
      assigneeId: employees[2].id,
      assignType: AssignType.DIRECT,
      status: TaskStatus.APPROVED,
      startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10天前开始
      endTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前完成
      estimatedHours: 35,
      actualHours: 32, // 添加实际工时
      commission: 150.00,
      description: '钻石段位代练任务（已结算）',
    },
  });

  // 创建已结算的记录
  const settlement2 = await prisma.settlement.create({
    data: {
      taskId: settledTask.id,
      userId: employees[2].id,
      amount: 150.00,
      status: 'COMPLETED',
      settledAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前结算
      notes: '任务完成质量优秀，按时结算',
    },
  });

  // 创建员工1的已完成任务
  const employee1CompletedTask = await prisma.task.create({
    data: {
      taskNo: 'TASK006',
      orderId: orders[0].id,
      assigneeId: employees[0].id,
      assignType: AssignType.DIRECT,
      status: TaskStatus.APPROVED,
      startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5天前开始
      endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前完成
      estimatedHours: 18,
      actualHours: 16, // 实际工时
      commission: 75.00,
      description: '白银段位代练任务（已完成）',
    },
  });

  // 为员工1创建结算记录
  const settlement3 = await prisma.settlement.create({
    data: {
      taskId: employee1CompletedTask.id,
      userId: employees[0].id,
      amount: 75.00,
      status: 'COMPLETED',
      settledAt: new Date(),
      notes: '任务完成及时，质量良好',
    },
  });

  // 更新员工的总收益
  await prisma.user.update({
    where: { id: employees[0].id },
    data: {
      totalEarnings: 75.00,
    },
  });

  await prisma.user.update({
    where: { id: employees[1].id },
    data: {
      totalEarnings: 100.00,
    },
  });

  await prisma.user.update({
    where: { id: employees[2].id },
    data: {
      totalEarnings: 150.00,
    },
  });

  console.log('已创建结算记录:', [settlement1.id, settlement2.id, settlement3.id]);

  // 初始化多游戏数据
  console.log('🎮 初始化多游戏支持数据...');
  await seedMultiGameData();

  console.log('数据库初始化完成！');
  console.log('默认账号信息:');
  console.log('管理员: admin / 123456');
  console.log('老板: boss / 123456');
  console.log('员工1: employee1 / 123456');
  console.log('员工2: employee2 / 123456');
  console.log('员工3: employee3 / 123456');
}

main()
  .catch((e) => {
    console.error('数据库初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
