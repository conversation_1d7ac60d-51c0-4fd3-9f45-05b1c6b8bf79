-- CreateTable
CREATE TABLE `user_sessions` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `sessionToken` VARCHAR(191) NOT NULL,
    `socketId` VARCHAR(191) NULL,
    `ipAddress` VARCHAR(191) NOT NULL,
    `userAgent` TEXT NULL,
    `loginTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `lastActivity` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `logoutTime` DATETIME(3) NULL,
    `status` ENUM('ACTIVE', 'INACTIVE', 'FORCED_LOGOUT') NOT NULL DEFAULT 'ACTIVE',
    `deviceInfo` TEXT NULL,
    `locationCountry` VARCHAR(191) NULL,
    `locationRegion` VARCHAR(191) NULL,
    `locationProvince` VARCHAR(191) NULL,
    `locationCity` VARCHAR(191) NULL,
    `locationIsp` VARCHAR(191) NULL,
    `locationFull` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `user_sessions_sessionToken_key`(`sessionToken`),
    INDEX `user_sessions_userId_idx`(`userId`),
    INDEX `user_sessions_status_idx`(`status`),
    INDEX `user_sessions_lastActivity_idx`(`lastActivity`),
    INDEX `user_sessions_ipAddress_idx`(`ipAddress`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `login_logs` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NULL,
    `username` VARCHAR(191) NOT NULL,
    `ipAddress` VARCHAR(191) NOT NULL,
    `userAgent` TEXT NULL,
    `loginTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `logoutTime` DATETIME(3) NULL,
    `loginResult` ENUM('SUCCESS', 'FAILED', 'BLOCKED') NOT NULL,
    `failureReason` VARCHAR(191) NULL,
    `deviceInfo` TEXT NULL,
    `sessionDuration` INTEGER NULL,
    `locationCountry` VARCHAR(191) NULL,
    `locationRegion` VARCHAR(191) NULL,
    `locationProvince` VARCHAR(191) NULL,
    `locationCity` VARCHAR(191) NULL,
    `locationIsp` VARCHAR(191) NULL,
    `locationFull` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `login_logs_userId_idx`(`userId`),
    INDEX `login_logs_loginTime_idx`(`loginTime`),
    INDEX `login_logs_ipAddress_idx`(`ipAddress`),
    INDEX `login_logs_loginResult_idx`(`loginResult`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user_sessions` ADD CONSTRAINT `user_sessions_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `login_logs` ADD CONSTRAINT `login_logs_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
