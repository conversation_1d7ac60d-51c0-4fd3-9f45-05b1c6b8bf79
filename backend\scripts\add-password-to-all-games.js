const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addPasswordFieldToAllGames() {
  try {
    console.log('🔧 为所有游戏检查并添加游戏密码字段...');
    
    // 获取所有活跃游戏
    const games = await prisma.game.findMany({
      where: { isActive: true },
      include: {
        formFields: {
          where: { isActive: true }
        }
      }
    });
    
    console.log(`📋 找到 ${games.length} 个活跃游戏`);
    
    for (const game of games) {
      console.log(`\n🎮 处理游戏: ${game.displayName} (${game.name})`);
      
      // 检查是否已经存在游戏密码字段
      const existingPasswordField = game.formFields.find(field => 
        field.fieldKey === 'game_password' || 
        field.fieldKey === 'gamePassword' ||
        field.fieldLabel.includes('密码')
      );
      
      if (existingPasswordField) {
        console.log(`✅ 已存在密码字段: ${existingPasswordField.fieldLabel}`);
        continue;
      }
      
      console.log('➕ 添加游戏密码字段...');
      
      // 获取当前最大的排序值
      const maxSortOrder = await prisma.gameFormField.findFirst({
        where: { gameId: game.id },
        orderBy: { sortOrder: 'desc' },
        select: { sortOrder: true }
      });
      
      const newSortOrder = (maxSortOrder?.sortOrder || 0) + 1;
      
      await prisma.gameFormField.create({
        data: {
          gameId: game.id,
          fieldKey: 'game_password',
          fieldLabel: '游戏密码',
          fieldType: 'PASSWORD',
          isRequired: true,
          isActive: true,
          sortOrder: newSortOrder,
          placeholder: '请输入游戏密码'
        }
      });
      
      console.log('✅ 游戏密码字段添加成功！');
    }
    
    console.log('\n🔍 验证所有游戏的字段...');
    
    // 重新查询所有游戏的字段
    const updatedGames = await prisma.game.findMany({
      where: { isActive: true },
      include: {
        formFields: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        }
      }
    });
    
    updatedGames.forEach(game => {
      console.log(`\n📋 ${game.displayName}:`);
      game.formFields.forEach((field, index) => {
        const required = field.isRequired ? '(必需)' : '(可选)';
        console.log(`  ${index + 1}. ${field.fieldLabel} ${required}`);
      });
    });
    
  } catch (error) {
    console.error('❌ 处理失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

addPasswordFieldToAllGames();
