import { Router } from 'express';
import {
  getOverviewStats,
  getRevenueAnalysis,
  getEmployeePerformance,
  getOrderTrends,
} from '../controllers/statisticsController';
import { authenticateToken, requireBossOrAdmin } from '../middleware/auth';

const router = Router();

// 获取综合统计数据（需要老板或管理员权限）
router.get('/overview', authenticateToken, requireBossOrAdmin, getOverviewStats);

// 获取收益分析数据（需要老板或管理员权限）
router.get('/revenue', authenticateToken, requireBossOrAdmin, getRevenueAnalysis);

// 获取员工绩效数据（需要老板或管理员权限）
router.get('/employees', authenticateToken, requireBossOrAdmin, getEmployeePerformance);

// 获取订单趋势数据（需要老板或管理员权限）
router.get('/trends', authenticateToken, requireBossOrAdmin, getOrderTrends);

export default router;
