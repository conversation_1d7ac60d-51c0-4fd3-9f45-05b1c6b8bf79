import { Request, Response } from 'express';
import { asyncHandler, ValidationError } from '../middleware/errorHandler';
import { ApiResponse, FileUploadResponse } from '../types/common';
import { getFileUrl } from '../middleware/upload';

// 单文件上传
export const uploadSingleFile = asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    throw new ValidationError('没有上传文件');
  }

  const file = req.file;
  const type = req.body.type || 'documents';
  
  const fileResponse: FileUploadResponse = {
    filename: file.filename,
    originalName: file.originalname,
    path: file.path,
    size: file.size,
    mimeType: file.mimetype,
    url: getFileUrl(file.filename, type),
  };

  const response: ApiResponse<FileUploadResponse> = {
    success: true,
    data: fileResponse,
    message: '文件上传成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 多文件上传
export const uploadMultipleFiles = asyncHandler(async (req: Request, res: Response) => {
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    throw new ValidationError('没有上传文件');
  }

  const files = req.files as Express.Multer.File[];
  const type = req.body.type || 'documents';
  
  const fileResponses: FileUploadResponse[] = files.map(file => ({
    filename: file.filename,
    originalName: file.originalname,
    path: file.path,
    size: file.size,
    mimeType: file.mimetype,
    url: getFileUrl(file.filename, type),
  }));

  const response: ApiResponse<FileUploadResponse[]> = {
    success: true,
    data: fileResponses,
    message: `成功上传 ${files.length} 个文件`,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});



// 上传任务截图
export const uploadScreenshots = asyncHandler(async (req: Request, res: Response) => {
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    throw new ValidationError('没有上传截图文件');
  }

  const files = req.files as Express.Multer.File[];
  
  // 检查是否都是图片或视频文件
  const invalidFiles = files.filter(file => 
    !file.mimetype.startsWith('image/') && !file.mimetype.startsWith('video/')
  );
  
  if (invalidFiles.length > 0) {
    throw new ValidationError('截图只能是图片或视频文件');
  }

  const fileResponses: FileUploadResponse[] = files.map(file => ({
    filename: file.filename,
    originalName: file.originalname,
    path: file.path,
    size: file.size,
    mimeType: file.mimetype,
    url: getFileUrl(file.filename, 'screenshots'),
  }));

  const response: ApiResponse<FileUploadResponse[]> = {
    success: true,
    data: fileResponses,
    message: `成功上传 ${files.length} 个截图`,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});
