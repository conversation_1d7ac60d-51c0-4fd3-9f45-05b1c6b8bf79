/**
 * 导航组件导出
 */

export { default as AppLayout } from './AppLayout.vue'
export { default as AppSidebar } from './AppSidebar.vue'
export { default as AppHeader } from './AppHeader.vue'

// 类型定义
export interface MenuItem {
  index: string
  title: string
  icon: any
  children?: MenuItem[]
  permission?: string[]
}

export interface BreadcrumbItem {
  name: string
  path?: string
  current?: boolean
}

export type NavigationTheme = 'boss' | 'employee' | 'admin'
