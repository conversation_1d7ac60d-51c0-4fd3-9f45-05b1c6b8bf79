import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import App from './App.vue'
import router from './router'
import './styles/index.scss'
import { permissionDirective } from '@/utils/permission'
import { initializeSocketService } from '@/services/socketService'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册权限指令
app.directive('permission', permissionDirective)

// 初始化Socket.IO服务（在用户登录后会自动连接）
router.afterEach(() => {
  // 检查用户是否已登录，如果是则初始化Socket服务
  const token = localStorage.getItem('token')
  if (token) {
    initializeSocketService()
  }
})

app.mount('#app')
