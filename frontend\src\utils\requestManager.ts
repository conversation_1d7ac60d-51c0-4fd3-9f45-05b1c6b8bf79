/**
 * 请求管理器 - 处理请求去重、防抖和重试逻辑
 */

import { AxiosRequestConfig } from 'axios'

// 请求缓存接口
interface RequestCache {
  [key: string]: {
    promise: Promise<any>
    timestamp: number
    config: AxiosRequestConfig
  }
}

// 防抖缓存接口
interface DebounceCache {
  [key: string]: {
    timer: number
    lastCall: number
  }
}

class RequestManager {
  private requestCache: RequestCache = {}
  private debounceCache: DebounceCache = {}
  private readonly CACHE_DURATION = 5000 // 5秒内的相同请求会被去重
  private readonly DEBOUNCE_DELAY = 300 // 300ms防抖延迟

  /**
   * 生成请求的唯一键
   */
  private generateRequestKey(config: AxiosRequestConfig): string {
    const { method = 'GET', url = '', params, data } = config
    const paramsStr = params ? JSON.stringify(params) : ''
    const dataStr = data ? JSON.stringify(data) : ''
    return `${method.toUpperCase()}_${url}_${paramsStr}_${dataStr}`
  }

  /**
   * 检查是否为重复请求
   */
  private isDuplicateRequest(key: string): boolean {
    const cached = this.requestCache[key]
    if (!cached) return false

    const now = Date.now()
    const isExpired = now - cached.timestamp > this.CACHE_DURATION

    if (isExpired) {
      delete this.requestCache[key]
      return false
    }

    return true
  }

  /**
   * 缓存请求
   */
  private cacheRequest(key: string, promise: Promise<any>, config: AxiosRequestConfig): void {
    this.requestCache[key] = {
      promise,
      timestamp: Date.now(),
      config
    }

    // 请求完成后清除缓存
    promise.finally(() => {
      delete this.requestCache[key]
    })
  }

  /**
   * 防抖处理
   */
  private debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number = this.DEBOUNCE_DELAY
  ): Promise<ReturnType<T>> {
    return new Promise((resolve, reject) => {
      const cached = this.debounceCache[key]
      const now = Date.now()

      // 清除之前的定时器
      if (cached?.timer) {
        clearTimeout(cached.timer)
      }

      // 设置新的定时器
      const timer = setTimeout(async () => {
        try {
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          delete this.debounceCache[key]
        }
      }, delay) as unknown as number

      this.debounceCache[key] = {
        timer,
        lastCall: now
      }
    })
  }

  /**
   * 处理请求 - 包含去重和防抖逻辑
   */
  public async handleRequest<T>(
    config: AxiosRequestConfig,
    requestFn: () => Promise<T>,
    options: {
      enableDeduplication?: boolean
      enableDebounce?: boolean
      debounceDelay?: number
    } = {}
  ): Promise<T> {
    const {
      enableDeduplication = true,
      enableDebounce = false,
      debounceDelay = this.DEBOUNCE_DELAY
    } = options

    const requestKey = this.generateRequestKey(config)

    // 检查是否启用去重
    if (enableDeduplication && this.isDuplicateRequest(requestKey)) {
      console.log(`[RequestManager] 检测到重复请求，返回缓存结果: ${requestKey}`)
      return this.requestCache[requestKey].promise
    }

    // 检查是否启用防抖
    if (enableDebounce) {
      return this.debounce(requestKey, requestFn, debounceDelay)
    }

    // 执行请求
    const promise = requestFn()

    // 缓存请求（如果启用去重）
    if (enableDeduplication) {
      this.cacheRequest(requestKey, promise, config)
    }

    return promise
  }

  /**
   * 清除所有缓存
   */
  public clearCache(): void {
    // 清除请求缓存
    this.requestCache = {}

    // 清除防抖定时器
    Object.values(this.debounceCache).forEach(({ timer }) => {
      clearTimeout(timer)
    })
    this.debounceCache = {}
  }

  /**
   * 清除特定请求的缓存
   */
  public clearRequestCache(config: AxiosRequestConfig): void {
    const key = this.generateRequestKey(config)
    delete this.requestCache[key]
    
    const debounceItem = this.debounceCache[key]
    if (debounceItem) {
      clearTimeout(debounceItem.timer)
      delete this.debounceCache[key]
    }
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    requestCacheSize: number
    debounceCacheSize: number
    oldestRequest: number | null
  } {
    const requestKeys = Object.keys(this.requestCache)
    const debounceKeys = Object.keys(this.debounceCache)
    
    let oldestRequest: number | null = null
    if (requestKeys.length > 0) {
      oldestRequest = Math.min(
        ...Object.values(this.requestCache).map(item => item.timestamp)
      )
    }

    return {
      requestCacheSize: requestKeys.length,
      debounceCacheSize: debounceKeys.length,
      oldestRequest
    }
  }

  /**
   * 检查请求是否应该被限制
   */
  public shouldThrottleRequest(config: AxiosRequestConfig): boolean {
    // 对于某些高频接口进行限制
    const throttledPaths = [
      '/auth/login',
      '/users/me',
      '/notifications/unread-count'
    ]

    const url = config.url || ''
    return throttledPaths.some(path => url.includes(path))
  }

  /**
   * 重试请求逻辑
   */
  public async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    shouldRetry: (error: any) => boolean = () => true
  ): Promise<T> {
    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn()
      } catch (error: any) {
        lastError = error
        
        // 检查是否应该重试
        if (!shouldRetry(error) || attempt === maxRetries) {
          break
        }

        // 429错误不应该重试，直接抛出
        if (error.status === 429) {
          throw error
        }

        console.log(`[RequestManager] 请求失败，第${attempt}次重试，${retryDelay}ms后重试...`)
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
      }
    }

    throw lastError
  }
}

// 导出单例实例
export const requestManager = new RequestManager()

// 导出工具函数
export const createRequestKey = (config: AxiosRequestConfig): string => {
  const { method = 'GET', url = '', params, data } = config
  const paramsStr = params ? JSON.stringify(params) : ''
  const dataStr = data ? JSON.stringify(data) : ''
  return `${method.toUpperCase()}_${url}_${paramsStr}_${dataStr}`
}

// 防抖工具函数
export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): ((...args: Parameters<T>) => Promise<ReturnType<T>>) => {
  let timer: number | null = null

  return (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return new Promise((resolve, reject) => {
      if (timer) {
        clearTimeout(timer)
      }

      timer = setTimeout(async () => {
        try {
          const result = await fn(...args)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay) as unknown as number
    })
  }
}

// 节流工具函数
export const throttle = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): ((...args: Parameters<T>) => ReturnType<T> | undefined) => {
  let lastCall = 0

  return (...args: Parameters<T>): ReturnType<T> | undefined => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      return fn(...args)
    }
    
    return undefined
  }
}

export default requestManager
