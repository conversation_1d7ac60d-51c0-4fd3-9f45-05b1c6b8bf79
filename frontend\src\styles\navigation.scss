/**
 * 统一的导航样式系统
 */

// 导入sass:map模块以使用map.get函数
@use 'sass:map';

// 导航主题色彩 - 现代化设计（性能优化版）
$nav-themes: (
  boss: (
    // 🎨 优化：更丰富的渐变层次，替代毛玻璃效果
    primary: linear-gradient(180deg,
      #1e293b 0%,
      #1a2332 25%,
      #151d28 50%,
      #10171f 75%,
      #0f172a 100%),
    secondary: linear-gradient(135deg,
      #334155 0%,
      #2a3441 25%,
      #212936 50%,
      #1e293b 100%),
    accent: #3b82f6,
    text: #f8fafc,
    text-secondary: #cbd5e1,
    active: linear-gradient(135deg,
      #3b82f6 0%,
      #2563eb 25%,
      #1d4ed8 75%,
      #1e40af 100%),
    hover: rgba(59, 130, 246, 0.15),
    border: rgba(59, 130, 246, 0.3),
    shadow: rgba(59, 130, 246, 0.35),
    glow: rgba(59, 130, 246, 0.45)
  ),
  employee: (
    // 🎨 优化：员工主题渐变增强
    primary: linear-gradient(180deg,
      #064e3b 0%,
      #054239 25%,
      #043730 50%,
      #032b26 75%,
      #022c22 100%),
    secondary: linear-gradient(135deg,
      #065f46 0%,
      #055240 25%,
      #054539 50%,
      #064e3b 100%),
    accent: #10b981,
    text: #ecfdf5,
    text-secondary: #a7f3d0,
    active: linear-gradient(135deg,
      #10b981 0%,
      #059669 25%,
      #047857 75%,
      #065f46 100%),
    hover: rgba(16, 185, 129, 0.15),
    border: rgba(16, 185, 129, 0.3),
    shadow: rgba(16, 185, 129, 0.35),
    glow: rgba(16, 185, 129, 0.45)
  ),
  admin: (
    // 🎨 优化：管理员主题渐变增强
    primary: linear-gradient(180deg,
      #7c2d12 0%,
      #6b2710 25%,
      #5a210e 50%,
      #4a1b0c 75%,
      #451a03 100%),
    secondary: linear-gradient(135deg,
      #9a3412 0%,
      #832e10 25%,
      #7c2d12 50%,
      #6b2710 100%),
    accent: #ea580c,
    text: #fef2f2,
    text-secondary: #fecaca,
    active: linear-gradient(135deg,
      #ea580c 0%,
      #dc2626 25%,
      #b91c1c 75%,
      #991b1b 100%),
    hover: rgba(234, 88, 12, 0.15),
    border: rgba(234, 88, 12, 0.3),
    shadow: rgba(234, 88, 12, 0.35),
    glow: rgba(234, 88, 12, 0.45)
  )
);

// 导航尺寸 - 优化版
$nav-sidebar-width: 280px;
$nav-sidebar-collapsed-width: 72px;
$nav-header-height: 64px;
$nav-logo-height: 72px;

// 现代化设计参数
$nav-border-radius: 12px;
$nav-border-radius-small: 8px;
$nav-item-height: 48px;
$nav-item-padding: 16px;
// 🔍 优化：增大图标尺寸，提升可读性
$nav-icon-size: 24px;
$nav-icon-size-collapsed: 32px; // 🎯 折叠状态下与Logo"老"字一样大
$nav-spacing: 8px;

// 导航动画 - 增强版
$nav-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$nav-transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$nav-transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
$nav-transition-smooth: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
$nav-transition-elastic: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

// 动画关键帧
@keyframes slideInFromLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

// 基础导航布局
.layout-container {
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  position: relative;
}

// 侧边栏基础样式 - 现代化设计（性能优化版）
.nav-sidebar {
  width: $nav-sidebar-width;
  height: 100vh;
  min-height: 100vh;
  max-height: 100vh;
  position: relative;
  flex-shrink: 0;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s ease,
              border-radius 0.3s ease;
  overflow: hidden;
  z-index: 100;
  border-radius: 0 $nav-border-radius $nav-border-radius 0;
  // 🎨 优化：移除毛玻璃效果，使用多层阴影系统
  box-shadow:
    // 主要阴影 - 深度感
    4px 0 32px rgba(0, 0, 0, 0.15),
    // 边缘光效 - 立体感
    0 0 0 1px rgba(255, 255, 255, 0.08),
    // 内部高光 - 质感
    inset 1px 0 0 rgba(255, 255, 255, 0.05),
    // 远距离阴影 - 层次感
    8px 0 64px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;

  // 添加内部发光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.1) 100%);
    z-index: 1;
  }

  &.collapsed {
    width: $nav-sidebar-collapsed-width;
    // 🎨 优化：折叠状态的阴影系统
    box-shadow:
      // 主要阴影 - 适配窄宽度
      2px 0 24px rgba(0, 0, 0, 0.12),
      // 边缘光效
      0 0 0 1px rgba(255, 255, 255, 0.06),
      // 内部高光
      inset 1px 0 0 rgba(255, 255, 255, 0.04),
      // 远距离阴影
      4px 0 48px rgba(0, 0, 0, 0.06);
    border-radius: 0 10px 10px 0;
  }

  // 主题样式混入 - 现代化设计
  @each $theme, $colors in $nav-themes {
    &.theme-#{$theme} {
      background: map.get($colors, primary);
      color: map.get($colors, text);

      .nav-logo {
        background: map.get($colors, secondary);
        color: map.get($colors, text);
        border-radius: 0 0 $nav-border-radius $nav-border-radius;
        margin: $nav-spacing;
        margin-top: 0;
        margin-bottom: $nav-spacing * 2;
        // 🎨 优化：增强Logo区域阴影系统
        box-shadow:
          // 主要阴影
          0 8px 24px rgba(0, 0, 0, 0.18),
          // 内部高光
          inset 0 1px 0 rgba(255, 255, 255, 0.12),
          // 底部深度
          0 4px 12px rgba(0, 0, 0, 0.1),
          // 边缘光效
          0 0 0 1px rgba(255, 255, 255, 0.05);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60%;
          height: 1px;
          background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 50%,
            transparent 100%);
        }
      }

      .nav-menu {
        background: transparent;
        padding: 0 $nav-spacing;

        .nav-menu-item {
          color: map.get($colors, text-secondary);
          border-radius: $nav-border-radius-small;
          margin-bottom: 4px;
          position: relative;
          overflow: hidden;
          animation: slideInFromLeft 0.3s ease-out;

          // 悬停背景效果
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: map.get($colors, hover);
            opacity: 0;
            transition: $nav-transition-smooth;
            border-radius: $nav-border-radius-small;
            transform: scaleX(0);
            transform-origin: left;
          }

          // 点击波纹效果
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
            pointer-events: none;
          }

          &:hover {
            color: map.get($colors, text);
            transform: translateX(6px) scale(1.02);
            transition: $nav-transition-elastic;

            &::before {
              opacity: 1;
              transform: scaleX(1);
            }
          }

          &:active {
            transform: translateX(6px) scale(0.98);

            &::after {
              width: 120px;
              height: 120px;
              opacity: 0;
              transition: width 0.3s, height 0.3s, opacity 0.3s;
            }
          }

          &.is-active {
            background: map.get($colors, active);
            color: white;
            box-shadow: 0 8px 32px map.get($colors, shadow),
                        0 0 0 1px map.get($colors, border),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateX(10px) scale(1.05);
            position: relative;

            // 外部发光效果
            &::before {
              content: '';
              position: absolute;
              top: -2px;
              left: -2px;
              right: -2px;
              bottom: -2px;
              background: map.get($colors, active);
              border-radius: calc(#{$nav-border-radius-small} + 2px);
              opacity: 0.3;
              filter: blur(8px);
              z-index: -1;
              animation: pulseGlow 2s infinite;
            }

            // 右侧指示器
            &::after {
              content: '';
              position: absolute;
              right: 8px;
              top: 50%;
              transform: translateY(-50%);
              width: 8px;
              height: 8px;
              background: white;
              border-radius: 50%;
              box-shadow: 0 0 16px rgba(255, 255, 255, 0.9),
                          0 0 32px map.get($colors, glow);
              animation: pulseGlow 1.5s infinite;
            }
          }
        }

        .nav-submenu {
          .nav-submenu-title {
            color: map.get($colors, text-secondary);
            border-radius: $nav-border-radius-small;
            margin-bottom: 4px;
            position: relative;
            overflow: hidden;
            animation: slideInFromLeft 0.4s ease-out;

            // 悬停背景效果
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: map.get($colors, hover);
              opacity: 0;
              transition: $nav-transition-smooth;
              border-radius: $nav-border-radius-small;
              transform: scaleX(0);
              transform-origin: left;
            }

            &:hover {
              color: map.get($colors, text);
              transform: translateX(6px) scale(1.02);
              transition: $nav-transition-elastic;

              &::before {
                opacity: 1;
                transform: scaleX(1);
              }
            }

            &:active {
              transform: translateX(6px) scale(0.98);
            }
          }

          &.is-opened .nav-submenu-title {
            background: map.get($colors, secondary);
            color: map.get($colors, text);
            border-radius: $nav-border-radius-small;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateX(4px);

            &::before {
              opacity: 0;
            }
          }
        }
      }
    }
  }
}

// Logo区域 - 现代化设计
.nav-logo {
  height: $nav-logo-height;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: $nav-transition;

  .logo-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
  }

  .logo-text {
    font-size: 20px;
    font-weight: 800;
    margin: 0;
    transition: $nav-transition;
    white-space: nowrap;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &.collapsed {
      font-size: 24px;
      font-weight: 900;
    }
  }

  .logo-icon {
    font-size: 28px;
    transition: $nav-transition;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}

// 菜单样式 - 现代化设计
.nav-menu {
  border: none;
  width: 100%;
  background: transparent;
  padding-top: $nav-spacing;
  flex: 1;
  overflow-y: auto;

  .nav-menu-item {
    height: $nav-item-height;
    line-height: $nav-item-height;
    padding: 0 $nav-item-padding;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: $nav-transition;
    position: relative;
    border: none;
    margin: 0;
    z-index: 1;

    .nav-icon {
      width: $nav-icon-size;
      height: $nav-icon-size;
      margin-right: 12px;
      // 🔍 优化：增大图标字体大小
      font-size: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: $nav-transition;
      position: relative;
      z-index: 2;
      // 🔍 优化：增强图标清晰度
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .nav-text {
      flex: 1;
      transition: $nav-transition;
      white-space: nowrap;
      overflow: hidden;
      font-weight: 500;
      font-size: 14px;
      position: relative;
      z-index: 2;
    }

    &.collapsed {
      padding: 0 calc(($nav-sidebar-collapsed-width - $nav-icon-size) / 2);
      justify-content: center;

      .nav-icon {
        margin-right: 0;
        font-size: 20px;
      }

      .nav-text {
        display: none;
      }
    }

    // 悬停效果增强
    &:hover {
      .nav-icon {
        transform: scale(1.1);
      }

      .nav-text {
        font-weight: 600;
      }
    }

    // 激活状态增强
    &.is-active {
      .nav-icon {
        transform: scale(1.15);
      }

      .nav-text {
        font-weight: 700;
      }
    }
  }

  .nav-submenu {
    margin-bottom: 4px;

    .nav-submenu-title {
      height: $nav-item-height;
      line-height: $nav-item-height;
      padding: 0 $nav-item-padding;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: $nav-transition;
      position: relative;
      z-index: 1;

      .nav-icon {
        width: $nav-icon-size;
        height: $nav-icon-size;
        margin-right: 12px;
        // 🔍 优化：子菜单图标也增大
        font-size: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: $nav-transition;
        position: relative;
        z-index: 2;
        // 🔍 优化：增强图标清晰度
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .nav-text {
        flex: 1;
        transition: $nav-transition;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 500;
        font-size: 14px;
        position: relative;
        z-index: 2;
      }

      .nav-arrow {
        width: 16px;
        height: 16px;
        transition: $nav-transition-bounce;
        position: relative;
        z-index: 2;

        &.expanded {
          transform: rotate(90deg);
        }
      }

      // 收缩状态样式
      &.collapsed {
        padding: 0 calc(($nav-sidebar-collapsed-width - $nav-icon-size) / 2);
        justify-content: center;

        .nav-icon {
          margin-right: 0;
          font-size: 20px;
        }

        .nav-text {
          display: none;
        }

        .nav-arrow {
          display: none;
        }
      }

      // 悬停效果增强
      &:hover {
        .nav-icon {
          transform: scale(1.1);
        }

        .nav-text {
          font-weight: 600;
        }

        .nav-arrow {
          transform: scale(1.1);

          &.expanded {
            transform: rotate(90deg) scale(1.1);
          }
        }
      }
    }

    .nav-submenu-items {
      // 🎨 优化：移除毛玻璃，使用纯色背景
      background: rgba(0, 0, 0, 0.08);
      overflow: hidden;
      transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                  opacity 0.3s ease,
                  transform 0.3s ease;
      border-radius: 0 0 $nav-border-radius-small $nav-border-radius-small;
      margin: 0 $nav-spacing;
      // 🎨 优化：增强阴影效果替代毛玻璃
      box-shadow:
        inset 0 2px 8px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.06);
      opacity: 0;
      transform: translateY(-10px);

      .nav-menu-item {
        padding-left: calc($nav-item-padding + $nav-icon-size + 12px);
        height: 44px;
        line-height: 44px;
        font-size: 13px;
        margin-bottom: 2px;
        border-radius: $nav-border-radius-small;
        position: relative;
        overflow: hidden;
        opacity: 0;
        transform: translateX(-20px);
        animation: slideInFromLeft 0.3s ease-out forwards;

        // 为每个子菜单项添加延迟动画
        @for $i from 1 through 10 {
          &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.05}s;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        // 悬停背景效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.1);
          opacity: 0;
          transition: $nav-transition-fast;
          border-radius: $nav-border-radius-small;
          transform: scaleX(0);
          transform-origin: left;
        }

        .nav-icon {
          // 🔍 优化：子菜单项图标也适当增大
          width: 18px;
          height: 18px;
          font-size: 16px;
          margin-right: 8px;
          transition: $nav-transition;
          // 🔍 优化：增强图标清晰度
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        .nav-text {
          font-size: 13px;
          font-weight: 400;
          transition: $nav-transition;
        }

        &:hover {
          transform: translateX(4px);

          &::before {
            opacity: 1;
            transform: scaleX(1);
          }

          .nav-icon {
            transform: scale(1.1);
          }

          .nav-text {
            font-weight: 500;
          }
        }

        &.is-active {
          background: rgba(255, 255, 255, 0.15);
          transform: translateX(6px);

          .nav-icon {
            transform: scale(1.15);
          }

          .nav-text {
            font-weight: 600;
          }
        }
      }
    }

    // 展开状态的子菜单样式
    &.is-opened {
      .nav-submenu-items {
        // 🎨 优化：增强展开状态的视觉效果
        border: 1px solid rgba(255, 255, 255, 0.12);
        box-shadow:
          // 内部阴影
          inset 0 2px 8px rgba(0, 0, 0, 0.12),
          // 外部阴影
          0 4px 16px rgba(0, 0, 0, 0.08),
          // 边缘光效
          0 0 0 1px rgba(255, 255, 255, 0.04);
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}

// 顶部导航栏
.nav-header {
  height: $nav-header-height;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 50;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .nav-toggle {
      width: 40px;
      height: 40px;
      border: none;
      background: transparent;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: $nav-transition;
      color: #6b7280;

      &:hover {
        background: #f3f4f6;
        color: #374151;
      }

      .toggle-icon {
        width: 20px;
        height: 20px;
        transition: $nav-transition;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .user-dropdown {
      .user-trigger {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: $nav-transition;
        color: #374151;

        &:hover {
          background: #f3f4f6;
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #3b82f6;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
        }

        .user-name {
          font-weight: 500;
          font-size: 14px;
        }

        .dropdown-arrow {
          width: 16px;
          height: 16px;
          transition: transform 0.3s ease;
        }
      }
    }
  }
}

// 主内容区域
.nav-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main-content {
    flex: 1;
    background: #f8fafc;
    padding: 24px;
    overflow-y: auto;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .nav-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.mobile-open {
      transform: translateX(0);
    }
  }

  .nav-main {
    margin-left: 0;
  }

  .nav-header {
    padding: 0 16px;
  }

  .main-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .nav-header {
    .header-right {
      gap: 8px;

      .user-dropdown .user-trigger {
        padding: 6px 8px;

        .user-name {
          display: none;
        }
      }
    }
  }

  .main-content {
    padding: 12px;
  }
}

// 导航遮罩层（移动端）
.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

// 滚动条样式
.nav-menu {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Tooltip样式（收缩状态下的提示）
.nav-menu-item.collapsed,
.nav-submenu.collapsed .nav-submenu-title {
  position: relative;

  &:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 12px;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: tooltipFadeIn 0.2s ease-out;
  }

  &:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.9);
    margin-left: 6px;
    z-index: 1000;
    pointer-events: none;
    animation: tooltipFadeIn 0.2s ease-out;
  }
}

// Tooltip动画
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

// 🔧 折叠状态专用样式 - 最高优先级
.nav-sidebar.collapsed {
  // 普通菜单项折叠样式
  .nav-menu-item {
    // 🎯 优化：为更大的图标调整内边距
    padding: 0 calc(($nav-sidebar-collapsed-width - $nav-icon-size-collapsed) / 2) !important;
    justify-content: center !important;
    // 🎯 确保有足够的垂直空间
    min-height: $nav-item-height !important;

    .nav-icon {
      // 🎯 优化：折叠状态下图标与Logo"老"字一样大
      width: $nav-icon-size-collapsed !important;
      height: $nav-icon-size-collapsed !important;
      margin-right: 0 !important;
      font-size: 30px !important; // 🎯 匹配Logo的视觉大小
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      // 🔍 优化：增强图标清晰度和对比度
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      filter: brightness(1.1) contrast(1.1) !important;
      // 🎯 优化：确保图标居中显示
      line-height: 1 !important;
    }

    .nav-text {
      display: none !important;
    }

    &:hover {
      transform: scale(1.1) !important;
    }

    &.is-active {
      transform: scale(1.15) !important;
    }
  }

  // 子菜单标题折叠样式
  .nav-submenu-title {
    // 🎯 优化：为更大的图标调整内边距
    padding: 0 calc(($nav-sidebar-collapsed-width - $nav-icon-size-collapsed) / 2) !important;
    justify-content: center !important;
    // 🎯 确保有足够的垂直空间
    min-height: $nav-item-height !important;

    .nav-icon {
      // 🎯 优化：子菜单标题折叠状态下图标与Logo"老"字一样大
      width: $nav-icon-size-collapsed !important;
      height: $nav-icon-size-collapsed !important;
      margin-right: 0 !important;
      font-size: 30px !important; // 🎯 匹配Logo的视觉大小
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      // 🔍 优化：增强图标清晰度和对比度
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      filter: brightness(1.1) contrast(1.1) !important;
      // 🎯 优化：确保图标居中显示
      line-height: 1 !important;
    }

    .nav-text {
      display: none !important;
    }

    .nav-arrow {
      display: none !important;
    }
  }

  // 子菜单项完全隐藏
  .nav-submenu-items {
    display: none !important;
  }
}
