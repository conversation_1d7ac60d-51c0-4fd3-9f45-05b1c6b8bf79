import { PrismaClient } from '@prisma/client';
import { 
  Notification, 
  CreateNotificationRequest, 
  NotificationQuery, 
  PaginatedResponse 
} from '../types/common';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export class NotificationService {
  // 创建通知
  async createNotification(data: CreateNotificationRequest): Promise<Notification> {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          title: data.title,
          content: data.content,
          type: data.type,
          isRead: false,
          data: data.data ? JSON.stringify(data.data) : null,
        },
      });

      logger.info(`创建通知成功: ${notification.id} - ${notification.title}`);
      
      return this.formatNotification(notification);
    } catch (error) {
      logger.error('创建通知失败:', error);
      throw new Error('创建通知失败');
    }
  }

  // 获取用户通知列表
  async getUserNotifications(
    userId: string, 
    query: NotificationQuery = {}
  ): Promise<PaginatedResponse<Notification>> {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        isRead,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = query;

      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {
        userId,
      };

      if (type) {
        where.type = type;
      }

      if (typeof isRead === 'boolean') {
        where.isRead = isRead;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }
        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // 获取总数
      const total = await prisma.notification.count({ where });

      // 获取通知列表
      const notifications = await prisma.notification.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      const totalPages = Math.ceil(total / limit);

      return {
        items: notifications.map(this.formatNotification),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error('获取用户通知列表失败:', error);
      throw new Error('获取通知列表失败');
    }
  }

  // 获取通知详情
  async getNotificationById(id: string, userId: string): Promise<Notification | null> {
    try {
      const notification = await prisma.notification.findFirst({
        where: {
          id,
          userId,
        },
      });

      if (!notification) {
        return null;
      }

      return this.formatNotification(notification);
    } catch (error) {
      logger.error('获取通知详情失败:', error);
      throw new Error('获取通知详情失败');
    }
  }

  // 标记通知为已读
  async markAsRead(id: string, userId: string): Promise<Notification | null> {
    try {
      const notification = await prisma.notification.findFirst({
        where: {
          id,
          userId,
        },
      });

      if (!notification) {
        return null;
      }

      const updatedNotification = await prisma.notification.update({
        where: { id },
        data: { isRead: true },
      });

      logger.info(`标记通知已读: ${id}`);
      return this.formatNotification(updatedNotification);
    } catch (error) {
      logger.error('标记通知已读失败:', error);
      throw new Error('标记通知已读失败');
    }
  }

  // 标记所有通知为已读
  async markAllAsRead(userId: string): Promise<{ count: number }> {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      });

      logger.info(`用户 ${userId} 标记 ${result.count} 条通知为已读`);
      return { count: result.count };
    } catch (error) {
      logger.error('标记所有通知已读失败:', error);
      throw new Error('标记所有通知已读失败');
    }
  }

  // 删除通知
  async deleteNotification(id: string, userId: string): Promise<boolean> {
    try {
      const notification = await prisma.notification.findFirst({
        where: {
          id,
          userId,
        },
      });

      if (!notification) {
        return false;
      }

      await prisma.notification.delete({
        where: { id },
      });

      logger.info(`删除通知: ${id}`);
      return true;
    } catch (error) {
      logger.error('删除通知失败:', error);
      throw new Error('删除通知失败');
    }
  }

  // 获取未读通知数量
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      });

      return count;
    } catch (error) {
      logger.error('获取未读通知数量失败:', error);
      throw new Error('获取未读通知数量失败');
    }
  }

  // 清理过期通知（保留最近30天的通知）
  async cleanupOldNotifications(): Promise<{ count: number }> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: thirtyDaysAgo,
          },
        },
      });

      logger.info(`清理了 ${result.count} 条过期通知`);
      return { count: result.count };
    } catch (error) {
      logger.error('清理过期通知失败:', error);
      throw new Error('清理过期通知失败');
    }
  }

  // 格式化通知数据
  private formatNotification(notification: any): Notification {
    return {
      id: notification.id,
      userId: notification.userId,
      title: notification.title,
      content: notification.content,
      type: notification.type,
      isRead: notification.isRead,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
      data: notification.data ? JSON.parse(notification.data) : null,
    };
  }
}
