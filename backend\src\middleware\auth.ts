import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UserRole } from '@prisma/client';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { AuthenticationError, AuthorizationError } from './errorHandler';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        role: UserRole;
        nickname?: string;
      };
    }
  }
}

// JWT载荷接口
interface JwtPayload {
  userId: string;
  username: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// 验证JWT令牌
export async function authenticateToken(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AuthenticationError('缺少访问令牌');
    }

    // 验证令牌
    const decoded = jwt.verify(token, config.JWT_SECRET) as JwtPayload;

    // 查询用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        nickname: true,
        role: true,
        status: true,
      },
    });

    if (!user) {
      throw new AuthenticationError('用户不存在');
    }

    if (user.status !== 'ACTIVE') {
      throw new AuthenticationError('用户账号已被禁用');
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      role: user.role,
      nickname: user.nickname || undefined,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AuthenticationError('无效的访问令牌'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AuthenticationError('访问令牌已过期'));
    } else {
      next(error);
    }
  }
}

// 角色权限检查
export function requireRole(...roles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('用户未认证');
    }

    if (!roles.includes(req.user.role)) {
      throw new AuthorizationError('权限不足');
    }

    next();
  };
}

// 检查是否为老板或管理员
export function requireBossOrAdmin(req: Request, res: Response, next: NextFunction): void {
  requireRole(UserRole.BOSS, UserRole.ADMIN)(req, res, next);
}

// 检查是否为管理员
export function requireAdmin(req: Request, res: Response, next: NextFunction): void {
  requireRole(UserRole.ADMIN)(req, res, next);
}

// 检查是否为员工（或更高权限）
export function requireEmployee(req: Request, res: Response, next: NextFunction): void {
  requireRole(UserRole.EMPLOYEE, UserRole.BOSS, UserRole.ADMIN)(req, res, next);
}

// 检查资源所有权或管理员/老板权限
export function requireOwnershipOrAdmin(userIdField: string = 'userId') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('用户未认证');
      }

      // 管理员和老板可以访问所有资源
      if (req.user.role === UserRole.ADMIN || req.user.role === UserRole.BOSS) {
        return next();
      }

      // 检查资源所有权
      const resourceUserId = req.params[userIdField] || req.body[userIdField];

      if (req.user.id !== resourceUserId) {
        throw new AuthorizationError('只能访问自己的资源');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

// 可选认证（不强制要求登录）
export async function optionalAuth(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, config.JWT_SECRET) as JwtPayload;
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          nickname: true,
          role: true,
          status: true,
        },
      });

      if (user && user.status === 'ACTIVE') {
        req.user = {
          id: user.id,
          username: user.username,
          role: user.role,
          nickname: user.nickname || undefined,
        };
      }
    }

    next();
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next();
  }
}
