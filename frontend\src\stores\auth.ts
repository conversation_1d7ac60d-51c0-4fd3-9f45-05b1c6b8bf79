import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { authApi } from '@/api/auth'
import type { User, LoginForm, RegisterForm } from '@/types'
import { initializeSocketService, destroySocketService } from '@/services/socketService'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role)
  const isBoss = computed(() => userRole.value === 'BOSS' || userRole.value === 'ADMIN')
  const isEmployee = computed(() => userRole.value === 'EMPLOYEE')
  const isAdmin = computed(() => userRole.value === 'ADMIN')

  // 从本地存储加载token
  const loadTokenFromStorage = () => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
    }
  }

  // 保存token到本地存储
  const saveTokenToStorage = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除token
  const clearToken = () => {
    token.value = null
    localStorage.removeItem('token')
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      loading.value = true
      const response = await authApi.login(loginForm)
      
      if (response.success && response.data) {
        user.value = response.data.user
        saveTokenToStorage(response.data.token)

        // 初始化Socket服务
        try {
          initializeSocketService()
        } catch (error) {
          console.error('初始化Socket服务失败:', error)
        }

        ElMessage.success('登录成功')

        // 根据用户角色重定向
        const redirectPath = getRedirectPath()
        await router.push(redirectPath)

        return true
      }
      return false
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    try {
      loading.value = true
      const response = await authApi.register(registerForm)
      
      if (response.success && response.data) {
        user.value = response.data.user
        saveTokenToStorage(response.data.token)
        
        ElMessage.success('注册成功')
        
        // 根据用户角色重定向
        const redirectPath = getRedirectPath()
        await router.push(redirectPath)
        
        return true
      }
      return false
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      user.value = null
      clearToken()

      // 销毁Socket服务
      try {
        destroySocketService()
      } catch (error) {
        console.error('销毁Socket服务失败:', error)
      }

      ElMessage.success('已退出登录')
      await router.push('/login')
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    loadTokenFromStorage()
    
    if (!token.value) {
      return false
    }

    try {
      const response = await authApi.getCurrentUser()
      if (response.success && response.data) {
        user.value = response.data
        return true
      } else {
        clearToken()
        return false
      }
    } catch (error) {
      clearToken()
      return false
    }
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo }
    }
  }

  // 更新密码
  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      loading.value = true
      const response = await authApi.updatePassword({
        currentPassword,
        newPassword
      })
      
      if (response.success) {
        ElMessage.success('密码更新成功')
        return true
      }
      return false
    } catch (error: any) {
      ElMessage.error(error.message || '密码更新失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 根据用户角色获取重定向路径
  const getRedirectPath = () => {
    if (!user.value) return '/dashboard'
    
    switch (user.value.role) {
      case 'BOSS':
      case 'ADMIN':
        return '/boss'
      case 'EMPLOYEE':
        return '/employee'
      default:
        return '/dashboard'
    }
  }

  // 初始化时加载token
  loadTokenFromStorage()

  return {
    // 状态
    user,
    token,
    loading,
    
    // 计算属性
    isAuthenticated,
    userRole,
    isBoss,
    isEmployee,
    isAdmin,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    updateUserInfo,
    updatePassword,
    getRedirectPath
  }
})
