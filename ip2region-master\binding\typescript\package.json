{"name": "ts-ip2region", "version": "0.1.1", "license": "Apache-2.0", "author": "<PERSON>", "main": "dist/index.js", "module": "dist/ip2region.esm.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"analyze": "size-limit --why", "build": "dts build", "lint": "dts lint", "prepare": "dts build", "size": "size-limit", "start": "dts watch", "test": "dts test"}, "husky": {"hooks": {"pre-commit": "dts lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "jest": {"testEnvironment": "node"}, "peerDependencies": {}, "engines": {"node": ">=12"}, "size-limit": [{"path": "dist/ip2region.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/ip2region.esm.js", "limit": "10 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^11.1.6", "@tsconfig/recommended": "^1.0.8", "dts-cli": "^2.0.5", "husky": "^9.1.7", "size-limit": "^11.1.6", "tslib": "^2.8.1", "typescript": "^5.7.2"}}