<template>
  <el-dialog
    v-model="dialogVisible"
    title="强制用户下线"
    width="500px"
    :before-close="handleClose"
  >
    <div class="force-logout-dialog">
      <div class="user-info">
        <el-alert
          :title="`即将强制 ${user?.username} 下线`"
          type="warning"
          :closable="false"
          show-icon
        />
        
        <div class="user-details">
          <div class="detail-item">
            <span class="label">用户名：</span>
            <span class="value">{{ user?.username }}</span>
          </div>
          <div class="detail-item" v-if="user?.nickname">
            <span class="label">昵称：</span>
            <span class="value">{{ user?.nickname }}</span>
          </div>
          <div class="detail-item">
            <span class="label">角色：</span>
            <el-tag :type="getRoleTagType(user?.role)" size="small">
              {{ getRoleText(user?.role) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <span class="label">IP地址：</span>
            <span class="value">{{ user?.ipAddress }}</span>
          </div>
          <div class="detail-item">
            <span class="label">在线时长：</span>
            <span class="value">{{ formatOnlineTime(user?.onlineDuration || 0) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">地理位置：</span>
            <span class="value">{{ formatLocation(user) }}</span>
          </div>
        </div>
      </div>
      
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="下线原因" prop="reason">
          <el-select 
            v-model="form.reason" 
            placeholder="请选择下线原因"
            style="width: 100%"
            @change="handleReasonChange"
          >
            <el-option label="违规操作" value="违规操作" />
            <el-option label="安全检查" value="安全检查" />
            <el-option label="系统维护" value="系统维护" />
            <el-option label="异常登录" value="异常登录" />
            <el-option label="管理员操作" value="管理员操作" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          v-if="form.reason === '其他原因'" 
          label="详细说明" 
          prop="customReason"
        >
          <el-input
            v-model="form.customReason"
            type="textarea"
            :rows="3"
            placeholder="请输入详细的下线原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <div class="warning-notice">
        <el-alert
          title="注意：强制下线后，用户将立即断开连接，正在进行的操作可能会丢失。"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="danger" 
          @click="handleConfirm"
          :loading="loading"
        >
          确认强制下线
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { OnlineUser, UserRole } from '@/types'

interface Props {
  modelValue: boolean
  user: OnlineUser | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', sessionId: string, reason: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const form = reactive({
  reason: '',
  customReason: ''
})

// 表单验证规则
const rules: FormRules = {
  reason: [
    { required: true, message: '请选择下线原因', trigger: 'change' }
  ],
  customReason: [
    { required: true, message: '请输入详细说明', trigger: 'blur' },
    { min: 5, max: 200, message: '说明长度应在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 监听对话框打开，重置表单
watch(() => props.modelValue, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 获取角色标签类型
const getRoleTagType = (role?: UserRole) => {
  switch (role) {
    case 'ADMIN': return 'danger'
    case 'BOSS': return 'warning'
    case 'EMPLOYEE': return 'success'
    default: return 'info'
  }
}

// 获取角色文本
const getRoleText = (role?: UserRole) => {
  switch (role) {
    case 'ADMIN': return '管理员'
    case 'BOSS': return '老板'
    case 'EMPLOYEE': return '员工'
    default: return '未知'
  }
}

// 格式化在线时长
const formatOnlineTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 格式化地理位置
const formatLocation = (user?: OnlineUser | null): string => {
  if (!user) return '未知位置'
  
  const parts = []
  if (user.locationCountry && user.locationCountry !== '未知') {
    parts.push(user.locationCountry)
  }
  if (user.locationProvince && user.locationProvince !== '未知') {
    parts.push(user.locationProvince)
  }
  if (user.locationCity && user.locationCity !== '未知') {
    parts.push(user.locationCity)
  }
  if (user.locationIsp && user.locationIsp !== '未知') {
    parts.push(user.locationIsp)
  }
  
  return parts.length > 0 ? parts.join(' ') : '未知位置'
}

// 处理原因选择变化
const handleReasonChange = () => {
  if (form.reason !== '其他原因') {
    form.customReason = ''
  }
}

// 重置表单
const resetForm = () => {
  form.reason = ''
  form.customReason = ''
  formRef.value?.clearValidate()
}

// 确认强制下线
const handleConfirm = async () => {
  if (!formRef.value || !props.user) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const reason = form.reason === '其他原因' ? form.customReason : form.reason
    emit('confirm', props.user.id, reason)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.force-logout-dialog {
  .user-info {
    margin-bottom: 20px;
    
    .user-details {
      margin-top: 16px;
      padding: 16px;
      background-color: #f5f7fa;
      border-radius: 6px;
      
      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          width: 80px;
          color: #606266;
          font-size: 14px;
        }
        
        .value {
          color: #303133;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
  
  .warning-notice {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
