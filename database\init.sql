-- 王者荣耀代练任务分发管理系统数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS game_boost_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE game_boost_db;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'gameuser'@'%' IDENTIFIED BY 'gamepass123';

-- 授予权限
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'gameuser'@'%';
GRANT ALL PRIVILEGES ON game_boost_db.* TO 'gameuser'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 创建日志表（用于记录系统操作日志）
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(191),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(191),
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认系统配置
INSERT IGNORE INTO system_configs (config_key, config_value, description) VALUES
('system_name', '王者荣耀代练任务分发管理系统', '系统名称'),
('default_commission_rate', '0.4', '默认佣金比例（40%）'),
('max_task_duration', '168', '最大任务持续时间（小时）'),
('auto_settlement_enabled', 'true', '是否启用自动结算'),
('notification_enabled', 'true', '是否启用通知功能'),
('max_file_size', '5242880', '最大文件上传大小（5MB）'),
('allowed_file_types', '["jpg","jpeg","png","gif","mp4","mov"]', '允许的文件类型');

-- 创建通知表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(191) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    type ENUM('INFO', 'SUCCESS', 'WARNING', 'ERROR') DEFAULT 'INFO',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建文件上传记录表
CREATE TABLE IF NOT EXISTS file_uploads (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(191) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    related_type VARCHAR(50), -- 关联类型：task_progress, user_avatar等
    related_id VARCHAR(191),   -- 关联ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_related (related_type, related_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建统计视图
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.id,
    u.username,
    u.nickname,
    u.role,
    COUNT(DISTINCT t.id) as total_tasks,
    COUNT(DISTINCT CASE WHEN t.status = 'COMPLETED' THEN t.id END) as completed_tasks,
    COALESCE(SUM(CASE WHEN t.status = 'COMPLETED' THEN t.commission END), 0) as total_earnings,
    AVG(CASE WHEN t.status = 'COMPLETED' AND t.actualHours > 0 THEN t.actualHours END) as avg_hours_per_task
FROM users u
LEFT JOIN tasks t ON u.id = t.assigneeId
GROUP BY u.id, u.username, u.nickname, u.role;

-- 创建任务统计视图
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    DATE(t.createdAt) as date,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN t.status = 'PENDING' THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN t.status = 'IN_PROGRESS' THEN 1 END) as in_progress_tasks,
    COUNT(CASE WHEN t.status = 'COMPLETED' THEN 1 END) as completed_tasks,
    AVG(t.commission) as avg_commission
FROM tasks t
GROUP BY DATE(t.createdAt)
ORDER BY date DESC;

-- 创建索引优化查询性能
-- 用户表索引
ALTER TABLE users ADD INDEX idx_role (role);
ALTER TABLE users ADD INDEX idx_status (status);
ALTER TABLE users ADD INDEX idx_created_at (createdAt);

-- 订单表索引
ALTER TABLE orders ADD INDEX idx_status (status);
ALTER TABLE orders ADD INDEX idx_priority (priority);
ALTER TABLE orders ADD INDEX idx_created_by (createdById);
ALTER TABLE orders ADD INDEX idx_deadline (deadline);

-- 任务表索引
ALTER TABLE tasks ADD INDEX idx_status (status);
ALTER TABLE tasks ADD INDEX idx_assign_type (assignType);
ALTER TABLE tasks ADD INDEX idx_order_id (orderId);
ALTER TABLE tasks ADD INDEX idx_assignee_id (assigneeId);
ALTER TABLE tasks ADD INDEX idx_created_at (createdAt);

-- 任务进度表索引
ALTER TABLE task_progress ADD INDEX idx_task_id (taskId);
ALTER TABLE task_progress ADD INDEX idx_user_id (userId);
ALTER TABLE task_progress ADD INDEX idx_created_at (createdAt);

-- 结算记录表索引
ALTER TABLE settlements ADD INDEX idx_status (status);
ALTER TABLE settlements ADD INDEX idx_user_id (userId);
ALTER TABLE settlements ADD INDEX idx_settled_at (settledAt);

COMMIT;
