#!/bin/bash

# 宝塔面板环境检查脚本
# 确保所有必要的软件都已正确安装和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_check() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

# 检查宝塔面板
check_baota() {
    log_check "检查宝塔面板..."
    
    if command -v bt &> /dev/null; then
        BT_VERSION=$(bt version 2>/dev/null || echo "未知版本")
        log_info "宝塔面板已安装: $BT_VERSION"
        
        # 检查宝塔面板服务状态
        if systemctl is-active --quiet bt; then
            log_info "宝塔面板服务运行正常"
        else
            log_warn "宝塔面板服务未运行，请检查"
        fi
    else
        log_error "宝塔面板未安装"
        echo "请先安装宝塔面板："
        echo "wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh"
        return 1
    fi
}

# 检查Node.js
check_nodejs() {
    log_check "检查Node.js..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
        
        log_info "Node.js版本: $NODE_VERSION"
        
        if [ "$NODE_MAJOR" -ge 18 ]; then
            log_info "Node.js版本符合要求 (>=18)"
        else
            log_error "Node.js版本过低，需要18+版本"
            echo "请在宝塔面板 -> 软件商店 -> 运行环境 中安装Node.js 18+"
            return 1
        fi
        
        # 检查npm
        if command -v npm &> /dev/null; then
            NPM_VERSION=$(npm --version)
            log_info "npm版本: $NPM_VERSION"
        else
            log_error "npm未安装"
            return 1
        fi
    else
        log_error "Node.js未安装"
        echo "请在宝塔面板 -> 软件商店 -> 运行环境 中安装Node.js 18+"
        return 1
    fi
}

# 检查MySQL
check_mysql() {
    log_check "检查MySQL..."
    
    if command -v mysql &> /dev/null; then
        MYSQL_VERSION=$(mysql --version)
        log_info "MySQL版本: $MYSQL_VERSION"
        
        # 检查MySQL服务状态
        if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
            log_info "MySQL服务运行正常"
            
            # 检查MySQL连接（尝试常见密码）
            if mysql -u root -p123456 -e "SELECT 1;" &> /dev/null 2>&1; then
                log_info "MySQL root用户连接正常（密码：123456）"
            elif mysql -u root -e "SELECT 1;" &> /dev/null 2>&1; then
                log_info "MySQL root用户连接正常（无密码）"
            else
                log_warn "MySQL root用户连接失败，部署时会提示输入密码"
            fi
        else
            log_error "MySQL服务未运行"
            return 1
        fi
    else
        log_error "MySQL未安装"
        echo "请在宝塔面板 -> 软件商店 -> 数据库 中安装MySQL"
        return 1
    fi
}

# 检查Redis
check_redis() {
    log_check "检查Redis..."
    
    if command -v redis-server &> /dev/null; then
        REDIS_VERSION=$(redis-server --version)
        log_info "Redis版本: $REDIS_VERSION"
        
        # 检查Redis服务状态（宝塔面板的Redis可能使用不同的服务名）
        if systemctl is-active --quiet redis || systemctl is-active --quiet redis-server || pgrep redis-server &> /dev/null || pgrep redis &> /dev/null; then
            log_info "Redis服务运行正常"

            # 检查Redis连接
            if timeout 3 redis-cli ping &> /dev/null; then
                log_info "Redis连接正常"
            elif timeout 3 redis-cli -p 6379 ping &> /dev/null; then
                log_info "Redis连接正常（端口6379）"
            else
                log_warn "Redis连接测试失败，但服务正在运行"
            fi
        else
            log_warn "Redis服务状态检测失败，但可能已通过宝塔面板启动"
            # 尝试直接连接测试
            if timeout 3 redis-cli ping &> /dev/null; then
                log_info "Redis连接正常（直接连接成功）"
            else
                log_error "Redis服务未运行或连接失败"
                return 1
            fi
        fi
    else
        log_error "Redis未安装"
        echo "请在宝塔面板 -> 软件商店 -> 数据库 中安装Redis"
        return 1
    fi
}

# 检查Nginx
check_nginx() {
    log_check "检查Nginx..."
    
    if command -v nginx &> /dev/null; then
        NGINX_VERSION=$(nginx -v 2>&1)
        log_info "Nginx版本: $NGINX_VERSION"
        
        # 检查Nginx服务状态
        if systemctl is-active --quiet nginx; then
            log_info "Nginx服务运行正常"
            
            # 检查Nginx配置
            if nginx -t &> /dev/null; then
                log_info "Nginx配置文件语法正确"
            else
                log_warn "Nginx配置文件有语法错误"
            fi
        else
            log_error "Nginx服务未运行"
            return 1
        fi
    else
        log_error "Nginx未安装"
        echo "请在宝塔面板 -> 软件商店 -> Web服务器 中安装Nginx"
        return 1
    fi
}

# 检查PM2
check_pm2() {
    log_check "检查PM2..."
    
    if command -v pm2 &> /dev/null; then
        PM2_VERSION=$(pm2 --version)
        log_info "PM2版本: $PM2_VERSION"
    else
        log_warn "PM2未安装，将在部署时自动安装"
    fi
}

# 检查系统资源
check_system_resources() {
    log_check "检查系统资源..."
    
    # 检查内存
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.1f", $7/1024}')
    log_info "总内存: ${TOTAL_MEM}GB, 可用内存: ${AVAILABLE_MEM}GB"
    
    if (( $(echo "$TOTAL_MEM < 2.0" | bc -l) )); then
        log_warn "内存不足2GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $4}')
    log_info "根目录可用空间: $DISK_USAGE"
    
    # 检查CPU核心数
    CPU_CORES=$(nproc)
    log_info "CPU核心数: $CPU_CORES"
}

# 检查端口占用
check_ports() {
    log_check "检查端口占用..."
    
    PORTS=(80 443 3000 3306 6379 8888)
    
    for port in "${PORTS[@]}"; do
        if netstat -tuln | grep ":$port " &> /dev/null; then
            case $port in
                80|443) log_info "端口 $port 已被占用（正常，Nginx使用）" ;;
                3306) log_info "端口 $port 已被占用（正常，MySQL使用）" ;;
                6379) log_info "端口 $port 已被占用（正常，Redis使用）" ;;
                8888) log_info "端口 $port 已被占用（正常，宝塔面板使用）" ;;
                3000) log_warn "端口 $port 已被占用，可能需要停止现有服务" ;;
            esac
        else
            log_info "端口 $port 可用"
        fi
    done
}

# 主检查函数
main() {
    log_info "开始检查宝塔面板部署环境..."
    echo "========================================"
    
    ERRORS=0
    
    check_baota || ((ERRORS++))
    echo "----------------------------------------"
    
    check_nodejs || ((ERRORS++))
    echo "----------------------------------------"
    
    check_mysql || ((ERRORS++))
    echo "----------------------------------------"
    
    check_redis || ((ERRORS++))
    echo "----------------------------------------"
    
    check_nginx || ((ERRORS++))
    echo "----------------------------------------"
    
    check_pm2
    echo "----------------------------------------"
    
    check_system_resources
    echo "----------------------------------------"
    
    check_ports
    echo "========================================"
    
    if [ $ERRORS -eq 0 ]; then
        log_info "环境检查完成，所有必要组件都已就绪！"
        log_info "可以开始部署王者荣耀代练系统"
        return 0
    else
        log_error "发现 $ERRORS 个问题，请先解决后再进行部署"
        return 1
    fi
}

# 执行检查
main "$@"
