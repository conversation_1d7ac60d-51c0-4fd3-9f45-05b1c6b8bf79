import { prisma } from '../config/database';
import { OrderStatus, TaskStatus, UserRole } from '@prisma/client';

export class StatisticsService {
  // 获取综合统计数据
  async getOverviewStats(createdById?: string) {
    const orderWhere = createdById ? { createdById } : {};
    const taskWhere = createdById ? { 
      order: { createdById } 
    } : {};

    // 订单统计
    const totalOrders = await prisma.order.count({ where: orderWhere });
    const pendingOrders = await prisma.order.count({ 
      where: { ...orderWhere, status: OrderStatus.PENDING } 
    });
    const completedOrders = await prisma.order.count({ 
      where: { ...orderWhere, status: OrderStatus.COMPLETED } 
    });
    const inProgressOrders = await prisma.order.count({ 
      where: { ...orderWhere, status: OrderStatus.IN_PROGRESS } 
    });

    // 任务统计
    const totalTasks = await prisma.task.count({ where: taskWhere });
    const activeTasks = await prisma.task.count({ 
      where: { ...taskWhere, status: TaskStatus.IN_PROGRESS } 
    });
    const completedTasks = await prisma.task.count({ 
      where: { ...taskWhere, status: TaskStatus.COMPLETED } 
    });
    const pendingTasks = await prisma.task.count({ 
      where: { ...taskWhere, status: TaskStatus.PENDING } 
    });

    // 员工统计
    const totalEmployees = await prisma.user.count({ 
      where: { role: UserRole.EMPLOYEE } 
    });
    const activeEmployees = await prisma.user.count({ 
      where: { 
        role: UserRole.EMPLOYEE,
        assignedTasks: {
          some: {
            status: TaskStatus.IN_PROGRESS
          }
        }
      } 
    });

    // 收益统计
    const totalRevenue = await prisma.order.aggregate({
      where: { ...orderWhere, status: OrderStatus.COMPLETED },
      _sum: { price: true }
    });

    const totalCommission = await prisma.task.aggregate({
      where: { ...taskWhere, status: TaskStatus.COMPLETED },
      _sum: { commission: true }
    });

    // 本月统计
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlyOrders = await prisma.order.count({
      where: {
        ...orderWhere,
        createdAt: { gte: currentMonth }
      }
    });

    const monthlyRevenue = await prisma.order.aggregate({
      where: {
        ...orderWhere,
        status: OrderStatus.COMPLETED,
        createdAt: { gte: currentMonth }
      },
      _sum: { price: true }
    });

    return {
      overview: {
        totalOrders,
        pendingOrders,
        inProgressOrders,
        completedOrders,
        totalTasks,
        pendingTasks,
        activeTasks,
        completedTasks,
        totalEmployees,
        activeEmployees,
        totalRevenue: totalRevenue._sum.price || 0,
        totalCommission: totalCommission._sum.commission || 0,
      },
      monthly: {
        orders: monthlyOrders,
        revenue: monthlyRevenue._sum.price || 0,
      },
      rates: {
        orderCompletionRate: totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0,
        taskCompletionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        employeeUtilizationRate: totalEmployees > 0 ? Math.round((activeEmployees / totalEmployees) * 100) : 0,
      }
    };
  }

  // 获取收益分析数据
  async getRevenueAnalysis(period: string, createdById?: string) {
    const days = this.getPeriodDays(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    const orderWhere = createdById ? { createdById } : {};

    // 修复：使用原始SQL查询按日期分组，而不是按完整时间戳
    let dailyRevenueRaw;
    if (createdById) {
      dailyRevenueRaw = await prisma.$queryRaw`
        SELECT
          DATE(createdAt) as date,
          SUM(price) as revenue,
          COUNT(*) as count
        FROM orders
        WHERE status = 'COMPLETED'
          AND createdAt >= ${startDate}
          AND createdById = ${createdById}
        GROUP BY DATE(createdAt)
        ORDER BY DATE(createdAt) ASC
      `;
    } else {
      dailyRevenueRaw = await prisma.$queryRaw`
        SELECT
          DATE(createdAt) as date,
          SUM(price) as revenue,
          COUNT(*) as count
        FROM orders
        WHERE status = 'COMPLETED'
          AND createdAt >= ${startDate}
        GROUP BY DATE(createdAt)
        ORDER BY DATE(createdAt) ASC
      `;
    }

    // 按优先级分组的收益
    const revenueByPriority = await prisma.order.groupBy({
      by: ['priority'],
      where: {
        ...orderWhere,
        status: OrderStatus.COMPLETED,
        createdAt: { gte: startDate }
      },
      _sum: { price: true },
      _count: { id: true }
    });

    // 格式化每日收益数据
    const dailyRevenue = this.formatDailyRevenueData(dailyRevenueRaw as any[], days);

    // 计算总收益
    const totalPeriodRevenue = dailyRevenue.reduce((sum: number, item: any) => sum + item.value, 0);

    return {
      dailyRevenue,
      revenueByPriority: revenueByPriority.map(item => ({
        priority: item.priority,
        revenue: item._sum.price || 0,
        count: item._count.id
      })),
      totalPeriodRevenue
    };
  }

  // 获取员工绩效数据
  async getEmployeePerformance(createdById?: string) {
    const taskWhere = createdById ? { 
      order: { createdById } 
    } : {};

    const employeeStats = await prisma.user.findMany({
      where: { role: UserRole.EMPLOYEE },
      select: {
        id: true,
        username: true,
        nickname: true,
        level: true,
        totalEarnings: true,
        assignedTasks: {
          where: taskWhere,
          select: {
            id: true,
            status: true,
            commission: true,
            actualHours: true,
            createdAt: true,
            endTime: true
          }
        }
      }
    });

    return employeeStats.map(employee => {
      const tasks = employee.assignedTasks;
      // 修复：统计已审核通过和已完成的任务
      const completedTasks = tasks.filter(t =>
        t.status === TaskStatus.APPROVED || t.status === TaskStatus.COMPLETED
      );
      const totalTasks = tasks.length;
      const totalCommission = completedTasks.reduce((sum, t) => sum + (t.commission || 0), 0);

      // 优化工时计算：如果actualHours为空但有时间记录，则计算工时
      const totalHours = completedTasks.reduce((sum, t) => {
        if (t.actualHours && t.actualHours > 0) {
          return sum + t.actualHours;
        } else if (t.endTime && tasks.find(task => task.id === t.id)?.createdAt) {
          // 如果没有actualHours但有结束时间，使用创建时间到结束时间的差值作为估算
          const startTime = new Date(tasks.find(task => task.id === t.id)?.createdAt || 0);
          const endTime = new Date(t.endTime);
          const diffInHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
          // 设置最小工时为0.1小时（6分钟），避免过小的工时导致效率异常高
          const calculatedHours = Math.max(0.1, Math.round(diffInHours * 100) / 100);
          return sum + calculatedHours;
        } else {
          // 如果没有任何时间记录，默认设置为1小时
          return sum + 1.0;
        }
      }, 0);

      const avgHoursPerTask = completedTasks.length > 0 ? totalHours / completedTasks.length : 0;
      const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

      // 计算效率，添加合理性检查
      let efficiency = 0;
      if (totalHours > 0 && totalCommission > 0) {
        const rawEfficiency = totalCommission / totalHours;
        // 设置效率上限为1000元/小时，避免显示异常高的效率值
        efficiency = Math.min(rawEfficiency, 1000);
        efficiency = Math.round(efficiency * 100) / 100;
      }

      return {
        id: employee.id,
        username: employee.username,
        nickname: employee.nickname,
        level: employee.level,
        totalEarnings: employee.totalEarnings,
        performance: {
          totalTasks,
          completedTasks: completedTasks.length,
          totalCommission,
          totalHours: Math.round(totalHours * 100) / 100,
          avgHoursPerTask: Math.round(avgHoursPerTask * 100) / 100,
          completionRate: Math.round(completionRate),
          efficiency
        }
      };
    });
  }

  // 获取订单趋势数据
  async getOrderTrends(period: string, createdById?: string) {
    const days = this.getPeriodDays(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    const orderWhere = createdById ? { createdById } : {};

    // 修复：使用原始SQL查询按日期分组订单数量
    let dailyOrdersRaw;
    if (createdById) {
      dailyOrdersRaw = await prisma.$queryRaw`
        SELECT
          DATE(createdAt) as date,
          COUNT(*) as count
        FROM orders
        WHERE createdAt >= ${startDate}
          AND createdById = ${createdById}
        GROUP BY DATE(createdAt)
        ORDER BY DATE(createdAt) ASC
      `;
    } else {
      dailyOrdersRaw = await prisma.$queryRaw`
        SELECT
          DATE(createdAt) as date,
          COUNT(*) as count
        FROM orders
        WHERE createdAt >= ${startDate}
        GROUP BY DATE(createdAt)
        ORDER BY DATE(createdAt) ASC
      `;
    }

    // 按状态分组的订单数量
    const ordersByStatus = await prisma.order.groupBy({
      by: ['status'],
      where: {
        ...orderWhere,
        createdAt: { gte: startDate }
      },
      _count: { id: true }
    });

    return {
      dailyOrders: this.formatDailyOrderData(dailyOrdersRaw as any[], days),
      ordersByStatus: ordersByStatus.map(item => ({
        status: item.status,
        count: item._count.id
      }))
    };
  }

  // 辅助方法：获取周期天数
  private getPeriodDays(period: string): number {
    switch (period) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      default: return 30;
    }
  }

  // 辅助方法：格式化每日收益数据
  private formatDailyRevenueData(data: any[], days: number): any[] {
    const result = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      // 查找对应日期的数据
      const dayData = data.find(item => {
        const itemDate = new Date(item.date).toISOString().split('T')[0];
        return itemDate === dateStr;
      });

      result.push({
        date: dateStr,
        value: dayData ? Number(dayData.revenue) || 0 : 0
      });
    }

    return result;
  }

  // 辅助方法：格式化每日订单数据
  private formatDailyOrderData(data: any[], days: number): any[] {
    const result = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      // 查找对应日期的数据
      const dayData = data.find(item => {
        const itemDate = new Date(item.date).toISOString().split('T')[0];
        return itemDate === dateStr;
      });

      result.push({
        date: dateStr,
        value: dayData ? Number(dayData.count) || 0 : 0
      });
    }

    return result;
  }

  // 辅助方法：格式化每日数据（保留原方法以兼容其他地方的调用）
  private formatDailyData(data: any[], days: number, field: string = '_sum'): any[] {
    const result = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayData = data.find(item => {
        const itemDate = new Date(item.createdAt).toISOString().split('T')[0];
        return itemDate === dateStr;
      });

      result.push({
        date: dateStr,
        value: dayData ? (field === '_count' ? dayData[field].id : dayData[field].price || 0) : 0
      });
    }

    return result;
  }
}
