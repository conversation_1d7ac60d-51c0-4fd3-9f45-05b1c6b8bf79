import { PrismaClient } from '@prisma/client';

// Prisma客户端实例
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
});

// 数据库连接函数
export async function connectDatabase() {
  try {
    // 连接Prisma
    await prisma.$connect();

    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1`;

    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 断开数据库连接
export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ 断开数据库连接时出错:', error);
  }
}

// 数据库健康检查
export async function checkDatabaseHealth() {
  try {
    // 检查MySQL连接
    await prisma.$queryRaw`SELECT 1`;

    return {
      mysql: true,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('数据库健康检查失败:', error);
    return {
      mysql: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
}

// 优雅关闭处理
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});
