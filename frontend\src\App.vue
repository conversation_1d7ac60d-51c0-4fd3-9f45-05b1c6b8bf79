<template>
  <div id="app">
    <router-view />

    <!-- 全局组件 -->
    <GlobalLoading />
    <GlobalError />
    <NetworkStatus />
  </div>
</template>

<script setup lang="ts">
// 由于使用了auto-import，不需要手动导入Vue的组合式API
import { onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth'
import GlobalLoading from '@/components/GlobalLoading.vue'
import GlobalError from '@/components/GlobalError.vue'
import NetworkStatus from '@/components/NetworkStatus.vue'

const authStore = useAuthStore()

onMounted(() => {
  // 应用启动时检查登录状态
  authStore.checkAuth()
})
</script>

<style lang="scss">
#app {
  height: 100vh;
  width: 100vw;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.card-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
