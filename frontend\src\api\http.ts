import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { useAppStore } from '@/stores/app'
import { requestManager } from '@/utils/requestManager'
import type { ApiResponse } from '@/types'

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 获取app store实例
let appStore: ReturnType<typeof useAppStore> | null = null
const getAppStore = () => {
  if (!appStore) {
    try {
      appStore = useAppStore()
    } catch (error) {
      // 在某些情况下store可能还未初始化
      console.warn('App store not available:', error)
    }
  }
  return appStore
}

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 显示全局加载状态（对于非静默请求）
    const store = getAppStore()
    if (store && !config.headers?.['X-Silent']) {
      store.setLoading(true)
    }

    // 添加认证token
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    // 隐藏加载状态
    const store = getAppStore()
    if (store) {
      store.setLoading(false)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 隐藏全局加载状态
    const store = getAppStore()
    if (store) {
      store.setLoading(false)
    }

    // 直接返回响应数据
    return response.data
  },
  (error) => {
    // 隐藏全局加载状态
    const store = getAppStore()
    if (store) {
      store.setLoading(false)
    }

    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          ElMessage.error('登录已过期，请重新登录')
          router.push('/login')
          break
          
        case 403:
          // 权限不足 - 显示具体的错误信息
          const forbiddenMessage = data?.error?.message || '权限不足'
          ElMessage.error(forbiddenMessage)
          break

        case 404:
          // 资源未找到 - 显示具体的错误信息
          const notFoundMessage = data?.error?.message || '请求的资源不存在'
          ElMessage.error(notFoundMessage)
          break
          
        case 400:
          // 请求错误 - 显示具体的错误信息
          const badRequestMessage = data?.error?.message || '请求参数有误'
          ElMessage.error(badRequestMessage)
          break

        case 422:
          // 验证错误
          const message = data?.error?.message || '输入数据有误'
          ElMessage.error(message)
          break

        case 429:
          // 请求过于频繁
          const rateLimitMessage = data?.error?.message || '请求过于频繁，请稍后再试'
          ElMessage.error(rateLimitMessage)
          break

        case 500:
          // 服务器错误
          ElMessage.error('服务器内部错误')
          break

        default:
          // 其他错误
          const errorMessage = data?.error?.message || '请求失败'
          ElMessage.error(errorMessage)
      }
      
      // 返回格式化的错误
      return Promise.reject({
        status,
        message: data?.error?.message || '请求失败',
        data: data?.error
      })
    } else if (error.request) {
      // 网络错误
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject({
        status: 0,
        message: '网络连接失败',
        data: null
      })
    } else {
      // 其他错误
      ElMessage.error('请求配置错误')
      return Promise.reject({
        status: 0,
        message: '请求配置错误',
        data: null
      })
    }
  }
)

// 通用请求方法 - 集成请求管理器
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const requestConfig = { ...config, method: 'GET', url }

    return requestManager.handleRequest(
      requestConfig,
      () => http.get(url, config),
      {
        enableDeduplication: true,
        enableDebounce: requestManager.shouldThrottleRequest(requestConfig)
      }
    )
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const requestConfig = { ...config, method: 'POST', url, data }

    return requestManager.handleRequest(
      requestConfig,
      () => http.post(url, data, config),
      {
        enableDeduplication: !url.includes('/auth/login'), // 登录请求不去重
        enableDebounce: false // POST请求通常不需要防抖
      }
    )
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const requestConfig = { ...config, method: 'PUT', url, data }

    return requestManager.handleRequest(
      requestConfig,
      () => http.put(url, data, config),
      {
        enableDeduplication: true,
        enableDebounce: false
      }
    )
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const requestConfig = { ...config, method: 'DELETE', url }

    return requestManager.handleRequest(
      requestConfig,
      () => http.delete(url, config),
      {
        enableDeduplication: true,
        enableDebounce: false
      }
    )
  },

  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const requestConfig = { ...config, method: 'PATCH', url, data }

    return requestManager.handleRequest(
      requestConfig,
      () => http.patch(url, data, config),
      {
        enableDeduplication: true,
        enableDebounce: false
      }
    )
  }
}

// 文件上传请求
export const uploadRequest = (url: string, formData: FormData, onProgress?: (progress: number) => void) => {
  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

export default http
