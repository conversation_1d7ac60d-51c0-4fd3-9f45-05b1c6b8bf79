import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '../middleware/errorHandler';

// 字段验证规则类型定义
interface BaseValidationRule {
  required: boolean;
  message: string;
}

interface StringValidationRule extends BaseValidationRule {
  type?: 'string' | 'url';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
}

interface NumberValidationRule extends BaseValidationRule {
  type: 'number';
  min?: number;
  max?: number;
}

type ValidationRule = StringValidationRule | NumberValidationRule;

// 统一的字段验证规则配置
export const FIELD_VALIDATION_RULES: Record<string, ValidationRule> = {
  // 基础字段规则
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: '名称只能包含字母、数字、下划线和连字符'
  },
  displayName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '显示名称长度在 2 到 100 个字符'
  },
  description: {
    required: false,
    maxLength: 500,
    message: '描述长度不能超过 500 个字符'
  },
  icon: {
    required: false,
    type: 'url',
    message: '请输入有效的URL地址'
  },
  // 订单相关字段规则
  customerName: {
    required: true,
    minLength: 1,
    maxLength: 100,
    message: '客户姓名长度应在1-100个字符之间'
  },
  customerContact: {
    required: false,
    maxLength: 50,
    message: '联系方式长度不能超过50个字符'
  },
  gameAccount: {
    required: true,
    minLength: 1,
    maxLength: 100,
    message: '游戏账号长度应在1-100个字符之间'
  },
  gamePassword: {
    required: true,
    minLength: 1,
    maxLength: 100,
    message: '游戏密码长度应在1-100个字符之间'
  },
  price: {
    required: true,
    type: 'number',
    min: 0.01,
    message: '价格必须大于0'
  },
  requirements: {
    required: false,
    maxLength: 500,
    message: '特殊要求长度不能超过500个字符'
  }
};

// 创建统一的Joi验证规则
export function createFieldValidation(fieldName: keyof typeof FIELD_VALIDATION_RULES) {
  const rule = FIELD_VALIDATION_RULES[fieldName];
  let schema: any;

  if (rule.type === 'number') {
    schema = Joi.number();
    const numberRule = rule as NumberValidationRule;
    if (numberRule.min !== undefined) {
      schema = schema.min(numberRule.min);
    }
    if (numberRule.max !== undefined) {
      schema = schema.max(numberRule.max);
    }
  } else if (rule.type === 'url') {
    schema = Joi.string().uri();
  } else {
    schema = Joi.string();
    const stringRule = rule as StringValidationRule;
    if (stringRule.minLength !== undefined) {
      schema = schema.min(stringRule.minLength);
    }
    if (stringRule.maxLength !== undefined) {
      schema = schema.max(stringRule.maxLength);
    }
    if (stringRule.pattern) {
      schema = schema.pattern(stringRule.pattern);
    }
  }

  if (rule.required) {
    schema = schema.required();
  } else {
    schema = schema.optional().allow('');
  }

  return schema.messages({
    'string.empty': rule.message,
    'string.min': rule.message,
    'string.max': rule.message,
    'string.pattern.base': rule.message,
    'string.uri': rule.message,
    'number.min': rule.message,
    'any.required': `${fieldName}是必需的`,
  });
}

// 验证中间件
export function validate(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.body = value;
    next();
  };
}

// 查询参数验证
export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.query = value;
    next();
  };
}

// 路径参数验证
export function validateParams(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.params = value;
    next();
  };
}

// 通用验证规则
export const commonSchemas = {
  // ID验证
  id: Joi.string().required().messages({
    'string.empty': 'ID不能为空',
    'any.required': 'ID是必需的',
  }),

  // 分页参数
  pagination: Joi.object({
    page: Joi.alternatives().try(
      Joi.number().integer().min(1),
      Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).default(1),
    limit: Joi.alternatives().try(
      Joi.number().integer().min(1).max(100),
      Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).default(10),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // 搜索参数
  search: Joi.object({
    keyword: Joi.string().optional(),
    status: Joi.string().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  }),
};

// 认证相关验证
export const authSchemas = {
  login: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少3个字符',
      'string.max': '用户名最多30个字符',
      'any.required': '用户名是必需的',
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': '密码至少6个字符',
      'any.required': '密码是必需的',
    }),
  }),

  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: Joi.string().min(6).required(),
    nickname: Joi.string().max(50).optional(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional().messages({
      'string.pattern.base': '请输入有效的手机号码',
    }),
    role: Joi.string().valid('ADMIN', 'BOSS', 'EMPLOYEE').optional(),
  }),

  updatePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': '当前密码是必需的',
    }),
    newPassword: Joi.string().min(6).required().messages({
      'string.min': '新密码至少6个字符',
      'any.required': '新密码是必需的',
    }),
  }),
};

// 游戏类型配置 - 定义哪些游戏需要段位字段
const gameTypeConfigs = {
  // 有段位系统的游戏
  wzry: { hasRanks: true, displayName: '王者荣耀' },
  lol: { hasRanks: true, displayName: '英雄联盟' },
  pubg: { hasRanks: true, displayName: '绝地求生' },

  // 无段位系统的游戏
  ys: { hasRanks: false, displayName: '原神' },
  mc: { hasRanks: false, displayName: '鸣潮' },
  default: { hasRanks: false, displayName: '其他游戏' }
};

// 动态订单验证函数
export function createOrderValidationSchema(gameType?: string) {
  const gameConfig = gameTypeConfigs[gameType as keyof typeof gameTypeConfigs] || gameTypeConfigs.default;

  console.log(`创建订单验证Schema - 游戏类型: ${gameType}, 配置:`, gameConfig);

  const baseSchema = {
    // 使用统一的验证规则
    customerName: createFieldValidation('customerName'),
    customerContact: createFieldValidation('customerContact'),
    gameAccount: createFieldValidation('gameAccount'),
    gamePassword: createFieldValidation('gamePassword'),
    price: createFieldValidation('price'),
    gameType: Joi.string().required().messages({
      'any.required': '游戏类型是必需的',
    }),
    deadline: Joi.date().iso().optional(),
    requirements: createFieldValidation('requirements'),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional().default('NORMAL'),

    // 动态表单驱动系统核心字段
    details: Joi.object().optional(),  // 核心：存储动态表单数据
    formData: Joi.object().optional(), // 兼容字段：保持向后兼容

    // 其他可选字段
    gameId: Joi.string().optional(),
    currentRankId: Joi.string().allow('').optional(),
    targetRankId: Joi.string().allow('').optional(),
    templateId: Joi.string().optional(),
    templateVersion: Joi.number().optional(),
  };

  // 移除段位字段，不再支持段位相关功能
  console.log(`游戏 ${gameConfig.displayName} - 已移除段位字段`);

  return Joi.object(baseSchema);
}

// 动态订单验证中间件
export function validateOrderCreate(req: Request, res: Response, next: NextFunction) {
  const gameType = req.body.gameType;

  // 调试日志
  console.log('验证订单数据 - 原始请求体:', JSON.stringify(req.body, null, 2));
  console.log('游戏类型:', gameType);

  const schema = createOrderValidationSchema(gameType);

  const { error, value } = schema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    console.log('验证失败 - 错误详情:', error.details);
    const errorMessage = error.details
      .map(detail => detail.message)
      .join(', ');
    throw new ValidationError(errorMessage);
  }

  console.log('验证成功 - 处理后的数据:', JSON.stringify(value, null, 2));
  req.body = value;
  next();
}

// 订单相关验证（保留原有的静态验证作为备用）- 使用统一的验证规则
export const orderSchemas = {
  create: Joi.object({
    customerName: createFieldValidation('customerName'),
    customerContact: createFieldValidation('customerContact'),
    gameAccount: createFieldValidation('gameAccount'),
    gamePassword: createFieldValidation('gamePassword'),
    price: createFieldValidation('price'),
    deadline: Joi.date().iso().greater('now').optional().messages({
      'date.greater': '截止时间必须晚于当前时间',
    }),
    requirements: createFieldValidation('requirements'),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').default('NORMAL'),
  }),

  update: Joi.object({
    customerName: createFieldValidation('customerName').optional(),
    customerContact: createFieldValidation('customerContact'),
    price: createFieldValidation('price').optional(),
    deadline: Joi.date().iso().optional(),
    requirements: createFieldValidation('requirements'),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional(),
    status: Joi.string().valid('PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED').optional(),
  }),
};

// 任务相关验证
export const taskSchemas = {
  create: Joi.object({
    orderId: Joi.string().required(),
    assigneeId: Joi.string().optional(),
    assignType: Joi.string().valid('DIRECT', 'SYSTEM').required(),
    estimatedHours: Joi.number().integer().positive().optional(),
    commission: Joi.number().positive().optional(),
    description: Joi.string().max(500).allow('').optional(),
    notes: Joi.string().max(500).allow('').optional(),
  }),

  update: Joi.object({
    assigneeId: Joi.string().optional(),
    status: Joi.string().valid('PENDING', 'ACCEPTED', 'IN_PROGRESS', 'SUBMITTED', 'APPROVED', 'REJECTED', 'COMPLETED', 'CANCELLED').optional(),
    estimatedHours: Joi.number().integer().positive().optional(),
    actualHours: Joi.number().integer().positive().optional(),
    commission: Joi.number().positive().optional(),
    description: Joi.string().max(500).allow('').optional(),
    notes: Joi.string().max(500).allow('').optional(),
  }),

  updateProgress: Joi.object({
    progress: Joi.number().integer().min(0).max(100).required(),
    description: Joi.string().max(500).allow('').optional(),
    screenshots: Joi.array().items(Joi.string()).optional(),
  }),

  review: Joi.object({
    approved: Joi.boolean().required(),
    feedback: Joi.string().max(500).allow('').optional(),
  }),
};
