const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateMcTemplate() {
  try {
    console.log('🔍 查找鸣潮模板...');
    
    // 查找现有的鸣潮模板
    let mcTemplate = await prisma.orderTemplate.findFirst({
      where: { gameType: 'mc' }
    });
    
    if (!mcTemplate) {
      console.log('❌ 未找到鸣潮模板，创建新模板...');
      
      // 获取管理员用户
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });
      
      if (!adminUser) {
        throw new Error('未找到管理员用户');
      }
      
      // 创建新的鸣潮模板
      const newMcTemplate = {
        name: "鸣潮代练",
        gameType: "mc",
        description: "鸣潮游戏代练订单模板",
        fields: [
          {
            id: "customer_name",
            name: "customerName",
            label: "客户姓名",
            type: "text",
            required: true,
            display: { order: 1, group: "customer_info" }
          },
          {
            id: "customer_contact",
            name: "customerContact",
            label: "联系方式",
            type: "text",
            required: true,
            display: { order: 2, group: "customer_info" }
          },
          {
            id: "game_account",
            name: "gameAccount",
            label: "游戏账号",
            type: "text",
            required: true,
            display: { order: 3, group: "game_info" }
          },
          {
            id: "game_password",
            name: "gamePassword",
            label: "游戏密码",
            type: "text",
            required: true,
            display: { order: 4, group: "game_info" }
          },
          {
            id: "daily_checkin",
            name: "dailyCheckin",
            label: "每日签到",
            type: "checkbox",
            required: false,
            defaultValue: false,
            config: {
              helpText: "是否需要每日签到服务"
            },
            display: { 
              order: 5, 
              group: "service_info",
              helpText: "勾选此项将包含每日签到服务"
            }
          },
          {
            id: "service_type",
            name: "serviceType",
            label: "服务类型",
            type: "select",
            required: true,
            config: {
              options: [
                { value: "level_boost", label: "等级提升" },
                { value: "quest_complete", label: "任务完成" },
                { value: "resource_farm", label: "资源收集" },
                { value: "daily_tasks", label: "日常任务" }
              ]
            },
            display: { order: 6, group: "service_info" }
          },
          {
            id: "current_level",
            name: "currentLevel",
            label: "当前等级",
            type: "number",
            required: true,
            config: { min: 1, max: 100 },
            display: { order: 7, group: "level_info" }
          },
          {
            id: "target_level",
            name: "targetLevel",
            label: "目标等级",
            type: "number",
            required: true,
            config: { min: 1, max: 100 },
            display: { order: 8, group: "level_info" }
          },
          {
            id: "price",
            name: "price",
            label: "订单价格",
            type: "number",
            required: true,
            config: { min: 0.01, precision: 2 },
            display: { order: 9, group: "price_info" }
          }
        ],
        displayConfig: {
          listView: [
            { field: "customerName", width: 100 },
            { field: "currentLevel", width: 80 },
            { field: "targetLevel", width: 80 },
            { field: "serviceType", width: 120 },
            { field: "dailyCheckin", width: 80 },
            { field: "price", width: 80 },
            { field: "status", width: 100 }
          ],
          detailView: [
            { field: "customerName", label: "客户姓名" },
            { field: "customerContact", label: "联系方式" },
            { field: "gameAccount", label: "游戏账号" },
            { field: "currentLevel", label: "当前等级" },
            { field: "targetLevel", label: "目标等级" },
            { field: "serviceType", label: "服务类型" },
            { field: "dailyCheckin", label: "每日签到" },
            { field: "price", label: "订单价格" }
          ],
          createForm: {
            groups: [
              { name: "customer_info", label: "客户信息", columns: 2 },
              { name: "game_info", label: "游戏信息", columns: 2 },
              { name: "level_info", label: "等级信息", columns: 2 },
              { name: "service_info", label: "服务信息", columns: 1 },
              { name: "price_info", label: "价格信息", columns: 1 }
            ]
          }
        }
      };
      
      mcTemplate = await prisma.orderTemplate.create({
        data: {
          ...newMcTemplate,
          createdById: adminUser.id
        }
      });
      
      console.log('✅ 鸣潮模板创建成功:', mcTemplate.id);
    } else {
      console.log('✅ 找到现有鸣潮模板:', mcTemplate.name);
      
      // 检查字段数据类型
      console.log('字段数据类型:', typeof mcTemplate.fields);
      console.log('显示配置数据类型:', typeof mcTemplate.displayConfig);

      // 处理字段数据
      let fields;
      if (typeof mcTemplate.fields === 'string') {
        fields = JSON.parse(mcTemplate.fields);
      } else if (Array.isArray(mcTemplate.fields)) {
        fields = mcTemplate.fields;
      } else {
        console.log('字段数据:', mcTemplate.fields);
        throw new Error('无法解析字段数据');
      }

      const dailyCheckinField = fields.find(f => f.name === 'dailyCheckin' || f.label.includes('每日签到'));

      if (dailyCheckinField) {
        console.log('✅ 模板已包含每日签到字段:', dailyCheckinField.label);
      } else {
        console.log('❌ 模板缺少每日签到字段，正在添加...');

        // 添加每日签到字段
        const newDailyCheckinField = {
          id: "daily_checkin",
          name: "dailyCheckin",
          label: "每日签到",
          type: "checkbox",
          required: false,
          defaultValue: false,
          config: {
            helpText: "是否需要每日签到服务"
          },
          display: {
            order: fields.length + 1,
            group: "service_info",
            helpText: "勾选此项将包含每日签到服务"
          }
        };

        fields.push(newDailyCheckinField);

        // 处理显示配置数据
        let displayConfig;
        if (typeof mcTemplate.displayConfig === 'string') {
          displayConfig = JSON.parse(mcTemplate.displayConfig);
        } else if (typeof mcTemplate.displayConfig === 'object') {
          displayConfig = mcTemplate.displayConfig;
        } else {
          console.log('显示配置数据:', mcTemplate.displayConfig);
          throw new Error('无法解析显示配置数据');
        }

        // 确保显示配置有正确的结构
        if (!displayConfig.listView) displayConfig.listView = [];
        if (!displayConfig.detailView) displayConfig.detailView = [];

        // 添加到列表视图
        if (!displayConfig.listView.find(item => item.field === 'dailyCheckin')) {
          displayConfig.listView.splice(-2, 0, { field: "dailyCheckin", width: 80 });
        }

        // 添加到详情视图
        if (!displayConfig.detailView.find(item => item.field === 'dailyCheckin')) {
          displayConfig.detailView.splice(-1, 0, { field: "dailyCheckin", label: "每日签到" });
        }

        // 更新模板
        await prisma.orderTemplate.update({
          where: { id: mcTemplate.id },
          data: {
            fields: fields,
            displayConfig: displayConfig
          }
        });

        console.log('✅ 每日签到字段添加成功');
      }
    }
    
    console.log('🎉 鸣潮模板更新完成');
    
  } catch (error) {
    console.error('❌ 更新鸣潮模板失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateMcTemplate();
