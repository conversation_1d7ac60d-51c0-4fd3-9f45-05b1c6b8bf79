import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { requireRole } from '../middleware/roleAuth';
import { UserRole } from '@prisma/client';
import {
  getOnlineUsers,
  getUserSessions,
  forceUserLogout,
  getLoginLogs,
  getOnlineUserStats,
  getLoginStats,
  cleanupExpiredSessions,
  getIpLocation
} from '../controllers/onlineUserController';

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取在线用户列表 - BOSS和ADMIN可访问
router.get('/online', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  getOnlineUsers
);

// 获取在线用户统计 - BOSS和ADMIN可访问
router.get('/online/stats', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  getOnlineUserStats
);

// 获取用户会话历史 - BOSS和ADMIN可访问，员工只能查看自己的
router.get('/sessions/:userId', 
  requireRole([UserRole.BOSS, UserRole.ADMIN, UserRole.EMPLOYEE]), 
  getUserSessions
);

// 强制用户下线 - 只有BOSS和ADMIN可以操作
router.post('/sessions/:sessionId/force-logout', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  forceUserLogout
);

// 获取登录日志 - BOSS和ADMIN可访问
router.get('/login-logs', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  getLoginLogs
);

// 获取登录统计 - BOSS和ADMIN可访问
router.get('/login-stats', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  getLoginStats
);

// 清理过期会话 - 只有ADMIN可以操作
router.post('/cleanup-sessions', 
  requireRole([UserRole.ADMIN]), 
  cleanupExpiredSessions
);

// 获取IP地理位置信息 - BOSS和ADMIN可访问
router.get('/ip-location/:ip', 
  requireRole([UserRole.BOSS, UserRole.ADMIN]), 
  getIpLocation
);

export default router;
