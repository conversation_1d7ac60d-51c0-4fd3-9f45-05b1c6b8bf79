import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },

  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true,
      roles: ['ADMIN', 'BOSS', 'EMPLOYEE']
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      roles: ['ADMIN', 'BOSS', 'EMPLOYEE']
    }
  },

  // 管理后台路由
  {
    path: '/boss',
    name: 'Boss',
    component: () => import('@/views/layout/BossLayout.vue'),
    meta: {
      title: '管理后台',
      requiresAuth: true,
      roles: ['BOSS', 'ADMIN']
    },
    children: [
      {
        path: '',
        name: 'BossHome',
        redirect: '/boss/orders'
      },
      {
        path: 'orders',
        name: 'BossOrders',
        component: () => import('@/views/boss/Orders.vue'),
        meta: {
          title: '订单管理',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'orders/create',
        name: 'CreateOrder',
        component: () => import('@/views/boss/CreateOrderNew.vue'),
        meta: {
          title: '创建订单',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'create-order',
        name: 'CreateOrderAlias',
        component: () => import('@/views/boss/CreateOrderNew.vue'),
        meta: {
          title: '创建订单',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'orders/create-improved',
        name: 'CreateOrderImproved',
        component: () => import('@/views/boss/CreateOrderImproved.vue'),
        meta: {
          title: '创建订单 (改进版)',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },


      {
        path: 'tasks',
        name: 'BossTasks',
        component: () => import('@/views/boss/Tasks.vue'),
        meta: {
          title: '任务管理',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'employees',
        name: 'Employees',
        component: () => import('@/views/boss/Employees.vue'),
        meta: {
          title: '员工管理',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('@/views/boss/Statistics.vue'),
        meta: {
          title: '数据统计',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'settlements',
        name: 'Settlements',
        component: () => import('@/views/boss/Settlements.vue'),
        meta: {
          title: '结算管理',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'monitoring',
        name: 'OnlineMonitoring',
        component: () => import('@/views/boss/OnlineMonitoring.vue'),
        meta: {
          title: '在线监控',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'login-logs',
        name: 'LoginLogs',
        component: () => import('@/views/boss/LoginLogs.vue'),
        meta: {
          title: '登录日志',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'monitoring-dashboard',
        name: 'MonitoringDashboard',
        component: () => import('@/views/boss/MonitoringDashboard.vue'),
        meta: {
          title: '监控仪表板',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'games',
        name: 'GameManagement',
        component: () => import('@/views/admin/GameManagement.vue'),
        meta: {
          title: '游戏管理',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      },
      {
        path: 'debug-games',
        name: 'GameDebug',
        component: () => import('@/views/debug/GameDebug.vue'),
        meta: {
          title: '游戏调试',
          requiresAuth: true,
          roles: ['BOSS', 'ADMIN']
        }
      }
    ]
  },
  // 工作台路由
  {
    path: '/employee',
    name: 'Employee',
    component: () => import('@/views/layout/EmployeeLayout.vue'),
    meta: {
      title: '工作台',
      requiresAuth: true,
      roles: ['EMPLOYEE']
    },
    children: [
      {
        path: '',
        name: 'EmployeeHome',
        redirect: '/employee/available-tasks'
      },
      {
        path: 'available-tasks',
        name: 'AvailableTasks',
        component: () => import('@/views/employee/AvailableTasks.vue'),
        meta: {
          title: '可接单任务',
          requiresAuth: true,
          roles: ['EMPLOYEE']
        }
      },
      {
        path: 'my-tasks',
        name: 'MyTasks',
        component: () => import('@/views/employee/MyTasks.vue'),
        meta: {
          title: '我的任务',
          requiresAuth: true,
          roles: ['EMPLOYEE']
        }
      },
      {
        path: 'earnings',
        name: 'Earnings',
        component: () => import('@/views/employee/Earnings.vue'),
        meta: {
          title: '收益统计',
          requiresAuth: true,
          roles: ['EMPLOYEE']
        }
      },
      {
        path: 'orders/:id',
        name: 'EmployeeOrderDetails',
        component: () => import('@/views/employee/OrderDetails.vue'),
        meta: {
          title: '订单详情',
          requiresAuth: true,
          roles: ['EMPLOYEE']
        }
      }
    ]
  },

  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面未找到'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE || 'Ace Platform系统'}`
  }

  const authStore = useAuthStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果没有认证信息，尝试从localStorage恢复
    if (!authStore.isAuthenticated) {
      const isAuthenticated = await authStore.checkAuth()
      if (!isAuthenticated) {
        // 未登录，重定向到登录页
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 检查角色权限
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      if (!authStore.user?.role || !to.meta.roles.includes(authStore.user.role)) {
        // 权限不足，重定向到404
        next('/404')
        return
      }
    }
  } else {
    // 如果已登录用户访问登录页面，重定向到对应的仪表板
    if (authStore.isAuthenticated && to.path === '/login') {
      const redirectPath = authStore.getRedirectPath()
      next(redirectPath)
      return
    }
  }

  next()
})

export default router
