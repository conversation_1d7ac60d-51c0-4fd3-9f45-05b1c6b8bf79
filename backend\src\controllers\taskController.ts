import { Request, Response } from 'express';
import { TaskService } from '../services/taskService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { CreateTaskRequest, UpdateTaskRequest, UpdateTaskProgressRequest, ReviewTaskRequest, CommissionCalculationParams } from '../types/task';
import { getSocketService } from '../services/socketService';
import { CommissionService } from '../services/commissionService';

const taskService = new TaskService();

// 创建任务
export const createTask = asyncHandler(async (req: Request, res: Response) => {
  const taskData: CreateTaskRequest = req.body;

  const task = await taskService.createTask(taskData);

  // 发送Socket通知
  try {
    const socketService = getSocketService();

    if (task.assignType === 'SYSTEM') {
      // 系统挂单，通知所有员工
      await socketService.notifyNewTask(task);
    } else if (task.assigneeId) {
      // 直接分配，通知指定员工
      await socketService.notifyTaskAssignment(task, task.assigneeId);
    }
  } catch (error) {
    console.error('发送Socket通知失败:', error);
  }

  const response: ApiResponse = {
    success: true,
    data: task,
    message: '任务创建成功',
    timestamp: new Date().toISOString(),
  };

  res.status(201).json(response);
});

// 获取任务列表
export const getTasks = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  // 如果是员工，只能查看自己的任务
  if (req.user!.role === 'EMPLOYEE') {
    query.assigneeId = req.user!.id;
  }
  
  const result = await taskService.getTasks(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取任务列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取可接单的任务列表
export const getAvailableTasks = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  const result = await taskService.getAvailableTasks(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取可接单任务成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 接单
export const acceptTask = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const employeeId = req.user!.id;

  const task = await taskService.acceptTask(id, employeeId);

  // 发送Socket通知
  try {
    const socketService = getSocketService();
    await socketService.notifyTaskStatusUpdate(task, task.order?.createdById);
  } catch (error) {
    console.error('发送Socket通知失败:', error);
  }

  const response: ApiResponse = {
    success: true,
    data: task,
    message: '接单成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 获取任务详情
export const getTaskById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const task = await taskService.getTaskById(id);
  
  const response: ApiResponse = {
    success: true,
    data: task,
    message: '获取任务详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新任务信息
export const updateTask = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateTaskRequest = req.body;
  
  const task = await taskService.updateTask(id, updateData);
  
  const response: ApiResponse = {
    success: true,
    data: task,
    message: '任务更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新任务进度
export const updateTaskProgress = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const progressData: UpdateTaskProgressRequest = {
    taskId: id,
    ...req.body,
  };

  const progress = await taskService.updateTaskProgress(progressData, userId);

  // 发送Socket通知
  try {
    const socketService = getSocketService();
    const task = await taskService.getTaskById(id);
    if (task) {
      await socketService.notifyTaskProgressUpdate(task, progress);
    }
  } catch (error) {
    console.error('发送Socket通知失败:', error);
  }

  const response: ApiResponse = {
    success: true,
    data: progress,
    message: '任务进度更新成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 员工更新任务状态
export const updateTaskStatus = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status } = req.body;
  const userId = req.user!.id;

  const task = await taskService.updateTaskStatus(id, status, userId);

  // 发送Socket通知
  try {
    const socketService = getSocketService();
    await socketService.notifyTaskStatusUpdate(task);
  } catch (error) {
    console.error('发送Socket通知失败:', error);
  }

  const response: ApiResponse = {
    success: true,
    data: task,
    message: '任务状态更新成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 审核任务
export const reviewTask = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const reviewerId = req.user!.id;
  const reviewData: ReviewTaskRequest = {
    taskId: id,
    ...req.body,
  };
  
  const task = await taskService.reviewTask(reviewData, reviewerId);
  
  const response: ApiResponse = {
    success: true,
    data: task,
    message: '任务审核完成',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 取消任务
export const cancelTask = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const userRole = req.user!.role;
  
  const task = await taskService.cancelTask(id, userId, userRole);
  
  const response: ApiResponse = {
    success: true,
    data: task,
    message: '任务取消成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取任务统计信息
export const getTaskStats = asyncHandler(async (req: Request, res: Response) => {
  let assigneeId: string | undefined;
  
  // 如果是员工，只能查看自己的统计
  if (req.user!.role === 'EMPLOYEE') {
    assigneeId = req.user!.id;
  }
  
  const stats = await taskService.getTaskStats(assigneeId);
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取任务统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取我的任务
export const getMyTasks = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  const userId = req.user!.id;

  // 只查看自己的任务
  query.assigneeId = userId;

  const result = await taskService.getTasks(query);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取我的任务成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 修复现有任务的工时数据（管理员功能）
export const fixTaskHours = asyncHandler(async (req: Request, res: Response) => {
  const result = await taskService.fixExistingTaskHours();

  const response: ApiResponse = {
    success: true,
    data: result,
    message: `成功修复 ${result.fixedTasks} 个任务的工时数据`,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 计算佣金
export const calculateCommission = asyncHandler(async (req: Request, res: Response) => {
  const params: CommissionCalculationParams = req.body;

  const result = CommissionService.calculateCommission(params);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: '佣金计算成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});


