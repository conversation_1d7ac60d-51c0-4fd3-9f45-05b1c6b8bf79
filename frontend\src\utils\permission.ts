import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { UserRole } from '@/types'

/**
 * 检查用户是否有指定角色
 * @param roles 允许的角色列表
 * @returns 是否有权限
 */
export function hasRole(roles: UserRole | UserRole[]): boolean {
  const authStore = useAuthStore()
  
  if (!authStore.user?.role) {
    return false
  }
  
  const userRole = authStore.user.role
  const allowedRoles = Array.isArray(roles) ? roles : [roles]
  
  return allowedRoles.includes(userRole)
}

/**
 * 检查用户是否是管理员
 * @returns 是否是管理员
 */
export function isAdmin(): boolean {
  return hasRole('ADMIN')
}

/**
 * 检查用户是否是老板
 * @returns 是否是老板
 */
export function isBoss(): boolean {
  return hasRole(['BOSS', 'ADMIN'])
}

/**
 * 检查用户是否是员工
 * @returns 是否是员工
 */
export function isEmployee(): boolean {
  return hasRole('EMPLOYEE')
}

/**
 * 检查用户是否有访问指定路由的权限
 * @param routeRoles 路由要求的角色
 * @returns 是否有权限
 */
export function canAccessRoute(routeRoles?: UserRole[]): boolean {
  if (!routeRoles || routeRoles.length === 0) {
    return true
  }
  
  return hasRole(routeRoles)
}

/**
 * 权限指令 - 用于在模板中控制元素显示
 * 使用方法: v-permission="['ADMIN', 'BOSS']"
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: { value: UserRole | UserRole[] }) {
    const { value } = binding
    
    if (!hasRole(value)) {
      el.style.display = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: { value: UserRole | UserRole[] }) {
    const { value } = binding
    
    if (!hasRole(value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

/**
 * 权限组合式函数
 * @returns 权限相关的方法和状态
 */
export function usePermission() {
  const authStore = useAuthStore()
  
  return {
    hasRole,
    isAdmin,
    isBoss,
    isEmployee,
    canAccessRoute,
    userRole: computed(() => authStore.user?.role),
    isAuthenticated: computed(() => authStore.isAuthenticated)
  }
}
