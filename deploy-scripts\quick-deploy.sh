#!/bin/bash

# 王者荣耀代练系统 - 快速部署脚本
# 专为您的宝塔环境优化（MySQL密码：123456）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
PROJECT_NAME="game-boost"
BACKEND_DIR="/www/wwwroot/${PROJECT_NAME}-backend"
FRONTEND_DIR="/www/wwwroot/${PROJECT_NAME}-frontend"
IP2REGION_DIR="/www/wwwroot/${PROJECT_NAME}-ip2region"
DB_NAME="game_boost_db"
DB_USER="gameuser"
DB_PASS="GameUser123!"
MYSQL_ROOT_PASS="123456"

# 获取服务器IP
get_server_ip() {
    log_step "获取服务器公网IP..."
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
    if [ -z "$SERVER_IP" ]; then
        log_error "无法获取服务器IP，请手动输入："
        read -p "请输入服务器公网IP: " SERVER_IP
    fi
    log_info "服务器IP: $SERVER_IP"
}

# 快速环境检查
quick_check() {
    log_step "快速环境检查..."
    
    # 检查必要命令
    for cmd in node npm mysql redis-cli nginx; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd 未安装，请在宝塔面板安装"
            exit 1
        fi
    done
    
    # 检查Node.js版本
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要18+"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 创建数据库
setup_database() {
    log_step "配置数据库..."
    
    # 测试MySQL连接
    if ! mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT 1;" &> /dev/null; then
        log_error "MySQL连接失败，请检查密码是否为：${MYSQL_ROOT_PASS}"
        exit 1
    fi
    
    # 创建数据库和用户
    mysql -u root -p${MYSQL_ROOT_PASS} << EOF
CREATE DATABASE IF NOT EXISTS ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    log_info "数据库配置完成"
}

# 部署后端
deploy_backend() {
    log_step "部署后端..."
    
    # 清理并创建目录
    rm -rf ${BACKEND_DIR}
    mkdir -p ${BACKEND_DIR}
    
    # 复制文件
    cp -r backend/* ${BACKEND_DIR}/
    
    # 复制IP解析库
    rm -rf ${IP2REGION_DIR}
    cp -r ip2region-master ${IP2REGION_DIR}
    
    cd ${BACKEND_DIR}
    
    # 创建环境配置
    cat > .env << EOF
NODE_ENV=production
DATABASE_URL="mysql://${DB_USER}:${DB_PASS}@localhost:3306/${DB_NAME}?schema=public&sslmode=prefer"
REDIS_HOST=localhost
REDIS_PORT=6379
JWT_SECRET=$(openssl rand -base64 32)
PORT=3000
IP2REGION_PATH=${IP2REGION_DIR}/data/ip2region.xdb
CORS_ORIGIN=http://${SERVER_IP}:8080,http://localhost:8080
EOF
    
    # 安装依赖
    log_info "安装后端依赖..."
    npm install --production
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        npm install -g pm2
    fi
    
    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    npx prisma generate
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    npx prisma db push
    
    log_info "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log_step "部署前端..."
    
    # 清理并创建目录
    rm -rf ${FRONTEND_DIR}
    mkdir -p ${FRONTEND_DIR}
    
    # 复制文件
    cp -r frontend/* ${FRONTEND_DIR}/
    
    cd ${FRONTEND_DIR}
    
    # 修改API配置
    if [ -f "src/config/api.js" ]; then
        sed -i "s|http://localhost:3000|http://${SERVER_IP}:3000|g" src/config/api.js
    fi
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    # 构建项目
    log_info "构建前端项目..."
    npm run build
    
    log_info "前端部署完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 停止现有服务
    pm2 delete game-boost-backend 2>/dev/null || true
    pm2 delete game-boost-frontend 2>/dev/null || true
    
    # 启动后端
    cd ${BACKEND_DIR}
    pm2 start ecosystem.config.js --env production
    
    # 启动前端（先安装serve）
    if ! command -v serve &> /dev/null; then
        npm install -g serve
    fi

    cd ${FRONTEND_DIR}
    pm2 start --name "game-boost-frontend" --interpreter none -- serve -s dist -l 8080
    
    # 保存PM2配置
    pm2 save
    pm2 startup
    
    log_info "服务启动完成"
}

# 显示部署结果
show_result() {
    log_step "部署完成！"
    echo "========================================"
    log_info "前端访问地址: http://${SERVER_IP}:8080"
    log_info "后端API地址: http://${SERVER_IP}:3000"
    log_info "数据库名称: ${DB_NAME}"
    log_info "数据库用户: ${DB_USER}"
    echo "========================================"
    log_info "服务状态:"
    pm2 status
    echo "========================================"
    log_info "如需查看日志:"
    echo "pm2 logs game-boost-backend"
    echo "pm2 logs game-boost-frontend"
}

# 主函数
main() {
    log_info "开始快速部署王者荣耀代练系统..."
    log_info "MySQL密码: ${MYSQL_ROOT_PASS}"
    log_info "数据库用户密码: ${DB_PASS}"
    echo "========================================"
    
    get_server_ip
    quick_check
    setup_database
    deploy_backend
    deploy_frontend
    start_services
    show_result
    
    log_info "部署完成！请访问 http://${SERVER_IP}:8080 查看系统"
}

# 执行部署
main "$@"
