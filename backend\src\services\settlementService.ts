import { prisma } from '../config/database';
import { SettlementStatus } from '@prisma/client';
import { NotFoundError, ConflictError } from '../middleware/errorHandler';
import { PaginationQuery } from '../types/common';

export interface SettlementQuery extends PaginationQuery {
  status?: string;
  userId?: string;
  keyword?: string;
}

export class SettlementService {
  // 获取结算列表
  async getSettlements(query: SettlementQuery) {
    const { page = 1, limit = 20, status, userId, keyword } = query;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (userId) {
      where.userId = userId;
    }
    
    if (keyword) {
      where.task = {
        taskNo: {
          contains: keyword,
        },
      };
    }

    // 获取总数
    const total = await prisma.settlement.count({ where });

    // 获取数据
    const settlements = await prisma.settlement.findMany({
      where,
      skip,
      take: limit,
      include: {
        task: {
          include: {
            order: {
              select: {
                id: true,
                customerName: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      items: settlements,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  // 获取结算详情
  async getSettlementById(id: string) {
    const settlement = await prisma.settlement.findUnique({
      where: { id },
      include: {
        task: {
          include: {
            order: {
              select: {
                id: true,
                customerName: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
    });

    if (!settlement) {
      throw new NotFoundError('结算记录不存在');
    }

    return settlement;
  }

  // 执行结算
  async settleSettlement(id: string, notes?: string) {
    // 检查结算记录是否存在
    const settlement = await prisma.settlement.findUnique({
      where: { id },
      include: {
        user: true,
      },
    });

    if (!settlement) {
      throw new NotFoundError('结算记录不存在');
    }

    // 检查状态
    if (settlement.status !== SettlementStatus.PENDING) {
      throw new ConflictError('只能结算待处理的记录');
    }

    // 更新结算记录
    const updatedSettlement = await prisma.settlement.update({
      where: { id },
      data: {
        status: SettlementStatus.COMPLETED,
        settledAt: new Date(),
        notes,
      },
      include: {
        task: {
          include: {
            order: {
              select: {
                id: true,
                customerName: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
    });

    // 更新用户总收益
    await prisma.user.update({
      where: { id: settlement.userId },
      data: {
        totalEarnings: {
          increment: settlement.amount,
        },
      },
    });

    return updatedSettlement;
  }

  // 取消结算
  async cancelSettlement(id: string) {
    // 检查结算记录是否存在
    const settlement = await prisma.settlement.findUnique({
      where: { id },
    });

    if (!settlement) {
      throw new NotFoundError('结算记录不存在');
    }

    // 检查状态
    if (settlement.status !== SettlementStatus.PENDING) {
      throw new ConflictError('只能取消待处理的结算记录');
    }

    // 更新结算记录
    const updatedSettlement = await prisma.settlement.update({
      where: { id },
      data: {
        status: SettlementStatus.CANCELLED,
      },
      include: {
        task: {
          include: {
            order: {
              select: {
                id: true,
                customerName: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
    });

    return updatedSettlement;
  }

  // 获取结算统计
  async getSettlementStats(userId?: string) {
    const where = userId ? { userId } : {};

    // 总金额统计
    const totalAmount = await prisma.settlement.aggregate({
      where: { ...where, status: SettlementStatus.COMPLETED },
      _sum: {
        amount: true,
      },
    });

    // 待结算金额统计
    const pendingAmount = await prisma.settlement.aggregate({
      where: { ...where, status: SettlementStatus.PENDING },
      _sum: {
        amount: true,
      },
    });

    // 数量统计
    const totalCount = await prisma.settlement.count({ where });
    const pendingCount = await prisma.settlement.count({ 
      where: { ...where, status: SettlementStatus.PENDING } 
    });
    const completedCount = await prisma.settlement.count({ 
      where: { ...where, status: SettlementStatus.COMPLETED } 
    });

    return {
      totalAmount: totalAmount._sum.amount || 0,
      pendingAmount: pendingAmount._sum.amount || 0,
      completedAmount: (totalAmount._sum.amount || 0) - (pendingAmount._sum.amount || 0),
      totalCount,
      pendingCount,
      completedCount,
    };
  }

  // 批量结算
  async batchSettle(ids: string[], notes?: string) {
    // 检查所有结算记录
    const settlements = await prisma.settlement.findMany({
      where: {
        id: { in: ids },
        status: SettlementStatus.PENDING,
      },
      include: {
        user: true,
      },
    });

    if (settlements.length !== ids.length) {
      throw new ConflictError('部分结算记录不存在或状态不正确');
    }

    // 批量更新结算记录
    await prisma.settlement.updateMany({
      where: { id: { in: ids } },
      data: {
        status: SettlementStatus.COMPLETED,
        settledAt: new Date(),
        notes,
      },
    });

    // 更新用户总收益
    for (const settlement of settlements) {
      await prisma.user.update({
        where: { id: settlement.userId },
        data: {
          totalEarnings: {
            increment: settlement.amount,
          },
        },
      });
    }

    return settlements.length;
  }
}
