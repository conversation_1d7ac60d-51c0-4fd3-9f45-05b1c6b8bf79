<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${username} 的会话历史`"
    width="80%"
    :before-close="handleClose"
  >
    <div class="session-dialog">
      <el-table 
        :data="monitoringStore.userSessions" 
        v-loading="monitoringStore.loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        
        <el-table-column label="地理位置" width="200">
          <template #default="{ row }">
            <div class="location-info">
              <el-icon><Location /></el-icon>
              <span>{{ formatLocation(row) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="设备信息" width="200">
          <template #default="{ row }">
            <div class="device-info">
              <div v-if="row.userAgent" class="user-agent">
                {{ formatUserAgent(row.userAgent) }}
              </div>
              <div v-if="row.deviceInfo" class="device-details">
                <el-tag size="small" v-if="row.deviceInfo.platform">
                  {{ row.deviceInfo.platform }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="loginTime" label="登录时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.loginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="logoutTime" label="登出时间" width="160">
          <template #default="{ row }">
            <span v-if="row.logoutTime">{{ formatDateTime(row.logoutTime) }}</span>
            <el-tag v-else type="success" size="small">在线中</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="会话时长" width="120">
          <template #default="{ row }">
            <span v-if="row.logoutTime">
              {{ formatSessionDuration(row.loginTime, row.logoutTime) }}
            </span>
            <span v-else class="online-duration">
              {{ formatSessionDuration(row.loginTime, new Date().toISOString()) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusTagType(row.status)" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'ACTIVE'"
              type="danger" 
              size="small" 
              @click="forceLogout(row)"
            >
              强制下线
            </el-button>
            <span v-else class="no-action">-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="monitoringStore.userSessions.length === 0 && !monitoringStore.loading" class="empty-state">
        <el-empty description="暂无会话记录" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshSessions" :loading="monitoringStore.loading">
          刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { useMonitoringStore } from '@/stores/monitoring'
import { formatDateTime } from '@/utils/date'
import type { UserSession, SessionStatus } from '@/types'

interface Props {
  modelValue: boolean
  userId: string
  username: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const monitoringStore = useMonitoringStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开，加载会话数据
watch(() => props.modelValue, (visible) => {
  if (visible && props.userId) {
    monitoringStore.fetchUserSessions(props.userId)
  }
})

// 格式化地理位置
const formatLocation = (session: UserSession): string => {
  const parts = []
  if (session.locationCountry && session.locationCountry !== '未知') {
    parts.push(session.locationCountry)
  }
  if (session.locationProvince && session.locationProvince !== '未知') {
    parts.push(session.locationProvince)
  }
  if (session.locationCity && session.locationCity !== '未知') {
    parts.push(session.locationCity)
  }
  if (session.locationIsp && session.locationIsp !== '未知') {
    parts.push(session.locationIsp)
  }
  
  return parts.length > 0 ? parts.join(' ') : '未知位置'
}

// 格式化用户代理
const formatUserAgent = (userAgent: string): string => {
  // 简化用户代理字符串显示
  if (userAgent.includes('Chrome')) {
    return 'Chrome浏览器'
  } else if (userAgent.includes('Firefox')) {
    return 'Firefox浏览器'
  } else if (userAgent.includes('Safari')) {
    return 'Safari浏览器'
  } else if (userAgent.includes('Edge')) {
    return 'Edge浏览器'
  } else if (userAgent.includes('Test-Client')) {
    return '测试客户端'
  } else {
    return '其他客户端'
  }
}

// 格式化会话时长
const formatSessionDuration = (startTime: string, endTime: string): string => {
  const start = new Date(startTime)
  const end = new Date(endTime)
  const duration = Math.floor((end.getTime() - start.getTime()) / 1000)
  
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取状态标签类型
const getStatusTagType = (status: SessionStatus) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'INACTIVE': return 'info'
    case 'FORCED_LOGOUT': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: SessionStatus) => {
  switch (status) {
    case 'ACTIVE': return '活跃'
    case 'INACTIVE': return '已下线'
    case 'FORCED_LOGOUT': return '强制下线'
    default: return '未知'
  }
}

// 强制下线
const forceLogout = async (session: UserSession) => {
  try {
    await ElMessageBox.confirm(
      `确定要强制 ${props.username} 下线吗？`,
      '强制下线确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await monitoringStore.forceUserLogout(session.id, '管理员强制下线')
    
    // 刷新会话列表
    await refreshSessions()
  } catch (error) {
    // 用户取消操作
  }
}

// 刷新会话列表
const refreshSessions = () => {
  if (props.userId) {
    monitoringStore.fetchUserSessions(props.userId)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.session-dialog {
  .location-info {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      color: #909399;
    }
  }
  
  .device-info {
    .user-agent {
      font-size: 12px;
      color: #606266;
      margin-bottom: 4px;
    }
    
    .device-details {
      .el-tag {
        margin-right: 4px;
      }
    }
  }
  
  .online-duration {
    color: #67c23a;
    font-weight: 500;
  }
  
  .no-action {
    color: #c0c4cc;
  }
  
  .empty-state {
    padding: 40px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
