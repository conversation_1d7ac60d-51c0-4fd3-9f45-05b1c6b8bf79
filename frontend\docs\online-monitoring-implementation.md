# 在线用户监控系统实现文档

## 概述

本文档描述了为王者荣耀代练任务分发管理系统新增的在线用户监控功能的前端实现。

## 功能特性

### 1. 在线用户监控页面 (`/boss/monitoring`)
- **实时在线用户列表**：显示当前在线的所有用户
- **用户信息展示**：用户名、角色、IP地址、地理位置、在线时长等
- **筛选功能**：支持按角色、IP地址、地理位置、在线时长筛选
- **强制下线功能**：管理员可以强制指定用户下线
- **用户会话查看**：查看用户的历史会话记录

### 2. 登录日志页面 (`/boss/login-logs`)
- **登录历史记录**：显示所有用户的登录日志
- **详细筛选**：支持按用户名、IP、登录结果、时间范围、地理位置筛选
- **统计信息**：成功/失败/被阻止登录次数统计
- **日志详情**：查看单条登录记录的详细信息
- **分页支持**：支持大量数据的分页显示

### 3. 监控仪表板页面 (`/boss/monitoring-dashboard`)
- **概览卡片**：当前在线用户、总登录次数、成功率等关键指标
- **图表分析**：
  - 在线用户角色分布饼图
  - 登录趋势折线图
  - 地理位置分布饼图
  - 登录时段分析柱状图
- **数据表格**：热门登录地区、失败原因统计
- **时间周期选择**：支持1天、3天、7天、30天数据查看

### 4. 实时通知功能
- **用户上线/下线通知**：管理员和老板可收到用户状态变化通知
- **强制下线通知**：被强制下线的用户会收到通知并自动退出
- **Socket.IO集成**：基于WebSocket的实时通信

## 技术实现

### 前端技术栈
- **Vue 3** + **TypeScript**：现代化的前端框架
- **Element Plus**：UI组件库
- **Pinia**：状态管理
- **Vue Router**：路由管理
- **ECharts**：图表可视化
- **Socket.IO Client**：实时通信
- **Axios**：HTTP客户端
- **SCSS**：样式预处理器

### 核心文件结构

```
frontend/src/
├── api/
│   └── monitoring.ts              # 监控相关API接口
├── stores/
│   └── monitoring.ts              # 监控数据状态管理
├── views/boss/
│   ├── OnlineMonitoring.vue       # 在线用户监控页面
│   ├── LoginLogs.vue              # 登录日志页面
│   └── MonitoringDashboard.vue    # 监控仪表板页面
├── components/
│   ├── UserSessionDialog.vue      # 用户会话详情对话框
│   ├── ForceLogoutDialog.vue      # 强制下线确认对话框
│   └── LogDetailDialog.vue        # 登录日志详情对话框
├── types/
│   └── index.ts                   # 新增监控相关类型定义
└── services/
    └── socketService.ts           # 更新Socket.IO服务
```

### 新增类型定义

```typescript
// 会话状态
export enum SessionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE', 
  FORCED_LOGOUT = 'FORCED_LOGOUT'
}

// 登录结果
export enum LoginResult {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  BLOCKED = 'BLOCKED'
}

// 在线用户信息
export interface OnlineUser {
  id: string
  userId: string
  username: string
  nickname?: string
  role: UserRole
  sessionToken: string
  socketId?: string
  ipAddress: string
  userAgent?: string
  loginTime: string
  lastActivity: string
  onlineDuration: number
  locationCountry?: string
  locationProvince?: string
  locationCity?: string
  locationIsp?: string
  locationFull?: string
}

// 登录日志
export interface LoginLog {
  id: string
  userId?: string
  username: string
  ipAddress: string
  userAgent?: string
  loginTime: string
  logoutTime?: string
  loginResult: LoginResult
  failureReason?: string
  deviceInfo?: any
  sessionDuration?: number
  locationCountry?: string
  locationProvince?: string
  locationCity?: string
  locationIsp?: string
  locationFull?: string
}
```

### API接口

监控API提供以下接口：
- `GET /monitoring/online` - 获取在线用户列表
- `GET /monitoring/online/stats` - 获取在线用户统计
- `GET /monitoring/sessions/:userId` - 获取用户会话历史
- `POST /monitoring/sessions/:sessionId/force-logout` - 强制用户下线
- `GET /monitoring/login-logs` - 获取登录日志
- `GET /monitoring/login-stats` - 获取登录统计
- `POST /monitoring/cleanup-sessions` - 清理过期会话

### 权限控制

所有监控功能仅限于BOSS和ADMIN角色访问：
- 路由级别权限控制
- API接口权限验证
- 组件级别权限判断

### 响应式设计

所有监控页面都支持响应式设计：
- 桌面端：完整功能展示
- 平板端：适配中等屏幕
- 移动端：简化布局，保持核心功能

## 使用说明

### 访问监控功能

1. 使用BOSS或ADMIN账号登录系统
2. 在左侧导航菜单中找到"系统监控"子菜单
3. 选择相应的监控功能：
   - **监控仪表板**：查看总体数据概览
   - **在线监控**：管理当前在线用户
   - **登录日志**：查看历史登录记录

### 强制用户下线

1. 进入"在线监控"页面
2. 在用户列表中找到目标用户
3. 点击"强制下线"按钮
4. 在弹出的对话框中选择下线原因
5. 确认操作，用户将立即被强制下线

### 查看用户会话历史

1. 在"在线监控"页面点击"查看会话"按钮
2. 在弹出的对话框中查看用户的所有会话记录
3. 可以看到每次登录的详细信息，包括IP、地理位置、设备信息等

### 分析登录数据

1. 进入"监控仪表板"页面
2. 查看概览卡片了解关键指标
3. 通过图表分析用户行为趋势
4. 使用时间周期选择器查看不同时间段的数据

## 注意事项

1. **性能考虑**：大量在线用户时，建议使用筛选功能减少数据量
2. **权限管理**：确保只有授权用户能访问监控功能
3. **数据隐私**：登录日志包含敏感信息，需要妥善保护
4. **实时性**：在线用户数据每30秒自动刷新一次
5. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验

## 后续优化建议

1. **数据导出**：支持将监控数据导出为Excel或PDF格式
2. **告警功能**：异常登录行为自动告警
3. **更多图表**：增加更多维度的数据可视化
4. **移动端优化**：进一步优化移动端用户体验
5. **批量操作**：支持批量强制下线等操作
