import { User<PERSON><PERSON>, UserStatus, LoginResult } from '@prisma/client';
import { prisma } from '../config/database';
import { hashPassword, verifyPassword } from '../utils/password';
import { generateAccessToken } from '../utils/jwt';
import { AuthenticationError, ConflictError, NotFoundError } from '../middleware/errorHandler';
import { LoginRequest, RegisterRequest, UpdatePasswordRequest } from '../types/auth';
import { onlineUserService } from './onlineUserService';
import { logger } from '../utils/logger';

export class AuthService {
  // 用户登录
  async login(loginData: LoginRequest, clientInfo?: {
    ipAddress: string;
    userAgent?: string;
    deviceInfo?: any;
  }) {
    const { username, password } = loginData;

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        password: true,
        nickname: true,
        role: true,
        status: true,
      },
    });

    if (!user) {
      throw new AuthenticationError('用户名或密码错误');
    }

    if (user.status !== UserStatus.ACTIVE) {
      throw new AuthenticationError('账号已被禁用');
    }

    // 记录登录尝试
    try {
      if (clientInfo) {
        await onlineUserService.createLoginLog({
          userId: user.id,
          username: user.username,
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
          loginResult: LoginResult.FAILED, // 先假设失败
          failureReason: '密码验证中',
          deviceInfo: clientInfo.deviceInfo,
        });
      }
    } catch (error) {
      logger.error('记录登录尝试失败', error);
      // 继续登录流程，不影响用户体验
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      // 更新登录日志为失败
      if (clientInfo) {
        try {
          await onlineUserService.createLoginLog({
            userId: user.id,
            username: user.username,
            ipAddress: clientInfo.ipAddress,
            userAgent: clientInfo.userAgent,
            loginResult: LoginResult.FAILED,
            failureReason: '密码错误',
            deviceInfo: clientInfo.deviceInfo,
          });
        } catch (error) {
          logger.error('更新登录失败日志失败', error);
        }
      }

      throw new AuthenticationError('用户名或密码错误');
    }

    // 生成访问令牌
    const token = generateAccessToken({
      id: user.id,
      username: user.username,
      role: user.role,
    });

    // 创建用户会话
    if (clientInfo) {
      try {
        await onlineUserService.createSession({
          userId: user.id,
          sessionToken: token,
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
          deviceInfo: clientInfo.deviceInfo,
        });

        // 更新登录日志为成功
        await onlineUserService.createLoginLog({
          userId: user.id,
          username: user.username,
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
          loginResult: LoginResult.SUCCESS,
          deviceInfo: clientInfo.deviceInfo,
        });
      } catch (error) {
        logger.error('创建用户会话失败', error);
        // 继续登录流程，不影响用户体验
      }
    }

    return {
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        role: user.role,
      },
      token,
      expiresIn: '7d',
    };
  }

  // 用户注册
  async register(registerData: RegisterRequest) {
    const { username, password, nickname, phone, role = UserRole.EMPLOYEE } = registerData;

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUser) {
      throw new ConflictError('用户名已存在');
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        password: hashedPassword,
        nickname,
        phone,
        role,
        status: UserStatus.ACTIVE,
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        role: true,
        createdAt: true,
      },
    });

    // 生成访问令牌
    const token = generateAccessToken({
      id: user.id,
      username: user.username,
      role: user.role,
    });

    return {
      user,
      token,
      expiresIn: '7d',
    };
  }

  // 更新密码
  async updatePassword(userId: string, passwordData: UpdatePasswordRequest) {
    const { currentPassword, newPassword } = passwordData;

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        password: true,
      },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('当前密码错误');
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
      },
    });

    return { message: '密码更新成功' };
  }



  // 获取当前用户信息
  async getCurrentUser(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        nickname: true,
        phone: true,
        role: true,
        level: true,
        totalEarnings: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    return user;
  }

  // 用户登出
  async logout(sessionToken: string) {
    try {
      await onlineUserService.endSession(sessionToken);
      return { message: '登出成功' };
    } catch (error) {
      logger.error('用户登出失败', error);
      return { message: '登出成功' }; // 即使失败也返回成功，避免影响用户体验
    }
  }
}
