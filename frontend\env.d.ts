/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Element Plus 全局组件类型声明
declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus')['ElButton']
    ElInput: typeof import('element-plus')['ElInput']
    ElForm: typeof import('element-plus')['ElForm']
    ElFormItem: typeof import('element-plus')['ElFormItem']
    ElMessage: typeof import('element-plus')['ElMessage']
    ElNotification: typeof import('element-plus')['ElNotification']
    ElTable: typeof import('element-plus')['ElTable']
    ElTableColumn: typeof import('element-plus')['ElTableColumn']
    ElPagination: typeof import('element-plus')['ElPagination']
    ElDialog: typeof import('element-plus')['ElDialog']
    ElCard: typeof import('element-plus')['ElCard']
    ElTag: typeof import('element-plus')['ElTag']
    ElSelect: typeof import('element-plus')['ElSelect']
    ElOption: typeof import('element-plus')['ElOption']
    ElDatePicker: typeof import('element-plus')['ElDatePicker']
    ElUpload: typeof import('element-plus')['ElUpload']
    ElProgress: typeof import('element-plus')['ElProgress']
    ElBadge: typeof import('element-plus')['ElBadge']
    ElDropdown: typeof import('element-plus')['ElDropdown']
    ElDropdownMenu: typeof import('element-plus')['ElDropdownMenu']
    ElDropdownItem: typeof import('element-plus')['ElDropdownItem']
    ElMenu: typeof import('element-plus')['ElMenu']
    ElMenuItem: typeof import('element-plus')['ElMenuItem']
    ElSubMenu: typeof import('element-plus')['ElSubMenu']
    ElBreadcrumb: typeof import('element-plus')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus')['ElBreadcrumbItem']
    ElLink: typeof import('element-plus')['ElLink']
    ElAvatar: typeof import('element-plus')['ElAvatar']
    ElTooltip: typeof import('element-plus')['ElTooltip']
    ElPopover: typeof import('element-plus')['ElPopover']
    ElSwitch: typeof import('element-plus')['ElSwitch']
    ElRadio: typeof import('element-plus')['ElRadio']
    ElRadioGroup: typeof import('element-plus')['ElRadioGroup']
    ElCheckbox: typeof import('element-plus')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus')['ElCheckboxGroup']
    ElRow: typeof import('element-plus')['ElRow']
    ElCol: typeof import('element-plus')['ElCol']
    ElContainer: typeof import('element-plus')['ElContainer']
    ElHeader: typeof import('element-plus')['ElHeader']
    ElAside: typeof import('element-plus')['ElAside']
    ElMain: typeof import('element-plus')['ElMain']
    ElFooter: typeof import('element-plus')['ElFooter']
    ElLoading: typeof import('element-plus')['ElLoading']
    ElEmpty: typeof import('element-plus')['ElEmpty']
    ElDivider: typeof import('element-plus')['ElDivider']
    ElSpace: typeof import('element-plus')['ElSpace']
    ElScrollbar: typeof import('element-plus')['ElScrollbar']
  }
}

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_SOCKET_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
