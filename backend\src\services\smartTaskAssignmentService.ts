import { prisma } from '../config/database';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import { EmployeeSkillService } from './employeeSkillService';
import type { EmployeeSkillMatch } from '../types/game';

export interface TaskAssignmentRecommendation {
  employeeId: string;
  employeeName: string;
  matchScore: number;
  skillLevel: string;
  completedTasks: number;
  estimatedCommission: number;
  reasons: string[];
  warnings: string[];
}

export interface SmartAssignmentOptions {
  orderId: string;
  maxRecommendations?: number;
  considerWorkload?: boolean;
  considerSkillLevel?: boolean;
  considerExperience?: boolean;
  minMatchScore?: number;
}

export class SmartTaskAssignmentService {
  private employeeSkillService: EmployeeSkillService;

  constructor() {
    this.employeeSkillService = new EmployeeSkillService();
  }

  // 智能任务分配推荐
  async getTaskAssignmentRecommendations(options: SmartAssignmentOptions): Promise<TaskAssignmentRecommendation[]> {
    const {
      orderId,
      maxRecommendations = 5,
      considerWorkload = true,
      considerSkillLevel = true,
      considerExperience = true,
      minMatchScore = 50
    } = options;

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        gameType: true,
        currentRank: true,
        targetRank: true,
        priority: true,
        deadline: true
      }
    });

    if (!order) {
      throw new NotFoundError('订单不存在');
    }

    // 由于现在使用灵活的游戏类型和段位，暂时简化技能匹配逻辑
    // TODO: 实现基于字符串的技能匹配
    const skillMatches: any[] = [];

    // 获取员工当前工作负载
    const employeeWorkloads = await this.getEmployeeWorkloads();

    // 计算推荐评分
    const recommendations: TaskAssignmentRecommendation[] = await Promise.all(
      skillMatches
        .filter(match => match.matchScore >= minMatchScore)
        .slice(0, maxRecommendations * 2) // 获取更多候选人进行筛选
        .map(async (match) => {
          const workload = employeeWorkloads[match.userId] || { activeTasks: 0, totalHours: 0 };
          
          let finalScore = match.matchScore;
          const reasons: string[] = [];
          const warnings: string[] = [];

          // 技能等级加分
          if (considerSkillLevel) {
            const skillBonus = this.getSkillLevelBonus(match.skillLevel);
            finalScore += skillBonus;
            if (skillBonus > 0) {
              reasons.push(`技能等级${match.skillLevel}加分${skillBonus}分`);
            }
          }

          // 经验加分
          if (considerExperience) {
            const experienceBonus = this.getExperienceBonus(match.completedTasks);
            finalScore += experienceBonus;
            if (experienceBonus > 0) {
              reasons.push(`完成${match.completedTasks}个任务，经验加分${experienceBonus}分`);
            }
          }

          // 工作负载调整
          if (considerWorkload) {
            const workloadPenalty = this.getWorkloadPenalty(workload.activeTasks);
            finalScore -= workloadPenalty;
            
            if (workload.activeTasks > 5) {
              warnings.push(`当前有${workload.activeTasks}个活跃任务，工作负载较重`);
            } else if (workload.activeTasks > 0) {
              reasons.push(`当前有${workload.activeTasks}个活跃任务`);
            }
          }

          // 段位能力检查 - 暂时跳过，因为现在使用字符串段位
          // TODO: 实现基于字符串的段位比较逻辑

          // 估算佣金
          const estimatedCommission = await this.estimateCommission(order, match);

          return {
            employeeId: match.userId,
            employeeName: match.nickname || match.username,
            matchScore: Math.round(finalScore),
            skillLevel: match.skillLevel,
            completedTasks: match.completedTasks,
            estimatedCommission,
            reasons,
            warnings
          };
        })
    );

    // 按最终评分排序并返回指定数量的推荐
    return recommendations
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, maxRecommendations);
  }

  // 自动分配任务
  async autoAssignTask(orderId: string, options?: {
    minMatchScore?: number;
    excludeOverloaded?: boolean;
  }): Promise<{
    success: boolean;
    assignedEmployeeId?: string;
    employeeName?: string;
    matchScore?: number;
    message: string;
  }> {
    const { minMatchScore = 70, excludeOverloaded = true } = options || {};

    try {
      const recommendations = await this.getTaskAssignmentRecommendations({
        orderId,
        maxRecommendations: 1,
        considerWorkload: excludeOverloaded,
        minMatchScore
      });

      if (recommendations.length === 0) {
        return {
          success: false,
          message: '未找到合适的员工进行自动分配'
        };
      }

      const bestMatch = recommendations[0];

      // 检查是否有严重警告
      const hasSeriousWarnings = bestMatch.warnings.some(warning =>
        warning.includes('工作负载较重') || warning.includes('低于目标段位')
      );

      if (hasSeriousWarnings && bestMatch.matchScore < 80) {
        return {
          success: false,
          message: `最佳匹配员工存在问题: ${bestMatch.warnings.join(', ')}`
        };
      }

      // 创建任务并分配
      const taskNo = this.generateTaskNo();
      const task = await prisma.task.create({
        data: {
          taskNo,
          orderId,
          assigneeId: bestMatch.employeeId,
          assignType: 'DIRECT',
          status: 'ACCEPTED',
          commission: bestMatch.estimatedCommission,
          commissionType: 'AUTO',
          description: `系统自动分配 - 匹配度: ${bestMatch.matchScore}分`
        }
      });

      return {
        success: true,
        assignedEmployeeId: bestMatch.employeeId,
        employeeName: bestMatch.employeeName,
        matchScore: bestMatch.matchScore,
        message: `任务已自动分配给${bestMatch.employeeName}，匹配度${bestMatch.matchScore}分`
      };

    } catch (error) {
      console.error('自动分配任务失败:', error);
      return {
        success: false,
        message: '自动分配任务失败，请手动分配'
      };
    }
  }

  // 获取员工工作负载
  private async getEmployeeWorkloads(): Promise<Record<string, { activeTasks: number; totalHours: number }>> {
    const activeTaskStats = await prisma.task.groupBy({
      by: ['assigneeId'],
      where: {
        assigneeId: { not: null },
        status: { in: ['ACCEPTED', 'IN_PROGRESS'] }
      },
      _count: true,
      _sum: {
        estimatedHours: true
      }
    });

    const workloads: Record<string, { activeTasks: number; totalHours: number }> = {};
    
    activeTaskStats.forEach(stat => {
      if (stat.assigneeId) {
        workloads[stat.assigneeId] = {
          activeTasks: stat._count || 0,
          totalHours: stat._sum?.estimatedHours || 0
        };
      }
    });

    return workloads;
  }

  // 技能等级加分
  private getSkillLevelBonus(skillLevel: string): number {
    const bonuses = {
      BEGINNER: 0,
      INTERMEDIATE: 5,
      ADVANCED: 10,
      EXPERT: 15,
      MASTER: 20
    };
    return bonuses[skillLevel as keyof typeof bonuses] || 0;
  }

  // 经验加分
  private getExperienceBonus(completedTasks: number): number {
    if (completedTasks >= 100) return 20;
    if (completedTasks >= 50) return 15;
    if (completedTasks >= 20) return 10;
    if (completedTasks >= 10) return 5;
    return 0;
  }

  // 工作负载惩罚
  private getWorkloadPenalty(activeTasks: number): number {
    if (activeTasks >= 10) return 30;
    if (activeTasks >= 7) return 20;
    if (activeTasks >= 5) return 10;
    if (activeTasks >= 3) return 5;
    return 0;
  }

  // 估算佣金
  private async estimateCommission(order: any, match: EmployeeSkillMatch): Promise<number> {
    try {
      // 简化佣金计算，直接基于订单价格
      // TODO: 实现基于新的灵活字段的佣金计算逻辑

      // 基础佣金比例
      let commissionRate = 0.3; // 30%

      // 根据技能等级调整佣金比例（如果有技能匹配信息）
      const skillRateAdjustment = {
        BEGINNER: -0.05,
        INTERMEDIATE: 0,
        ADVANCED: 0.02,
        EXPERT: 0.05,
        MASTER: 0.08
      };

      commissionRate += skillRateAdjustment[match.skillLevel] || 0;

      // 根据经验调整佣金比例
      if (match.completedTasks >= 50) commissionRate += 0.03;
      else if (match.completedTasks >= 20) commissionRate += 0.02;
      else if (match.completedTasks >= 10) commissionRate += 0.01;

      // 确保佣金比例在合理范围内
      commissionRate = Math.max(0.2, Math.min(0.5, commissionRate));

      return Math.round(order.price * commissionRate);
    } catch (error) {
      console.error('估算佣金失败:', error);
      // 返回默认佣金
      return Math.round(order.price * 0.3);
    }
  }

  // 生成任务编号
  private generateTaskNo(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `T${timestamp.slice(-8)}${random}`;
  }

  // 获取游戏热度统计
  async getGameHotnessStats(): Promise<Array<{
    gameId: string;
    gameName: string;
    orderCount: number;
    revenue: number;
    employeeCount: number;
    avgCompletionTime: number;
    hotnessScore: number;
    trend: 'rising' | 'stable' | 'declining';
  }>> {
    // 获取所有活跃游戏
    const games = await prisma.game.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            orders: true,
            employeeSkills: true
          }
        }
      }
    });

    const hotnessStats = await Promise.all(
      games.map(async (game) => {
        // 获取订单统计
        const orderStats = await prisma.order.aggregate({
          where: { gameId: game.id },
          _sum: { price: true },
          _count: true
        });

        // 获取平均完成时间
        const completedTasks = await prisma.task.findMany({
          where: {
            order: { gameId: game.id },
            status: 'COMPLETED',
            startTime: { not: null },
            endTime: { not: null }
          },
          select: {
            startTime: true,
            endTime: true
          }
        });

        const avgCompletionTime = completedTasks.length > 0
          ? completedTasks.reduce((sum, task) => {
              const duration = task.endTime!.getTime() - task.startTime!.getTime();
              return sum + duration;
            }, 0) / completedTasks.length / (1000 * 60 * 60) // 转换为小时
          : 0;

        // 计算热度评分
        const orderCount = orderStats._count;
        const revenue = orderStats._sum.price || 0;
        const employeeCount = game._count.employeeSkills;
        
        const hotnessScore = Math.round(
          orderCount * 0.4 + (revenue / 100) * 0.3 + employeeCount * 0.3
        );

        // 简单的趋势分析（基于最近30天vs之前30天）
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

        const [recentOrders, previousOrders] = await Promise.all([
          prisma.order.count({
            where: {
              gameId: game.id,
              createdAt: { gte: thirtyDaysAgo }
            }
          }),
          prisma.order.count({
            where: {
              gameId: game.id,
              createdAt: { gte: sixtyDaysAgo, lt: thirtyDaysAgo }
            }
          })
        ]);

        let trend: 'rising' | 'stable' | 'declining' = 'stable';
        if (recentOrders > previousOrders * 1.1) {
          trend = 'rising';
        } else if (recentOrders < previousOrders * 0.9) {
          trend = 'declining';
        }

        return {
          gameId: game.id,
          gameName: game.displayName,
          orderCount,
          revenue,
          employeeCount,
          avgCompletionTime: Math.round(avgCompletionTime),
          hotnessScore,
          trend
        };
      })
    );

    return hotnessStats.sort((a, b) => b.hotnessScore - a.hotnessScore);
  }
}
