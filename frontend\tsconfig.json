{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/views/*": ["./src/views/*"], "@/utils/*": ["./src/utils/*"], "@/api/*": ["./src/api/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"], "@/styles/*": ["./src/styles/*"], "@/router/*": ["./src/router/*"]}, "types": ["vite/client", "node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "env.d.ts", "auto-imports.d.ts", "components.d.ts"], "exclude": ["node_modules", "dist", "src/**/__tests__/*"]}