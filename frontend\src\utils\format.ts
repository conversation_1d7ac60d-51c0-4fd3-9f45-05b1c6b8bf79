/**
 * 数值格式化工具函数
 */

// 格式化货币数值
export function formatCurrency(value: number, options: {
  showSymbol?: boolean
  precision?: number
  compact?: boolean
} = {}): string {
  const { showSymbol = true, precision = 2, compact = false } = options
  
  if (value === 0) {
    return showSymbol ? '¥0' : '0'
  }
  
  if (compact && Math.abs(value) >= 1000) {
    return formatCompactCurrency(value, showSymbol)
  }
  
  const formatted = value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
  
  return showSymbol ? `¥${formatted}` : formatted
}

// 紧凑格式化货币（使用万、亿等单位）
export function formatCompactCurrency(value: number, showSymbol: boolean = true): string {
  const absValue = Math.abs(value)
  const sign = value < 0 ? '-' : ''
  
  let formatted: string
  
  if (absValue >= 1e12) {
    // 万亿
    formatted = `${(absValue / 1e12).toFixed(1)}万亿`
  } else if (absValue >= 1e8) {
    // 亿
    formatted = `${(absValue / 1e8).toFixed(1)}亿`
  } else if (absValue >= 1e4) {
    // 万
    formatted = `${(absValue / 1e4).toFixed(1)}万`
  } else if (absValue >= 1000) {
    // 千
    formatted = `${(absValue / 1000).toFixed(1)}千`
  } else {
    formatted = absValue.toString()
  }
  
  const result = `${sign}${formatted}`
  return showSymbol ? `¥${result}` : result
}

// 格式化数量
export function formatNumber(value: number, compact: boolean = false): string {
  if (value === 0) {
    return '0'
  }
  
  if (compact && Math.abs(value) >= 1000) {
    return formatCompactNumber(value)
  }
  
  return value.toLocaleString('zh-CN')
}

// 紧凑格式化数量
export function formatCompactNumber(value: number): string {
  const absValue = Math.abs(value)
  const sign = value < 0 ? '-' : ''
  
  if (absValue >= 1e8) {
    return `${sign}${(absValue / 1e8).toFixed(1)}亿`
  } else if (absValue >= 1e4) {
    return `${sign}${(absValue / 1e4).toFixed(1)}万`
  } else if (absValue >= 1000) {
    return `${sign}${(absValue / 1000).toFixed(1)}千`
  } else {
    return value.toString()
  }
}

// 格式化百分比
export function formatPercentage(value: number, precision: number = 1): string {
  return `${value.toFixed(precision)}%`
}

// 智能格式化数值（根据数值大小自动选择格式）
export function formatSmartNumber(value: number, type: 'currency' | 'number' = 'number'): string {
  const absValue = Math.abs(value)
  
  if (type === 'currency') {
    if (absValue >= 1e8) {
      return formatCompactCurrency(value, true)
    } else if (absValue >= 1e4) {
      return formatCompactCurrency(value, true)
    } else {
      return formatCurrency(value, { compact: false })
    }
  } else {
    if (absValue >= 1e4) {
      return formatCompactNumber(value)
    } else {
      return formatNumber(value, false)
    }
  }
}

// 图表Y轴格式化器
export function createYAxisFormatter(type: 'currency' | 'number' = 'number') {
  return (value: number) => {
    if (type === 'currency') {
      return formatCompactCurrency(value, true)
    } else {
      return formatCompactNumber(value)
    }
  }
}

// 图表提示框格式化器
export function createTooltipFormatter(type: 'currency' | 'number' = 'number') {
  return (params: any) => {
    const data = params[0] || params
    const value = data.value || data.data?.value || 0
    
    let formattedValue: string
    if (type === 'currency') {
      formattedValue = `收益: ${formatCurrency(value, { compact: false })}`
    } else {
      formattedValue = `数量: ${formatNumber(value, false)}`
    }
    
    return `${data.axisValue || data.name}<br/>${formattedValue}`
  }
}

// 检查数值是否为有效数字
export function isValidNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && isFinite(value)
}

// 安全的数值转换
export function safeNumber(value: any, defaultValue: number = 0): number {
  if (isValidNumber(value)) {
    return value
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isValidNumber(parsed) ? parsed : defaultValue
  }
  
  return defaultValue
}

// 数值范围检查
export function clampNumber(value: number, min: number = -Infinity, max: number = Infinity): number {
  return Math.max(min, Math.min(max, value))
}

// 格式化时间段文本
export function formatPeriodText(period: string): string {
  switch (period) {
    case '7d':
      return '近7天'
    case '30d':
      return '近30天'
    case '90d':
      return '近90天'
    default:
      return '近30天'
  }
}

// 格式化日期显示
export function formatDateForChart(dateStr: string): string {
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      return dateStr
    }
    return `${date.getMonth() + 1}/${date.getDate()}`
  } catch {
    return dateStr
  }
}

// 数据验证和清理
export function validateChartData(data: any[]): any[] {
  if (!Array.isArray(data)) {
    console.warn('图表数据不是数组:', data)
    return []
  }
  
  return data.filter(item => {
    if (!item || typeof item !== 'object') {
      console.warn('无效的图表数据项:', item)
      return false
    }
    
    if (!item.date) {
      console.warn('图表数据项缺少日期:', item)
      return false
    }
    
    if (!isValidNumber(item.value)) {
      console.warn('图表数据项数值无效:', item)
      return false
    }
    
    return true
  })
}
