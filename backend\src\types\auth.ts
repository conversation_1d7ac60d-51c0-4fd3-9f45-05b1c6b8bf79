import { UserRole } from '@prisma/client';

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
}

// 注册请求
export interface RegisterRequest {
  username: string;
  password: string;
  nickname?: string;
  phone?: string;
  role?: UserRole;
}

// 登录响应
export interface LoginResponse {
  success: true;
  data: {
    user: {
      id: string;
      username: string;
      nickname?: string;
      role: UserRole;
    };
    token: string;
    expiresIn: string;
  };
  message: string;
}

// 令牌刷新请求
export interface RefreshTokenRequest {
  refreshToken: string;
}



// 密码更新请求
export interface UpdatePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// JWT载荷
export interface JwtPayload {
  userId: string;
  username: string;
  role: UserRole;
  iat: number;
  exp: number;
}
