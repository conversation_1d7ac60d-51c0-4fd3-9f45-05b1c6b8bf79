import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { ipLocationService, LocationInfo } from './ipLocationService';
import { UserRole, SessionStatus, LoginResult } from '@prisma/client';

// 在线用户信息接口
export interface OnlineUser {
  id: string;
  userId: string;
  username: string;
  nickname?: string;
  role: UserRole;
  sessionToken: string;
  socketId?: string;
  ipAddress: string;
  userAgent?: string;
  loginTime: Date;
  lastActivity: Date;
  onlineDuration: number; // 在线时长（秒）
  locationCountry?: string;
  locationProvince?: string;
  locationCity?: string;
  locationIsp?: string;
  locationFull?: string;
}

// 用户会话信息接口
export interface UserSession {
  id: string;
  userId: string;
  sessionToken: string;
  socketId?: string;
  ipAddress: string;
  userAgent?: string;
  loginTime: Date;
  lastActivity: Date;
  logoutTime?: Date;
  status: SessionStatus;
  deviceInfo?: any;
  locationCountry?: string;
  locationProvince?: string;
  locationCity?: string;
  locationIsp?: string;
  locationFull?: string;
}

// 登录日志接口
export interface LoginLog {
  id: string;
  userId?: string;
  username: string;
  ipAddress: string;
  userAgent?: string;
  loginTime: Date;
  logoutTime?: Date;
  loginResult: LoginResult;
  failureReason?: string;
  deviceInfo?: any;
  sessionDuration?: number;
  locationCountry?: string;
  locationProvince?: string;
  locationCity?: string;
  locationIsp?: string;
  locationFull?: string;
}

// 筛选条件接口
export interface OnlineUserFilters {
  role?: UserRole;
  ipAddress?: string;
  locationProvince?: string;
  locationCity?: string;
  minOnlineTime?: number; // 最小在线时长（分钟）
}

export interface LoginLogFilters {
  userId?: string;
  username?: string;
  ipAddress?: string;
  loginResult?: LoginResult;
  startDate?: Date;
  endDate?: Date;
  locationProvince?: string;
  locationCity?: string;
}

export class OnlineUserService {
  // 创建用户会话
  async createSession(data: {
    userId: string;
    sessionToken: string;
    ipAddress: string;
    userAgent?: string;
    socketId?: string;
    deviceInfo?: any;
  }): Promise<UserSession> {
    try {
      // 获取IP地址地理位置信息
      const locationInfo = await ipLocationService.getLocation(data.ipAddress);
      
      // 创建会话记录
      const session = await prisma.userSession.create({
        data: {
          userId: data.userId,
          sessionToken: data.sessionToken,
          socketId: data.socketId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          deviceInfo: data.deviceInfo ? JSON.stringify(data.deviceInfo) : null,
          locationCountry: locationInfo.country,
          locationRegion: locationInfo.region,
          locationProvince: locationInfo.province,
          locationCity: locationInfo.city,
          locationIsp: locationInfo.isp,
          locationFull: locationInfo.fullLocation,
          status: SessionStatus.ACTIVE,
        },
      });

      logger.info('用户会话创建成功', {
        userId: data.userId,
        sessionId: session.id,
        ipAddress: data.ipAddress,
        location: `${locationInfo.province} ${locationInfo.city}`,
      });

      return this.formatSession(session);
    } catch (error) {
      logger.error('创建用户会话失败:', error);
      throw new Error('创建用户会话失败');
    }
  }

  // 更新用户活动时间
  async updateUserActivity(sessionToken: string, socketId?: string): Promise<void> {
    try {
      await prisma.userSession.updateMany({
        where: {
          sessionToken,
          status: SessionStatus.ACTIVE,
        },
        data: {
          lastActivity: new Date(),
          ...(socketId && { socketId }),
        },
      });
    } catch (error) {
      logger.error('更新用户活动时间失败:', error);
    }
  }

  // 获取在线用户列表
  async getOnlineUsers(filters?: OnlineUserFilters): Promise<OnlineUser[]> {
    try {
      const where: any = {
        status: SessionStatus.ACTIVE,
        lastActivity: {
          gte: new Date(Date.now() - 30 * 60 * 1000), // 30分钟内有活动
        },
      };

      // 应用筛选条件
      if (filters?.role) {
        where.user = { role: filters.role };
      }
      if (filters?.ipAddress) {
        where.ipAddress = { contains: filters.ipAddress };
      }
      if (filters?.locationProvince) {
        where.locationProvince = { contains: filters.locationProvince };
      }
      if (filters?.locationCity) {
        where.locationCity = { contains: filters.locationCity };
      }

      const sessions = await prisma.userSession.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              role: true,
            },
          },
        },
        orderBy: {
          lastActivity: 'desc',
        },
      });

      const onlineUsers = sessions.map(session => {
        const onlineDuration = Math.floor(
          (new Date().getTime() - session.loginTime.getTime()) / 1000
        );

        // 应用在线时长筛选
        if (filters?.minOnlineTime && onlineDuration < filters.minOnlineTime * 60) {
          return null;
        }

        return {
          id: session.id,
          userId: session.userId,
          username: session.user.username,
          nickname: session.user.nickname,
          role: session.user.role,
          sessionToken: session.sessionToken,
          socketId: session.socketId,
          ipAddress: session.ipAddress,
          userAgent: session.userAgent,
          loginTime: session.loginTime,
          lastActivity: session.lastActivity,
          onlineDuration,
          locationCountry: session.locationCountry,
          locationProvince: session.locationProvince,
          locationCity: session.locationCity,
          locationIsp: session.locationIsp,
          locationFull: session.locationFull,
        } as OnlineUser;
      }).filter(Boolean) as OnlineUser[];

      return onlineUsers;
    } catch (error) {
      logger.error('获取在线用户列表失败:', error);
      throw new Error('获取在线用户列表失败');
    }
  }

  // 获取用户会话详情
  async getUserSessions(userId: string): Promise<UserSession[]> {
    try {
      const sessions = await prisma.userSession.findMany({
        where: { userId },
        orderBy: { loginTime: 'desc' },
        take: 10, // 最近10次会话
      });

      return sessions.map(session => this.formatSession(session));
    } catch (error) {
      logger.error('获取用户会话详情失败:', error);
      throw new Error('获取用户会话详情失败');
    }
  }

  // 根据会话ID获取会话详情
  async getSessionById(sessionId: string) {
    try {
      const session = await prisma.userSession.findUnique({
        where: { id: sessionId },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              role: true,
            }
          }
        },
      });

      return session;
    } catch (error) {
      logger.error('获取会话详情失败:', error);
      throw new Error('获取会话详情失败');
    }
  }

  // 强制用户下线
  async forceLogout(sessionId: string, reason?: string): Promise<void> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { id: sessionId },
        include: { user: true },
      });

      if (!session) {
        throw new Error('会话不存在');
      }

      // 更新会话状态
      await prisma.userSession.update({
        where: { id: sessionId },
        data: {
          status: SessionStatus.FORCED_LOGOUT,
          logoutTime: new Date(),
        },
      });

      logger.info('强制用户下线成功', {
        sessionId,
        userId: session.userId,
        username: session.user.username,
        reason: reason || '管理员操作',
      });
    } catch (error) {
      logger.error('强制用户下线失败:', error);
      throw new Error('强制用户下线失败');
    }
  }

  // 检查用户是否在线
  async isUserOnline(userId: string): Promise<boolean> {
    try {
      const activeSession = await prisma.userSession.findFirst({
        where: {
          userId,
          status: SessionStatus.ACTIVE,
          lastActivity: {
            gte: new Date(Date.now() - 30 * 60 * 1000), // 30分钟内有活动
          },
        },
      });

      return !!activeSession;
    } catch (error) {
      logger.error('检查用户在线状态失败:', error);
      return false;
    }
  }

  // 记录登录日志
  async createLoginLog(data: {
    userId?: string;
    username: string;
    ipAddress: string;
    userAgent?: string;
    loginResult: LoginResult;
    failureReason?: string;
    deviceInfo?: any;
  }): Promise<LoginLog> {
    try {
      // 获取IP地址地理位置信息
      const locationInfo = await ipLocationService.getLocation(data.ipAddress);
      
      const loginLog = await prisma.loginLog.create({
        data: {
          userId: data.userId,
          username: data.username,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          loginResult: data.loginResult,
          failureReason: data.failureReason,
          deviceInfo: data.deviceInfo ? JSON.stringify(data.deviceInfo) : null,
          locationCountry: locationInfo.country,
          locationRegion: locationInfo.region,
          locationProvince: locationInfo.province,
          locationCity: locationInfo.city,
          locationIsp: locationInfo.isp,
          locationFull: locationInfo.fullLocation,
        },
      });

      return this.formatLoginLog(loginLog);
    } catch (error) {
      logger.error('创建登录日志失败:', error);
      throw new Error('创建登录日志失败');
    }
  }

  // 获取登录日志
  async getLoginLogs(filters?: LoginLogFilters): Promise<LoginLog[]> {
    try {
      const where: any = {};

      // 应用筛选条件
      if (filters?.userId) {
        where.userId = filters.userId;
      }
      if (filters?.username) {
        where.username = { contains: filters.username };
      }
      if (filters?.ipAddress) {
        where.ipAddress = { contains: filters.ipAddress };
      }
      if (filters?.loginResult) {
        where.loginResult = filters.loginResult;
      }
      if (filters?.startDate || filters?.endDate) {
        where.loginTime = {};
        if (filters.startDate) {
          where.loginTime.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.loginTime.lte = filters.endDate;
        }
      }
      if (filters?.locationProvince) {
        where.locationProvince = { contains: filters.locationProvince };
      }
      if (filters?.locationCity) {
        where.locationCity = { contains: filters.locationCity };
      }

      const logs = await prisma.loginLog.findMany({
        where,
        orderBy: { loginTime: 'desc' },
        take: 100, // 限制返回数量
      });

      return logs.map(log => this.formatLoginLog(log));
    } catch (error) {
      logger.error('获取登录日志失败:', error);
      throw new Error('获取登录日志失败');
    }
  }

  // 用户登出时更新会话
  async endSession(sessionToken: string): Promise<void> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { sessionToken },
      });

      if (session && session.status === SessionStatus.ACTIVE) {
        const sessionDuration = Math.floor(
          (new Date().getTime() - session.loginTime.getTime()) / 1000
        );

        await prisma.userSession.update({
          where: { sessionToken },
          data: {
            status: SessionStatus.INACTIVE,
            logoutTime: new Date(),
          },
        });

        // 更新登录日志的登出时间和会话时长
        await prisma.loginLog.updateMany({
          where: {
            userId: session.userId,
            loginTime: session.loginTime,
            logoutTime: null,
          },
          data: {
            logoutTime: new Date(),
            sessionDuration,
          },
        });

        logger.info('用户会话结束', {
          sessionToken,
          userId: session.userId,
          sessionDuration,
        });
      }
    } catch (error) {
      logger.error('结束用户会话失败:', error);
    }
  }

  // 清理过期会话
  async cleanupExpiredSessions(): Promise<void> {
    try {
      const expiredTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24小时前

      await prisma.userSession.updateMany({
        where: {
          status: SessionStatus.ACTIVE,
          lastActivity: {
            lt: expiredTime,
          },
        },
        data: {
          status: SessionStatus.INACTIVE,
          logoutTime: new Date(),
        },
      });

      logger.info('清理过期会话完成');
    } catch (error) {
      logger.error('清理过期会话失败:', error);
    }
  }

  // 格式化会话数据
  private formatSession(session: any): UserSession {
    return {
      id: session.id,
      userId: session.userId,
      sessionToken: session.sessionToken,
      socketId: session.socketId,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      loginTime: session.loginTime,
      lastActivity: session.lastActivity,
      logoutTime: session.logoutTime,
      status: session.status,
      deviceInfo: session.deviceInfo ? JSON.parse(session.deviceInfo) : null,
      locationCountry: session.locationCountry,
      locationProvince: session.locationProvince,
      locationCity: session.locationCity,
      locationIsp: session.locationIsp,
      locationFull: session.locationFull,
    };
  }

  // 格式化登录日志数据
  private formatLoginLog(log: any): LoginLog {
    return {
      id: log.id,
      userId: log.userId,
      username: log.username,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      loginTime: log.loginTime,
      logoutTime: log.logoutTime,
      loginResult: log.loginResult,
      failureReason: log.failureReason,
      deviceInfo: log.deviceInfo ? JSON.parse(log.deviceInfo) : null,
      sessionDuration: log.sessionDuration,
      locationCountry: log.locationCountry,
      locationProvince: log.locationProvince,
      locationCity: log.locationCity,
      locationIsp: log.locationIsp,
      locationFull: log.locationFull,
    };
  }
}

// 创建单例实例
export const onlineUserService = new OnlineUserService();
