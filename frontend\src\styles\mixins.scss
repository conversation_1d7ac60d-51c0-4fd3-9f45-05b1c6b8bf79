@use "sass:map";
@use "variables.scss" as *;

// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 响应式断点混入（最大宽度）
@mixin respond-below($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @if $value > 0 {
      @media (max-width: $value - 1px) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中对齐
@mixin center($horizontal: true, $vertical: true) {
  position: absolute;
  
  @if $horizontal and $vertical {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// Flex布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 按钮样式
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover,
  &:focus {
    color: $color;
    background-color: darken($background, 7.5%);
    border-color: darken($border, 10%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12.5%);
  }
  
  &.is-disabled,
  &:disabled {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card($padding: var(--spacing-md), $radius: var(--border-radius-base)) {
  background: var(--bg-color);
  border: 1px solid var(--border-light);
  border-radius: $radius;
  box-shadow: var(--shadow-base);
  padding: $padding;
}

// 输入框样式
@mixin input-variant($border-color: var(--border-base)) {
  border: 1px solid $border-color;
  border-radius: var(--border-radius-base);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-color);
  transition: var(--transition-base);
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
  
  &:disabled {
    background-color: var(--bg-page);
    color: var(--text-placeholder);
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: var(--text-placeholder);
  }
}

// 滚动条样式
@mixin scrollbar($size: 6px, $thumb-color: var(--border-base), $track-color: transparent) {
  &::-webkit-scrollbar {
    width: $size;
    height: $size;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $size / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 动画
@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-in-down($duration: 0.3s) {
  animation: slideInDown $duration ease-out;
}

@mixin slide-in-up($duration: 0.3s) {
  animation: slideInUp $duration ease-out;
}

// 关键帧动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 状态颜色
@mixin status-color($status) {
  @if $status == 'success' {
    color: var(--success-color);
  } @else if $status == 'warning' {
    color: var(--warning-color);
  } @else if $status == 'danger' {
    color: var(--danger-color);
  } @else if $status == 'info' {
    color: var(--info-color);
  } @else {
    color: var(--text-primary);
  }
}

// 状态背景色
@mixin status-background($status) {
  @if $status == 'success' {
    background-color: rgba(103, 194, 58, 0.1);
    color: var(--success-color);
  } @else if $status == 'warning' {
    background-color: rgba(230, 162, 60, 0.1);
    color: var(--warning-color);
  } @else if $status == 'danger' {
    background-color: rgba(245, 108, 108, 0.1);
    color: var(--danger-color);
  } @else if $status == 'info' {
    background-color: rgba(144, 147, 153, 0.1);
    color: var(--info-color);
  } @else {
    background-color: var(--bg-page);
    color: var(--text-primary);
  }
}
