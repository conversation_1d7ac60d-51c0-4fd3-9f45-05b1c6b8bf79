import { request } from './http'
import type { ApiResponse, PaginatedResponse, PaginationQuery } from '@/types'

// 通知接口类型定义
export interface Notification {
  id: string
  userId: string
  title: string
  content: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  isRead: boolean
  createdAt: string
  updatedAt: string
  data?: any
}

export interface NotificationQuery extends PaginationQuery {
  type?: string
  isRead?: boolean
  startDate?: string
  endDate?: string
}

export interface CreateNotificationRequest {
  userId: string
  title: string
  content: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  data?: any
}

// 通知API
export const notificationApi = {
  // 获取当前用户的通知列表
  getNotifications(query: NotificationQuery = {}): Promise<ApiResponse<PaginatedResponse<Notification>>> {
    // 过滤掉空字符串参数
    const filteredQuery = Object.fromEntries(
      Object.entries(query).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )
    return request.get('/notifications', { params: filteredQuery })
  },

  // 获取未读通知数量
  getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    return request.get('/notifications/unread-count')
  },

  // 获取通知详情
  getNotificationById(id: string): Promise<ApiResponse<Notification>> {
    return request.get(`/notifications/${id}`)
  },

  // 标记通知为已读
  markAsRead(id: string): Promise<ApiResponse<Notification>> {
    return request.put(`/notifications/${id}/read`)
  },

  // 标记所有通知为已读
  markAllAsRead(): Promise<ApiResponse<{ count: number }>> {
    return request.post('/notifications/mark-all-read')
  },

  // 删除通知
  deleteNotification(id: string): Promise<ApiResponse<{ message: string }>> {
    return request.delete(`/notifications/${id}`)
  },

  // 创建通知（管理员功能）
  createNotification(data: CreateNotificationRequest): Promise<ApiResponse<Notification>> {
    return request.post('/notifications', data)
  },

  // 清理过期通知（管理员功能）
  cleanupOldNotifications(): Promise<ApiResponse<{ count: number }>> {
    return request.post('/notifications/cleanup')
  }
}
