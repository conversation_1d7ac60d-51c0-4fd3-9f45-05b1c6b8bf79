### 模块一：【后台】游戏与表单配置模块

这是整个系统的基石和大脑。老板在这里定义“生产什么”和“如何生产”。
1. 功能界面设计
你需要一个后台管理界面，至少包含两个层级：
层级一：游戏列表 (Games List)
一个表格，展示所有已添加的游戏。
表格列: 游戏图标, 游戏名称, 状态(上架/下架), 订单数量, 操作(编辑/配置表单/切换状态)。
页面按钮: “添加新游戏”。
层级二：表单字段配置 (Form Field Configurator)
点击“配置表单”后，进入该游戏的专属配置页。
页面上会有一个列表，展示为这个游戏配置的所有字段。
字段列表的列: 排序(可拖拽调整), 字段标签(Label), 字段键名(Key), 字段类型(Type), 是否必填, 操作(编辑/删除)。
页面按钮: “添加新字段”。
2. "添加/编辑字段" 弹窗/页面
这是配置的核心。当老板点击“添加新字段”或“编辑”时，会弹出一个表单，让他填写以下信息：
字段标签 (Field Label):
用途: 显示在前端给用户看的名字。
示例: 游戏大区, 当前段位。
技术: VARCHAR 类型，允许中文。
字段键名 (Field Key):
用途: 程序内部使用的唯一标识，用于JSON存储和数据读取。极其重要，一旦使用，不建议修改。
示例: server_region, current_rank。
技术: VARCHAR 类型，建议只用英文字母、数字和下划线，并做唯一性校验。
字段类型 (Field Type):
用途: 决定了在创建订单时，前端渲染成哪种输入控件。
实现: 一个下拉选择框，选项如下：
text: 单行文本框 (用于账号、昵称)
textarea: 多行文本框 (用于备注、详细要求)
select: 下拉选择框 (用于单选的场景，如区服、段位)
checkbox: 多选框 (用于多选的场景，如代练项目)
number: 数字输入框 (用于数量、小时数)
password: 密码框 (输入时显示为星号)
image: 图片上传控件 (用于上传截图凭证)
选项 (Options):
用途: 当字段类型是 select 或 checkbox 时，此项才显示并必填。
实现: 一个文本框，让老板输入选项，每行一个。例如：
Generated code
QQ安卓
微信安卓
QQ-iOS
微信-iOS
Use code with caution.
技术: 后端接收到后，将这些文本按行分割，存为 JSON 数组格式，如 ["QQ安卓", "微信安卓", ...]。
是否必填 (Is Required):
用途: 控制该字段在创建订单时是否必须填写。
实现: 一个开关或勾选框 (Boolean true/false)。
占位提示 (Placeholder):
用途: 输入框内的灰色提示文字。
示例: 请输入您的游戏UID。
技术: VARCHAR 类型，可选填。
排序 (Display Order):
用途: 控制字段在表单中的显示顺序。
实现: 一个数字输入框。前端在渲染时按这个数字从小到大排序。更好的体验是直接在字段列表页通过拖拽来排序。