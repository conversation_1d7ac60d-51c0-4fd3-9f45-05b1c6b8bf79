import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { statisticsApi } from '@/api/statistics'
import type { OverviewStats, RevenueAnalysis, EmployeePerformance, OrderTrends } from '@/api/statistics'

export const useStatisticsStore = defineStore('statistics', () => {
  // 状态
  const loading = ref(false)
  const overviewStats = ref<OverviewStats | null>(null)
  const revenueAnalysis = ref<RevenueAnalysis | null>(null)
  const employeePerformance = ref<EmployeePerformance[]>([])
  const orderTrends = ref<OrderTrends | null>(null)

  // 获取综合统计数据
  const fetchOverviewStats = async () => {
    try {
      loading.value = true
      const response = await statisticsApi.getOverviewStats()
      
      if (response.success && response.data) {
        overviewStats.value = response.data
      }
    } catch (error: any) {
      console.error('获取综合统计失败:', error)
      ElMessage.error(error.message || '获取综合统计失败')
    } finally {
      loading.value = false
    }
  }

  // 获取收益分析数据
  const fetchRevenueAnalysis = async (period: string = '30d') => {
    try {
      const response = await statisticsApi.getRevenueAnalysis(period)
      
      if (response.success && response.data) {
        revenueAnalysis.value = response.data
      }
    } catch (error: any) {
      console.error('获取收益分析失败:', error)
      ElMessage.error(error.message || '获取收益分析失败')
    }
  }

  // 获取员工绩效数据
  const fetchEmployeePerformance = async () => {
    try {
      const response = await statisticsApi.getEmployeePerformance()
      
      if (response.success && response.data) {
        employeePerformance.value = response.data
      }
    } catch (error: any) {
      console.error('获取员工绩效失败:', error)
      ElMessage.error(error.message || '获取员工绩效失败')
    }
  }

  // 获取订单趋势数据
  const fetchOrderTrends = async (period: string = '30d') => {
    try {
      const response = await statisticsApi.getOrderTrends(period)
      
      if (response.success && response.data) {
        orderTrends.value = response.data
      }
    } catch (error: any) {
      console.error('获取订单趋势失败:', error)
      ElMessage.error(error.message || '获取订单趋势失败')
    }
  }

  // 刷新所有统计数据
  const refreshAllStats = async (period: string = '30d') => {
    await Promise.all([
      fetchOverviewStats(),
      fetchRevenueAnalysis(period),
      fetchEmployeePerformance(),
      fetchOrderTrends(period)
    ])
  }

  // 清空统计数据
  const clearStats = () => {
    overviewStats.value = null
    revenueAnalysis.value = null
    employeePerformance.value = []
    orderTrends.value = null
  }

  return {
    // 状态
    loading,
    overviewStats,
    revenueAnalysis,
    employeePerformance,
    orderTrends,
    
    // 方法
    fetchOverviewStats,
    fetchRevenueAnalysis,
    fetchEmployeePerformance,
    fetchOrderTrends,
    refreshAllStats,
    clearStats
  }
})
