import { Request, Response } from 'express';
import { OrderService, CreateOrderData, UpdateOrderData } from '../services/orderService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { getSocketService } from '../services/socketService';

const orderService = new OrderService();

// 创建订单
export const createOrder = asyncHandler(async (req: Request, res: Response) => {
  const createdById = req.user!.id;
  const orderData: CreateOrderData = req.body;

  // 调试日志
  console.log('接收到的订单数据:', JSON.stringify(orderData, null, 2));

  const order = await orderService.createOrder(createdById, orderData);
  
  const response: ApiResponse = {
    success: true,
    data: order,
    message: '订单创建成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// 获取订单列表
export const getOrders = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  // 如果是员工，只能查看自己相关的订单
  if (req.user!.role === 'EMPLOYEE') {
    // 员工可以查看所有待分配的订单和自己接受的任务对应的订单
    // 这里暂时允许查看所有订单，实际应用中可能需要更细粒度的控制
  }
  
  const result = await orderService.getOrders(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取订单列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取订单详情
export const getOrderById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const order = await orderService.getOrderById(id);
  
  const response: ApiResponse = {
    success: true,
    data: order,
    message: '获取订单详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新订单
export const updateOrder = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const userRole = req.user!.role;
  const updateData: UpdateOrderData = req.body;

  const order = await orderService.updateOrder(id, updateData, userId, userRole);

  // 发送Socket通知（如果状态发生变化）
  if (updateData.status) {
    try {
      const socketService = getSocketService();
      socketService.notifyOrderStatusUpdate(order);
    } catch (error) {
      console.error('发送Socket通知失败:', error);
    }
  }

  const response: ApiResponse = {
    success: true,
    data: order,
    message: '订单更新成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 删除订单
export const deleteOrder = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const userRole = req.user!.role;

  const result = await orderService.deleteOrder(id, userId, userRole);

  const response: ApiResponse = {
    success: true,
    data: result,
    message: result.message || '订单删除成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 获取订单统计信息
export const getOrderStats = asyncHandler(async (req: Request, res: Response) => {
  let createdById: string | undefined;
  
  // 如果是老板，可以查看自己创建的订单统计
  if (req.user!.role === 'BOSS') {
    createdById = req.user!.id;
  }
  // 管理员可以查看所有订单统计
  
  const stats = await orderService.getOrderStats(createdById);
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取订单统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取我的订单
export const getMyOrders = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  const userId = req.user!.id;
  
  // 只查看自己创建的订单
  query.createdById = userId;
  
  const result = await orderService.getOrders(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取我的订单成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});
