<template>
  <div class="monitoring-dashboard">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><DataAnalysis /></el-icon>
          监控仪表板
        </h1>
        <p class="page-description">系统监控数据总览和趋势分析</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="refreshAllData"
          :loading="loading"
        >
          刷新数据
        </el-button>
        <el-select v-model="selectedPeriod" @change="handlePeriodChange" style="width: 120px">
          <el-option label="今天" value="1" />
          <el-option label="最近3天" value="3" />
          <el-option label="最近7天" value="7" />
          <el-option label="最近30天" value="30" />
        </el-select>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card online">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ onlineUserStats?.total || 0 }}</div>
                <div class="card-label">当前在线</div>
                <div class="card-trend">
                  <span class="trend-text">实时数据</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card login">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Key /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ loginStats?.total || 0 }}</div>
                <div class="card-label">总登录次数</div>
                <div class="card-trend">
                  <span class="trend-text">{{ selectedPeriod }}天内</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card success">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ loginStats?.successful || 0 }}</div>
                <div class="card-label">成功登录</div>
                <div class="card-trend">
                  <span class="trend-text">
                    成功率 {{ successRate }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card users">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ loginStats?.uniqueUsers || 0 }}</div>
                <div class="card-label">活跃用户</div>
                <div class="card-trend">
                  <span class="trend-text">独立用户数</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 在线用户分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">在线用户角色分布</span>
                <el-button type="text" @click="refreshOnlineStats" class="refresh-button">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div></template>
            <div class="chart-container">
              <div ref="roleChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 登录趋势 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">登录趋势分析</span>
                <el-button type="text" @click="refreshLoginStats" class="refresh-button">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="trendChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <!-- 地理位置分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">地理位置分布</span>
                <el-button type="text" @click="refreshOnlineStats" class="refresh-button">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="locationChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 登录时段分析 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">登录时段分析</span>
                <el-button type="text" @click="refreshLoginStats" class="refresh-button">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="hourChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div class="stats-tables">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="table-card">
            <template #header>
              <span class="card-title">热门登录地区</span>
            </template>
            <el-table :data="topLocations" stripe style="width: 100%">
              <el-table-column prop="location" label="地区" />
              <el-table-column prop="count" label="登录次数" width="100" />
              <el-table-column label="占比" width="80">
                <template #default="{ row }">
                  {{ ((row.count / (loginStats?.total || 1)) * 100).toFixed(1) }}%
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="table-card">
            <template #header>
              <span class="card-title">失败原因统计</span>
            </template>
            <el-table :data="failureReasons" stripe style="width: 100%">
              <el-table-column prop="reason" label="失败原因" />
              <el-table-column prop="count" label="次数" width="80" />
              <el-table-column label="占比" width="80">
                <template #default="{ row }">
                  {{ ((row.count / (loginStats?.failed || 1)) * 100).toFixed(1) }}%
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { 
  DataAnalysis, 
  Refresh, 
  User, 
  Key, 
  Check, 
  UserFilled 
} from '@element-plus/icons-vue'
import { useMonitoringStore } from '@/stores/monitoring'
import * as echarts from 'echarts'
import type { OnlineUserStats, LoginStats } from '@/types'

const monitoringStore = useMonitoringStore()

// 响应式数据
const loading = ref(false)
const selectedPeriod = ref('7')
const onlineUserStats = ref<OnlineUserStats | null>(null)
const loginStats = ref<LoginStats | null>(null)

// 图表引用
const roleChartRef = ref<HTMLDivElement>()
const trendChartRef = ref<HTMLDivElement>()
const locationChartRef = ref<HTMLDivElement>()
const hourChartRef = ref<HTMLDivElement>()

// 图表实例
let roleChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null
let locationChart: echarts.ECharts | null = null
let hourChart: echarts.ECharts | null = null

// 计算属性
const successRate = computed(() => {
  if (!loginStats.value || loginStats.value.total === 0) return 0
  return ((loginStats.value.successful / loginStats.value.total) * 100).toFixed(1)
})

const topLocations = computed(() => {
  if (!loginStats.value?.topLocations) return []
  return Object.entries(loginStats.value.topLocations)
    .map(([location, count]) => ({ location, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
})

const failureReasons = computed(() => {
  if (!loginStats.value?.failureReasons) return []
  return Object.entries(loginStats.value.failureReasons)
    .map(([reason, count]) => ({ reason, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
})

// 刷新所有数据
const refreshAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      refreshOnlineStats(),
      refreshLoginStats()
    ])
  } finally {
    loading.value = false
  }
}

// 刷新在线用户统计
const refreshOnlineStats = async () => {
  await monitoringStore.fetchOnlineUserStats()
  onlineUserStats.value = monitoringStore.onlineUserStats
  await nextTick()
  updateRoleChart()
  updateLocationChart()
}

// 刷新登录统计
const refreshLoginStats = async () => {
  await monitoringStore.fetchLoginStats(parseInt(selectedPeriod.value))
  loginStats.value = monitoringStore.loginStats
  await nextTick()
  updateTrendChart()
  updateHourChart()
}

// 处理时间周期变化
const handlePeriodChange = () => {
  refreshLoginStats()
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  if (roleChartRef.value) {
    roleChart = echarts.init(roleChartRef.value)
  }
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
  if (locationChartRef.value) {
    locationChart = echarts.init(locationChartRef.value)
  }
  if (hourChartRef.value) {
    hourChart = echarts.init(hourChartRef.value)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    roleChart?.resize()
    trendChart?.resize()
    locationChart?.resize()
    hourChart?.resize()
  })
}

// 更新角色分布图表
const updateRoleChart = () => {
  if (!roleChart || !onlineUserStats.value) return
  
  const option = {
    title: {
      text: '在线用户角色分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '用户角色',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: onlineUserStats.value.byRole.ADMIN, name: '管理员' },
          { value: onlineUserStats.value.byRole.BOSS, name: '老板' },
          { value: onlineUserStats.value.byRole.EMPLOYEE, name: '员工' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  roleChart.setOption(option)
}

// 更新登录趋势图表
const updateTrendChart = () => {
  if (!trendChart || !loginStats.value) return
  
  const dates = Object.keys(loginStats.value.byDate).sort()
  const values = dates.map(date => loginStats.value!.byDate[date])
  
  const option = {
    title: {
      text: '登录趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates.map(date => date.substring(5)) // 只显示月-日
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '登录次数',
        type: 'line',
        data: values,
        smooth: true,
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 更新地理位置分布图表
const updateLocationChart = () => {
  if (!locationChart || !onlineUserStats.value) return
  
  const locations = Object.entries(onlineUserStats.value.byLocation)
    .map(([location, count]) => ({ name: location, value: count }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 10)
  
  const option = {
    title: {
      text: '地理位置分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '地理位置',
        type: 'pie',
        radius: '70%',
        data: locations,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  locationChart.setOption(option)
}

// 更新时段分析图表
const updateHourChart = () => {
  if (!hourChart || !loginStats.value) return
  
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'))
  const values = hours.map(hour => loginStats.value!.byHour[hour] || 0)
  
  const option = {
    title: {
      text: '登录时段分析',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: hours.map(hour => `${hour}:00`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '登录次数',
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  
  hourChart.setOption(option)
}

// 监听数据变化
watch(() => monitoringStore.onlineUserStats, (newStats) => {
  onlineUserStats.value = newStats
  updateRoleChart()
  updateLocationChart()
})

watch(() => monitoringStore.loginStats, (newStats) => {
  loginStats.value = newStats
  updateTrendChart()
  updateHourChart()
})

// 初始化
onMounted(async () => {
  await initCharts()
  await refreshAllData()
})
</script>

<style lang="scss" scoped>
.monitoring-dashboard {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .page-description {
        color: #606266;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .overview-cards {
    margin-bottom: 20px;

    .overview-card {
      height: 120px;

      &.online {
        border-left: 4px solid #67c23a;
      }

      &.login {
        border-left: 4px solid #409eff;
      }

      &.success {
        border-left: 4px solid #e6a23c;
      }

      &.users {
        border-left: 4px solid #f56c6c;
      }

      .card-content {
        display: flex;
        align-items: center;
        gap: 16px;
        height: 100%;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28px;
          background-color: #f5f7fa;
          color: #909399;
        }

        .card-info {
          flex: 1;

          .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #303133;
            line-height: 1;
            margin-bottom: 8px;
          }

          .card-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }

          .card-trend {
            .trend-text {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 20px;

    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .refresh-button {
          padding: 4px;
          color: #606266;
          transition: all 0.3s ease;

          &:hover {
            color: #409EFF;
            background-color: #f0f9ff;
          }

          .el-icon {
            font-size: 16px;
          }
        }
      }

      .chart-container {
        .chart {
          width: 100%;
          height: 300px;
        }
      }
    }
  }

  .stats-tables {
    .table-card {
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .monitoring-dashboard {
    .overview-cards {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .charts-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .monitoring-dashboard {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-actions {
        justify-content: flex-end;
      }
    }

    .overview-cards {
      .overview-card {
        margin-bottom: 16px;

        .card-content {
          .card-icon {
            width: 48px;
            height: 48px;
            font-size: 24px;
          }

          .card-info {
            .card-value {
              font-size: 24px;
            }
          }
        }
      }
    }

    .charts-section {
      .chart-card {
        .chart-container {
          .chart {
            height: 250px;
          }
        }
      }
    }
  }
}
</style>
