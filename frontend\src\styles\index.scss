// 使用新的@use语法导入变量和混入
@use 'variables.scss' as *;
@use 'mixins.scss' as *;

// 使用@use语法导入组件样式
@use 'components.scss';

// 使用@use语法导入导航样式
@use 'navigation.scss';

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-page);
  transition: var(--transition-base);
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-base);
  
  &:hover {
    color: var(--primary-light);
  }
}

// 按钮样式
button {
  border: none;
  outline: none;
  cursor: pointer;
  transition: var(--transition-base);
}

// 输入框样式
input, textarea, select {
  outline: none;
  font-family: inherit;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
}

// 表格样式
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 列表样式
ul, ol {
  list-style: none;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 3px;
  
  &:hover {
    background: var(--border-light);
  }
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-ellipsis {
  @include text-ellipsis();
}

.text-ellipsis-2 {
  @include text-ellipsis(2);
}

.text-ellipsis-3 {
  @include text-ellipsis(3);
}

// 间距工具类
@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in (0: 0, 1: var(--spacing-xs), 2: var(--spacing-sm), 3: var(--spacing-md), 4: var(--spacing-lg), 5: var(--spacing-xl)) {
    .#{$abbrev}-#{$size} { #{$prop}: $length; }
    .#{$abbrev}t-#{$size} { #{$prop}-top: $length; }
    .#{$abbrev}r-#{$size} { #{$prop}-right: $length; }
    .#{$abbrev}b-#{$size} { #{$prop}-bottom: $length; }
    .#{$abbrev}l-#{$size} { #{$prop}-left: $length; }
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length;
      #{$prop}-right: $length;
    }
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length;
      #{$prop}-bottom: $length;
    }
  }
}

// 显示/隐藏工具类
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

// Flex工具类
.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.align-stretch {
  align-items: stretch;
}

// 颜色工具类
.text-primary {
  color: var(--text-primary);
}

.text-regular {
  color: var(--text-regular);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

// 背景色工具类
.bg-primary {
  background-color: var(--primary-color);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

.bg-info {
  background-color: var(--info-color);
}

// 边框工具类
.border {
  border: 1px solid var(--border-base);
}

.border-top {
  border-top: 1px solid var(--border-base);
}

.border-right {
  border-right: 1px solid var(--border-base);
}

.border-bottom {
  border-bottom: 1px solid var(--border-base);
}

.border-left {
  border-left: 1px solid var(--border-base);
}

.border-0 {
  border: 0;
}

// 圆角工具类
.rounded {
  border-radius: var(--border-radius-base);
}

.rounded-sm {
  border-radius: var(--border-radius-small);
}

.rounded-lg {
  border-radius: var(--border-radius-large);
}

.rounded-circle {
  border-radius: var(--border-radius-circle);
}

// 阴影工具类
.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow-dark {
  box-shadow: var(--shadow-dark);
}

.shadow-none {
  box-shadow: none;
}

// 响应式工具类
@include respond-below(md) {
  .d-md-none {
    display: none;
  }
  
  .d-md-block {
    display: block;
  }
}

@include respond-to(md) {
  .d-md-none {
    display: none;
  }
  
  .d-md-block {
    display: block;
  }
}
