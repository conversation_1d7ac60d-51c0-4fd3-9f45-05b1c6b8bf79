import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { config } from '../config/env';
import { ValidationError } from './errorHandler';

// 确保上传目录存在
const uploadDir = config.UPLOAD_PATH;
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 创建子目录
const createSubDirs = () => {
  const subDirs = ['screenshots', 'documents'];
  subDirs.forEach(dir => {
    const fullPath = path.join(uploadDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  });
};

createSubDirs();

// 存储配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let subDir = 'documents'; // 默认目录

    // 根据请求路径确定子目录
    if (req.path.includes('/screenshots')) {
      subDir = 'screenshots';
    } else if (req.body.type === 'screenshot') {
      subDir = 'screenshots';
    }

    const destPath = path.join(uploadDir, subDir);
    cb(null, destPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const ext = path.extname(file.originalname);
    const filename = `${timestamp}_${random}${ext}`;
    cb(null, filename);
  },
});

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 允许的文件类型
  const allowedTypes = {
    image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    video: ['video/mp4', 'video/mov', 'video/avi'],
    document: ['application/pdf', 'text/plain'],
  };
  
  const allAllowedTypes = [
    ...allowedTypes.image,
    ...allowedTypes.video,
    ...allowedTypes.document,
  ];
  
  if (allAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError('不支持的文件类型'));
  }
};

// 创建multer实例
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.MAX_FILE_SIZE, // 5MB
    files: 10, // 最多10个文件
  },
});

// 单文件上传中间件
export const uploadSingle = (fieldName: string = 'file') => {
  return upload.single(fieldName);
};

// 多文件上传中间件
export const uploadMultiple = (fieldName: string = 'files', maxCount: number = 5) => {
  return upload.array(fieldName, maxCount);
};

// 删除文件工具函数
export const deleteFile = (filePath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// 获取文件URL
export const getFileUrl = (filename: string, type: string = 'documents'): string => {
  return `/uploads/${type}/${filename}`;
};
