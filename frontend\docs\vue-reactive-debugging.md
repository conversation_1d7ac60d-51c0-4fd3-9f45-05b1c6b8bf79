# Vue 3 响应式对象调试指南

## 问题描述

在Vue 3中，当我们直接使用`console.log`输出响应式对象时，控制台会显示`Proxy(Object)`，这是因为Vue 3使用Proxy来实现响应式系统。这种输出对调试不友好，且可能暴露内部实现细节。

## 错误示例

```typescript
// ❌ 错误的做法
const handleView = (order: Order) => {
  console.log('查看订单:', order) // 输出: Proxy(Object) {...}
}

const handleEdit = (task: Task) => {
  console.log('编辑任务:', task) // 输出: Proxy(Object) {...}
}
```

## 正确的解决方案

### 1. 使用 toRaw() 函数

```typescript
import { toRaw } from 'vue'

// ✅ 正确的做法
const handleView = (order: Order) => {
  console.log('查看订单:', toRaw(order)) // 输出原始对象
}

const handleEdit = (task: Task) => {
  console.log('编辑任务:', toRaw(task)) // 输出原始对象
}
```

### 2. 使用项目提供的调试工具

```typescript
import { debug } from '@/utils/debug'

// ✅ 推荐的做法
const handleView = (order: Order) => {
  debug.log('查看订单', order) // 自动处理响应式对象
}

const handleEdit = (task: Task) => {
  debug.info('编辑任务', task) // 自动处理响应式对象
}

// 输出关键信息
const handleViewDetails = (order: Order) => {
  debug.keys('订单关键信息', order, ['id', 'orderNo', 'customerName', 'status'])
}

// 表格形式输出数组
const handleViewList = (orders: Order[]) => {
  debug.table('订单列表', orders, ['orderNo', 'customerName', 'status', 'price'])
}
```

### 3. 生产环境自动移除调试代码

项目中的调试工具会自动检查环境变量，在生产环境中不会输出任何调试信息：

```typescript
// 只在开发环境输出，生产环境自动忽略
debug.log('调试信息', data)
```

## 最佳实践

### 1. 功能实现优先

在实际的事件处理函数中，应该优先实现具体功能，而不是仅仅输出调试信息：

```typescript
// ✅ 正确的实现
const handleView = (order: Order) => {
  selectedOrderId.value = order.id
  showDetailDialog.value = true
}

const handleEdit = (order: Order) => {
  selectedOrderId.value = order.id
  showEditDialog.value = true
}
```

### 2. 调试信息的使用场景

- **开发阶段**: 使用调试工具了解数据结构和流程
- **问题排查**: 输出关键信息帮助定位问题
- **性能优化**: 使用性能调试工具测量执行时间

```typescript
// 开发阶段调试
debug.reactive('用户数据分析', userData)

// 问题排查
debug.if(hasError, '错误详情', errorData)

// 性能测量
const result = await debug.performance('获取订单列表', async () => {
  return await orderApi.getOrders(query)
})
```

### 3. 清理调试代码

在功能完成后，应该：

1. 移除不必要的调试输出
2. 保留有助于问题排查的关键调试信息
3. 确保生产环境不会输出敏感信息

## 常见的响应式对象类型

### ref 对象
```typescript
const count = ref(0)
console.log(count) // RefImpl {_value: 0, ...}
console.log(toRaw(count)) // {_value: 0, ...}
console.log(count.value) // 0 (推荐直接访问.value)
```

### reactive 对象
```typescript
const state = reactive({ name: 'John', age: 30 })
console.log(state) // Proxy(Object) {name: 'John', age: 30}
console.log(toRaw(state)) // {name: 'John', age: 30}
```

### computed 对象
```typescript
const fullName = computed(() => `${firstName.value} ${lastName.value}`)
console.log(fullName) // ComputedRefImpl {...}
console.log(fullName.value) // "John Doe" (推荐直接访问.value)
```

## 工具函数参考

项目提供的调试工具函数：

- `debug.log(label, data)` - 基础调试输出
- `debug.info(label, data)` - 信息级别输出
- `debug.warn(label, data)` - 警告级别输出
- `debug.error(label, data)` - 错误级别输出
- `debug.reactive(label, data)` - 响应式对象详细分析
- `debug.keys(label, obj, keys)` - 输出对象的指定字段
- `debug.table(label, array, columns)` - 表格形式输出数组
- `debug.performance(label, fn)` - 性能测量
- `debug.if(condition, label, data)` - 条件调试

## 总结

1. **避免直接输出响应式对象** - 使用`toRaw()`或项目调试工具
2. **优先实现功能** - 调试代码不应该是主要逻辑
3. **使用合适的调试工具** - 根据需要选择合适的调试方法
4. **注意生产环境** - 确保调试代码不会影响生产环境性能
5. **及时清理** - 在功能完成后清理不必要的调试代码
