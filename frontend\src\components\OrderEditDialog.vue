<template>
  <el-dialog
    v-model="visible"
    title="编辑订单"
    width="700px"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 动态表单驱动系统 -->
      <el-form
        v-if="gameFormFields.length > 0"
        ref="formRef"
        :model="formData"
        :rules="dynamicFormRules"
        label-width="120px"
      >
        <!-- 根据游戏字段配置动态生成表单项 -->
        <el-form-item
          v-for="field in sortedFormFields"
          :key="field.fieldKey"
          :label="field.fieldLabel"
          :prop="field.fieldKey"
          :required="field.isRequired"
        >
          <!-- 文本输入框 -->
          <el-input
            v-if="field.fieldType === 'TEXT'"
            v-model="formData[field.fieldKey]"
            :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
          />

          <!-- 多行文本框 -->
          <el-input
            v-else-if="field.fieldType === 'TEXTAREA'"
            v-model="formData[field.fieldKey]"
            type="textarea"
            :rows="3"
            :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.fieldType === 'NUMBER'"
            v-model="formData[field.fieldKey]"
            style="width: 100%"
          />

          <!-- 选择框 -->
          <el-select
            v-else-if="field.fieldType === 'SELECT'"
            v-model="formData[field.fieldKey]"
            :placeholder="field.placeholder || `请选择${field.fieldLabel}`"
            style="width: 100%"
          >
            <el-option
              v-for="option in getFieldOptions(field)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <!-- 多选框 -->
          <el-checkbox-group
            v-else-if="field.fieldType === 'CHECKBOX'"
            v-model="formData[field.fieldKey]"
          >
            <el-checkbox
              v-for="option in getFieldOptions(field)"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>

          <!-- 密码输入框 -->
          <el-input
            v-else-if="field.fieldType === 'PASSWORD'"
            v-model="formData[field.fieldKey]"
            type="password"
            show-password
            :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
          />

          <!-- 图片上传 -->
          <el-upload
            v-else-if="field.fieldType === 'IMAGE'"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="(response) => handleImageUpload(response, field.fieldKey)"
            :on-error="handleUploadError"
            :file-list="getImageList(field.fieldKey)"
            list-type="picture-card"
            :limit="1"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>

          <!-- 默认文本输入框 -->
          <el-input
            v-else
            v-model="formData[field.fieldKey]"
            :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
          />
        </el-form-item>



        <!-- 管理字段：优先级和完成期限 -->
        <el-divider content-position="left">管理信息</el-divider>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="低" value="LOW" />
            <el-option label="普通" value="NORMAL" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>

        <el-form-item label="完成期限" prop="deadline">
          <el-date-picker
            v-model="formData.deadline"
            type="datetime"
            placeholder="请选择完成期限（可选）"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>

      <!-- 基础表单（当没有字段配置时） -->
      <el-form
        v-else
        ref="formRef"
        :model="formData"
        :rules="basicFormRules"
        label-width="120px"
      >
        <el-form-item label="客户姓名" prop="customerName">
          <el-input v-model="formData.customerName" placeholder="请输入客户姓名" />
        </el-form-item>

        <el-form-item label="联系方式" prop="customer_contact">
          <el-input v-model="formData.customer_contact" placeholder="请输入联系方式（可选）" />
        </el-form-item>

        <el-form-item label="游戏账号" prop="gameAccount">
          <el-input v-model="formData.gameAccount" placeholder="请输入游戏账号" />
        </el-form-item>

        <el-form-item label="游戏密码" prop="gamePassword">
          <el-input
            v-model="formData.gamePassword"
            type="password"
            show-password
            placeholder="请输入游戏密码"
          />
        </el-form-item>

        <el-form-item label="订单价格" prop="price">
          <el-input-number
            v-model="formData.price"
            :min="0.01"
            :precision="2"
            placeholder="请输入订单价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="特殊要求" prop="requirements">
          <el-input
            v-model="formData.requirements"
            type="textarea"
            :rows="3"
            placeholder="请输入特殊要求（可选）"
          />
        </el-form-item>

        <!-- 管理字段：优先级和完成期限 -->
        <el-divider content-position="left">管理信息</el-divider>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="低" value="LOW" />
            <el-option label="普通" value="NORMAL" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>

        <el-form-item label="完成期限" prop="deadline">
          <el-date-picker
            v-model="formData.deadline"
            type="datetime"
            placeholder="请选择完成期限（可选）"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 导入动态表单驱动系统的API
import { request } from '../api/http'
import { getActiveFieldsByGameId, type GameFormField } from '../api/gameFormFields'

// API 函数
const orderApi = {
  async getOrderById(id: string) {
    return request.get(`/orders/${id}`)
  },

  async updateOrder(id: string, data: any) {
    return request.put(`/orders/${id}`, data)
  }
}

// Props
interface Props {
  modelValue: boolean
  orderId: string | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const gameFormFields = ref<GameFormField[]>([])
const formData = reactive<Record<string, any>>({})

// 上传相关
const uploadUrl = ref('/api/v1/upload/image')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 计算属性
const sortedFormFields = computed(() => {
  return gameFormFields.value.sort((a, b) => a.sortOrder - b.sortOrder)
})

// 动态表单验证规则
const dynamicFormRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  gameFormFields.value.forEach(field => {
    if (field.isRequired) {
      rules[field.fieldKey] = [
        { required: true, message: `请输入${field.fieldLabel}`, trigger: 'blur' }
      ]
    }
  })
  
  return rules
})

// 基础表单验证规则
const basicFormRules: FormRules = {
  customerName: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '客户姓名长度应在1-50个字符之间', trigger: 'blur' }
  ],
  gameAccount: [
    { required: true, message: '请输入游戏账号', trigger: 'blur' },
    { min: 1, max: 100, message: '游戏账号长度应在1-100个字符之间', trigger: 'blur' }
  ],
  gamePassword: [
    { required: true, message: '请输入游戏密码', trigger: 'blur' },
    { min: 1, max: 100, message: '游戏密码长度应在1-100个字符之间', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入订单价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单价格必须大于0', trigger: 'blur' }
  ]
}

// 方法
const getFieldOptions = (field: GameFormField) => {
  try {
    let options = field.options
    if (typeof options === 'string') {
      options = JSON.parse(options)
    }

    if (!Array.isArray(options)) {
      return []
    }

    // 如果选项是字符串数组，转换为对象数组
    return options.map(option => {
      if (typeof option === 'string') {
        return { label: option, value: option }
      }
      // 如果已经是对象格式，直接返回
      return option
    })
  } catch {
    return []
  }
}

const getImageList = (fieldKey: string) => {
  const value = formData[fieldKey]
  if (!value) return []
  
  return [{
    name: 'image',
    url: value
  }]
}

const handleImageUpload = (response: any, fieldKey: string) => {
  if (response.success && response.data?.url) {
    formData[fieldKey] = response.data.url
    ElMessage.success('图片上传成功')
  }
}

const handleUploadError = () => {
  ElMessage.error('图片上传失败')
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  gameFormFields.value = []
  formRef.value?.resetFields()
}

const loadOrderData = async () => {
  if (!props.orderId) return

  try {
    loading.value = true

    // 获取订单详情
    const orderResponse = await orderApi.getOrderById(props.orderId)
    const order = orderResponse.data

    console.log('🔍 订单详情:', order)
    console.log('🔍 订单gameId:', order.gameId)
    console.log('🔍 订单gameType:', order.gameType)

    // 加载游戏字段配置
    let gameIdToUse = order.gameId

    // 如果没有gameId但有gameType，尝试通过gameType查找游戏
    if (!gameIdToUse && order.gameType) {
      console.log('🔍 没有gameId，尝试通过gameType查找游戏:', order.gameType)
      try {
        // 调用API查找游戏
        const gamesResponse = await request.get('/games', {
          params: { keyword: order.gameType, limit: 1 }
        })
        if (gamesResponse.data?.items?.length > 0) {
          const game = gamesResponse.data.items[0]
          gameIdToUse = game.id
          console.log('🔍 通过gameType找到游戏:', game)
        }
      } catch (error) {
        console.error('🔍 通过gameType查找游戏失败:', error)
      }
    }

    if (gameIdToUse) {
      console.log('🔍 开始加载游戏字段配置, gameId:', gameIdToUse)
      const fieldsResponse = await getActiveFieldsByGameId(gameIdToUse)
      gameFormFields.value = fieldsResponse || []
      console.log('🔍 获取到的字段配置:', gameFormFields.value)
      console.log('🔍 字段数量:', gameFormFields.value.length)
    } else {
      console.log('🔍 订单没有gameId且无法通过gameType找到游戏，使用基础表单')
      gameFormFields.value = []
    }

    // 填充表单数据
    console.log('🔍 开始填充表单数据')
    console.log('🔍 订单基础数据:', order)
    console.log('🔍 订单details:', order.details)
    console.log('🔍 订单formData:', order.formData)

    // 先填充基础订单数据
    Object.assign(formData, order)

    // 然后用动态字段数据覆盖（如果存在）
    if (order.details) {
      console.log('🔍 使用details字段数据')
      Object.assign(formData, order.details)
    } else if (order.formData) {
      console.log('🔍 使用formData字段数据')
      Object.assign(formData, order.formData)
    } else {
      console.log('🔍 没有动态字段数据，使用基础字段映射')
      // 基础字段到动态字段的映射 - 统一使用驼峰命名
      const fieldMapping = {
        'customerName': order.customerName,
        'customerContact': order.customerContact,
        'gameAccount': order.gameAccount,
        'gamePassword': order.gamePassword,
        'price': order.price,
        'requirements': order.requirements
      }

      console.log('🔍 字段映射:', fieldMapping)
      Object.assign(formData, fieldMapping)
    }

    console.log('🔍 最终表单数据:', formData)

    console.log('🔍 填充后的表单数据:', formData)

  } catch (error) {
    console.error('加载订单数据失败:', error)
    ElMessage.error('加载订单数据失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.orderId) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 分离动态字段和固定字段
    const dynamicData: Record<string, any> = {}
    const fixedData: Record<string, any> = {}

    // 字段映射：统一使用驼峰命名（不再需要转换）
    const fieldMapping: Record<string, string> = {
      'customerContact': 'customerContact',
      'customerName': 'customerName',
      'gameAccount': 'gameAccount',
      'gamePassword': 'gamePassword',
      'requirements': 'requirements'
    }

    Object.keys(formData).forEach(key => {
      const isGameField = gameFormFields.value.some(field => field.fieldKey === key)
      if (isGameField) {
        dynamicData[key] = formData[key]
      } else {
        // 对于固定字段，使用映射后的字段名
        const mappedKey = fieldMapping[key] || key
        fixedData[mappedKey] = formData[key]
      }
    })

    // 构建更新数据
    const updateData: Record<string, any> = {
      ...fixedData,
      // 将动态字段存储到details字段（新思路核心要求）
      details: dynamicData,
      formData: dynamicData // 保持向后兼容
    }

    // 数据类型修复
    // 处理deadline字段：空值或无效日期时删除该字段
    if (updateData.deadline === '' || updateData.deadline === null || updateData.deadline === undefined) {
      delete updateData.deadline
    } else if (updateData.deadline && typeof updateData.deadline === 'string') {
      // 验证日期格式
      const date = new Date(updateData.deadline)
      if (isNaN(date.getTime())) {
        delete updateData.deadline // 无效日期，删除字段
      }
    }

    // 处理必需的字符串字段：确保是字符串类型
    const requiredStringFields = ['gameAccount', 'gamePassword', 'customerName']
    requiredStringFields.forEach(field => {
      if (updateData[field] !== undefined && updateData[field] !== null) {
        updateData[field] = String(updateData[field])
      }
    })

    // 处理可选的字符串字段：空值时设为空字符串
    const optionalStringFields = ['customerContact', 'requirements']
    optionalStringFields.forEach(field => {
      if (updateData[field] === undefined || updateData[field] === null) {
        updateData[field] = ''
      } else {
        updateData[field] = String(updateData[field])
      }
    })

    // 处理数字字段
    if (updateData.price !== undefined && updateData.price !== null) {
      updateData.price = Number(updateData.price)
    }

    console.log('🔍 准备提交的更新数据:', updateData)
    console.log('🔍 动态字段数据:', dynamicData)
    console.log('🔍 固定字段数据:', fixedData)

    await orderApi.updateOrder(props.orderId, updateData)

    ElMessage.success('订单更新成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('更新订单失败:', error)
    ElMessage.error('更新订单失败')
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue && props.orderId) {
    nextTick(() => {
      // 每次打开对话框都重新加载数据和字段配置
      loadOrderData()
    })
  }
})

// 监听字段配置变化，重新加载字段
const refreshFields = async () => {
  if (!props.orderId) return

  try {
    const order = await orderApi.getOrderById(props.orderId)
    if (!order.success || !order.data) return

    const orderData = order.data
    const gameIdToUse = orderData.gameId

    // 如果没有gameId，暂时跳过字段加载
    // TODO: 可以考虑通过gameType查找对应的游戏ID

    if (gameIdToUse) {
      console.log('🔄 刷新字段配置, gameId:', gameIdToUse)
      const fieldsResponse = await getActiveFieldsByGameId(gameIdToUse)
      gameFormFields.value = fieldsResponse || []
      console.log('🔄 刷新后的字段配置:', gameFormFields.value)
    }
  } catch (error) {
    console.error('刷新字段配置失败:', error)
  }
}

// 暴露刷新方法给父组件
defineExpose({
  refreshFields
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    resetForm()
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style>
