import * as fs from 'fs';
import * as path from 'path';
import { logger } from '../utils/logger';

// 导入ip2region绑定
const ip2region = require('../lib/index.js');

// 定义地理位置信息接口
export interface LocationInfo {
  country: string;
  region: string;
  province: string;
  city: string;
  isp: string;
  fullLocation: string;
}

export class IpLocationService {
  private searcher: any;
  private isInitialized: boolean = false;

  constructor() {
    this.initializeDatabase();
  }

  private initializeDatabase() {
    try {
      const dbPath = path.join(__dirname, '../data/ip2region.xdb');
      
      // 检查数据库文件是否存在
      if (!fs.existsSync(dbPath)) {
        logger.error(`IP数据库文件不存在: ${dbPath}`);
        throw new Error(`IP数据库文件不存在: ${dbPath}`);
      }

      // 使用内存缓存方式加载整个数据库文件（推荐方式，性能最佳）
      const buffer = ip2region.loadContentFromFile(dbPath);
      this.searcher = ip2region.newWithBuffer(buffer);
      
      this.isInitialized = true;
      logger.info('IP地理位置数据库初始化成功', {
        dbPath,
        bufferSize: buffer.length,
        mode: 'memory-cached'
      });
    } catch (error) {
      logger.error('IP地理位置数据库初始化失败:', error);
      this.isInitialized = false;
      // 不抛出错误，允许服务继续运行，但IP解析会返回默认值
    }
  }

  // 解析IP地址地理位置
  async getLocation(ip: string): Promise<LocationInfo> {
    try {
      // 如果服务未初始化，返回默认位置
      if (!this.isInitialized) {
        logger.warn(`IP地理位置服务未初始化，返回默认位置: ${ip}`);
        return this.getDefaultLocation();
      }

      // 验证IP地址格式
      if (!this.isValidIP(ip)) {
        logger.warn(`无效的IP地址格式: ${ip}`);
        return this.getDefaultLocation();
      }

      // 查询地理位置
      const result = await this.searcher.search(ip);
      
      if (result && result.region) {
        const locationInfo = this.parseLocationString(result.region);
        
        logger.debug(`IP地址解析成功: ${ip} -> ${result.region}`, {
          ip,
          location: locationInfo,
          took: result.took,
          ioCount: result.ioCount
        });
        
        return locationInfo;
      } else {
        logger.warn(`IP地址解析无结果: ${ip}`);
        return this.getDefaultLocation();
      }
    } catch (error) {
      logger.error(`IP地址解析失败: ${ip}`, error);
      return this.getDefaultLocation();
    }
  }

  private parseLocationString(locationStr: string): LocationInfo {
    // ip2region格式：国家|区域|省份|城市|ISP
    const parts = locationStr.split('|');
    return {
      country: this.cleanLocationPart(parts[0]),
      region: this.cleanLocationPart(parts[1]),
      province: this.cleanLocationPart(parts[2]),
      city: this.cleanLocationPart(parts[3]),
      isp: this.cleanLocationPart(parts[4]),
      fullLocation: locationStr
    };
  }

  private cleanLocationPart(part: string): string {
    if (!part || part === '0' || part.trim() === '') {
      return '未知';
    }
    return part.trim();
  }

  private isValidIP(ip: string): boolean {
    // 使用ip2region内置的IP验证函数
    return ip2region.isValidIp(ip);
  }

  private getDefaultLocation(): LocationInfo {
    return {
      country: '未知',
      region: '未知',
      province: '未知',
      city: '未知',
      isp: '未知',
      fullLocation: '未知|未知|未知|未知|未知'
    };
  }

  // 批量查询（用于数据迁移或批量处理）
  async getLocationsBatch(ips: string[]): Promise<Map<string, LocationInfo>> {
    const results = new Map<string, LocationInfo>();
    
    for (const ip of ips) {
      results.set(ip, await this.getLocation(ip));
    }
    
    return results;
  }

  // 获取服务状态
  getStatus(): { initialized: boolean; dbPath: string } {
    const dbPath = path.join(__dirname, '../data/ip2region.xdb');
    return {
      initialized: this.isInitialized,
      dbPath
    };
  }

  // 重新初始化服务（用于数据库更新后）
  async reinitialize(): Promise<void> {
    logger.info('重新初始化IP地理位置服务...');
    this.initializeDatabase();
  }
}

// 创建单例实例
export const ipLocationService = new IpLocationService();
