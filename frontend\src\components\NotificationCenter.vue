<template>
  <div class="notification-center">
    <!-- 通知按钮 -->
    <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
      <el-button 
        circle 
        :icon="Bell" 
        @click="toggleNotificationPanel"
        :class="{ 'has-notifications': unreadCount > 0 }"
      />
    </el-badge>

    <!-- 通知面板 -->
    <el-drawer
      v-model="showNotificationPanel"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="notification-panel">
        <!-- 操作栏 -->
        <div class="notification-actions">
          <div class="action-buttons">
            <el-button size="small" @click="markAllAsRead" :disabled="unreadCount === 0" :loading="loading">
              全部已读
            </el-button>
            <el-button size="small" @click="refreshNotifications" :icon="Refresh" :loading="loading">
              刷新
            </el-button>
          </div>

          <!-- 筛选器 -->
          <div class="filter-section">
            <el-select v-model="filterType" placeholder="类型筛选" size="small" clearable style="width: 100px;">
              <el-option label="信息" value="INFO" />
              <el-option label="成功" value="SUCCESS" />
              <el-option label="警告" value="WARNING" />
              <el-option label="错误" value="ERROR" />
            </el-select>

            <el-select v-model="filterRead" placeholder="状态筛选" size="small" clearable style="width: 100px;">
              <el-option label="未读" :value="false" />
              <el-option label="已读" :value="true" />
            </el-select>

            <el-button size="small" @click="applyFilter" :icon="Filter" :loading="loading">
              筛选
            </el-button>
            <el-button size="small" @click="resetFilter" :loading="loading">
              重置
            </el-button>
          </div>
        </div>

        <!-- 通知列表 -->
        <div class="notification-list">
          <!-- 加载状态 -->
          <div v-if="loading && notifications.length === 0" class="loading-state">
            <el-skeleton :rows="3" animated />
          </div>

          <!-- 通知项 -->
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.isRead }"
            @click="markAsRead(notification.id)"
          >
            <div class="notification-icon">
              <el-icon :class="getNotificationIconClass(notification.type)">
                <component :is="getNotificationIcon(notification.type)" />
              </el-icon>
            </div>

            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.content }}</div>
              <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
            </div>

            <div class="notification-actions-mini">
              <el-button
                size="small"
                text
                @click.stop="removeNotification(notification.id)"
                :icon="Close"
              />
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMore" class="load-more">
            <el-button
              @click="loadMoreNotifications"
              :loading="loading"
              text
              style="width: 100%;"
            >
              加载更多
            </el-button>
          </div>

          <!-- 空状态 -->
          <el-empty
            v-if="!loading && notifications.length === 0"
            description="暂无通知"
            :image-size="100"
          />
        </div>
      </div>
    </el-drawer>

    <!-- 连接状态指示器 -->
    <div class="connection-status" v-if="showConnectionStatus">
      <el-tag 
        :type="isConnected ? 'success' : 'danger'" 
        size="small"
        effect="dark"
      >
        <el-icon><component :is="isConnected ? 'Connection' : 'Close'" /></el-icon>
        {{ isConnected ? '已连接' : '连接断开' }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Bell, Close, Message, Warning, InfoFilled, SuccessFilled, Refresh, Filter } from '@element-plus/icons-vue'
import { getSocketService } from '@/services/socketService'
import { formatRelativeTime } from '@/utils/date'
import { useNotificationStore } from '@/stores/notifications'
import type { Notification } from '@/api/notifications'

// 使用通知store
const notificationStore = useNotificationStore()

// 响应式数据
const showNotificationPanel = ref(false)
const showConnectionStatus = ref(false)
const isConnected = ref(false)
const filterType = ref<string>('')
const filterRead = ref<boolean | undefined>(undefined)

// 计算属性
const unreadCount = computed(() => notificationStore.unreadCount)
const notifications = computed(() => notificationStore.notifications)
const loading = computed(() => notificationStore.loading)
const hasMore = computed(() => notificationStore.pagination.hasNext)

// 检查连接状态
const checkConnectionStatus = () => {
  const socketService = getSocketService()
  isConnected.value = socketService?.isConnected() || false
}

// 切换通知面板
const toggleNotificationPanel = async () => {
  showNotificationPanel.value = !showNotificationPanel.value

  // 首次打开时加载通知
  if (showNotificationPanel.value && notifications.value.length === 0) {
    await loadNotifications()
  }
}

// 加载通知
const loadNotifications = async () => {
  notificationStore.resetPagination()
  await notificationStore.fetchNotifications({
    type: filterType.value || undefined,
    isRead: filterRead.value
  })
}

// 加载更多通知
const loadMoreNotifications = async () => {
  await notificationStore.loadMoreNotifications({
    type: filterType.value || undefined,
    isRead: filterRead.value
  })
}

// 应用筛选
const applyFilter = async () => {
  await loadNotifications()
}

// 重置筛选
const resetFilter = async () => {
  filterType.value = ''
  filterRead.value = undefined
  await loadNotifications()
}

// 刷新通知
const refreshNotifications = async () => {
  await loadNotifications()
}

// 标记为已读
const markAsRead = async (id: string) => {
  await notificationStore.markAsRead(id)
}

// 全部标记为已读
const markAllAsRead = async () => {
  await notificationStore.markAllAsRead()
}

// 移除通知
const removeNotification = async (id: string) => {
  await notificationStore.deleteNotification(id)
}

// 清空所有通知
const clearAllNotifications = () => {
  notificationStore.clearAllNotifications()
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap = {
    info: InfoFilled,
    success: SuccessFilled,
    warning: Warning,
    error: Close
  }
  return iconMap[type as keyof typeof iconMap] || InfoFilled
}

// 获取通知图标样式类
const getNotificationIconClass = (type: string) => {
  const classMap = {
    info: 'text-blue-500',
    success: 'text-green-500',
    warning: 'text-orange-500',
    error: 'text-red-500'
  }
  return classMap[type as keyof typeof classMap] || 'text-blue-500'
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return formatRelativeTime(timestamp)
}

// 定时检查连接状态 - 减少检查频率
let connectionCheckInterval: ReturnType<typeof setInterval> | null = null

onMounted(async () => {
  // 初始检查连接状态
  checkConnectionStatus()

  // 定时检查连接状态 - 改为30秒检查一次，减少服务器压力
  connectionCheckInterval = setInterval(checkConnectionStatus, 30000)

  // 监听窗口焦点事件，显示连接状态
  window.addEventListener('focus', () => {
    showConnectionStatus.value = true
    setTimeout(() => {
      showConnectionStatus.value = false
    }, 3000)
  })

  // 获取未读通知数量
  await notificationStore.fetchUnreadCount()
})

onUnmounted(() => {
  if (connectionCheckInterval) {
    clearInterval(connectionCheckInterval)
  }
})

// 添加通知方法（用于Socket推送）
const addNotification = (notification: Omit<Notification, 'id' | 'isRead'>) => {
  const newNotification: Notification = {
    ...notification,
    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
    isRead: false,
    userId: '', // 这里会在store中处理
    updatedAt: new Date().toISOString()
  }

  notificationStore.addNotification(newNotification)
}

// 暴露方法给父组件
defineExpose({
  addNotification,
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearAllNotifications,
  loadNotifications,
  refreshNotifications
})
</script>

<style lang="scss" scoped>
.notification-center {
  position: relative;
}

.notification-badge {
  .el-button {
    transition: all 0.3s ease;
    
    &.has-notifications {
      color: #409eff;
      background-color: #ecf5ff;
      border-color: #b3d8ff;
    }
  }
}

.notification-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-actions {
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;

  .action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
  }

  .filter-section {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
  }
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;

  .loading-state {
    padding: 16px;
  }

  .load-more {
    padding: 16px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
  }
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.unread {
    background-color: #ecf5ff;
    border-left: 3px solid #409eff;
    
    .notification-title {
      font-weight: 600;
    }
  }
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
  
  .el-icon {
    font-size: 18px;
  }
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  line-height: 1.4;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-actions-mini {
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .notification-item:hover & {
    opacity: 1;
  }
}

.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  
  .el-tag {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}
</style>
