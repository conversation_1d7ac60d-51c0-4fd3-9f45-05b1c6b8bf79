// 用户角色
export enum UserRole {
  ADMIN = 'ADMIN',
  BOSS = 'BOSS',
  EMPLOYEE = 'EMPLOYEE'
}

// 用户状态
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BANNED = 'BANNED'
}

// 订单状态
export enum OrderStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 订单优先级
export enum OrderPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 任务分配类型
export enum AssignType {
  DIRECT = 'DIRECT',
  SYSTEM = 'SYSTEM'
}

// 任务状态
export enum TaskStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PAUSED = 'PAUSED',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 结算状态
export enum SettlementStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 会话状态
export enum SessionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  FORCED_LOGOUT = 'FORCED_LOGOUT'
}

// 登录结果
export enum LoginResult {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  BLOCKED = 'BLOCKED'
}

// 结算记录
export interface Settlement {
  id: string
  taskId: string
  userId: string
  amount: number
  status: SettlementStatus
  settledAt?: string
  notes?: string
  createdAt: string
  updatedAt: string
  task?: {
    id: string
    taskNo: string
    order?: {
      id: string
      customerName: string
    }
  }
  user?: {
    id: string
    username: string
    nickname?: string
  }
}

// 用户信息
export interface User {
  id: string
  username: string
  nickname?: string
  phone?: string
  role: UserRole
  status: UserStatus
  level: number
  totalEarnings: number
  createdAt: string
  updatedAt: string
}

// 用户统计信息
export interface UserStats {
  user: {
    id: string
    username: string
    nickname?: string
    level: number
    totalEarnings: number
  }
  statistics: {
    totalTasks: number
    completedTasks: number
    completionRate: number
    totalHours: number
    avgHoursPerTask: number
  }
}

// 订单信息
export interface Order {
  id: string
  orderNo: string
  customerName: string
  customerContact?: string
  gameAccount: string
  gamePassword: string

  // 游戏类型支持
  gameType: string

  // 多游戏支持（可选）
  gameId?: string
  currentRankId?: string
  targetRankId?: string

  // 段位字段（字符串形式）
  currentRank?: string
  targetRank?: string

  price: number
  deadline?: string
  requirements?: string
  status: OrderStatus
  priority: OrderPriority
  createdById: string
  createdAt: string
  updatedAt: string

  // 模板相关字段
  templateId?: string
  templateVersion?: number
  formData?: Record<string, any>

  // 关联数据
  game?: {
    id: string
    name: string
    displayName: string
    icon?: string
  }
  currentRankInfo?: {
    id: string
    name: string
    displayName: string
    level: number
  }
  targetRankInfo?: {
    id: string
    name: string
    displayName: string
    level: number
  }
  createdBy?: Pick<User, 'id' | 'username' | 'nickname'>
  tasks?: Task[]
}

// 任务信息
export interface Task {
  id: string
  taskNo: string
  orderId: string
  assigneeId?: string
  assignType: AssignType
  status: TaskStatus
  startTime?: string
  endTime?: string
  estimatedHours?: number
  actualHours?: number
  commission?: number
  commissionType?: CommissionType
  commissionParams?: CommissionCalculationParams
  description?: string
  notes?: string
  createdAt: string
  updatedAt: string
  order?: Pick<Order, 'id' | 'orderNo' | 'customerName' | 'gameType' | 'gameAccount' | 'gamePassword' | 'price' | 'priority' | 'deadline'>
  assignee?: Pick<User, 'id' | 'username' | 'nickname' | 'level'>
  progress?: TaskProgress[]
}

// 任务进度
export interface TaskProgress {
  id: string
  taskId: string
  userId: string
  progress: number
  currentRank?: string
  description?: string
  screenshots?: string[]
  createdAt: string
  user?: Pick<User, 'id' | 'username' | 'nickname'>
}

// 结算记录
export interface Settlement {
  id: string
  taskId: string
  userId: string
  amount: number
  status: SettlementStatus
  settledAt?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

// API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: {
    message: string
    code?: string
    details?: any
  }
  timestamp: string
}

// 分页参数
export interface PaginationQuery {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
}

// 注册表单
export interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  nickname?: string
  phone?: string
}

// 订单表单
export interface OrderForm {
  customerName: string
  customerContact?: string
  gameAccount: string
  gamePassword: string

  // 多游戏支持
  gameId: string
  currentRankId: string
  targetRankId: string

  // 兼容旧版本字段（可选）
  currentRank?: string
  targetRank?: string

  price: number
  deadline?: string
  requirements?: string
  priority: OrderPriority
}

// 佣金计算参数
export interface CommissionCalculationParams {
  baseRate?: number
  difficultyMultiplier?: number
  priorityMultiplier?: number
  timeMultiplier?: number
  orderPrice?: number
  estimatedHours?: number
  currentRank?: string
  targetRank?: string
}

// 佣金计算结果
export interface CommissionCalculationResult {
  commission: number
  calculation: {
    baseCommission: number
    difficultyBonus: number
    priorityBonus: number
    timeBonus: number
    totalCommission: number
    formula: string
  }
}



// 佣金计算方式
export type CommissionType = 'AUTO' | 'MANUAL'

// 任务表单
export interface TaskForm {
  orderId: string
  assigneeId?: string
  assignType: AssignType
  estimatedHours?: number
  commission?: number
  commissionType: CommissionType
  commissionParams?: CommissionCalculationParams
  description?: string
  notes?: string
}

// 文件上传响应
export interface FileUploadResponse {
  filename: string
  originalName: string
  path: string
  size: number
  mimeType: string
  url: string
}

// 在线用户信息
export interface OnlineUser {
  id: string
  userId: string
  username: string
  nickname?: string
  role: UserRole
  sessionToken: string
  socketId?: string
  ipAddress: string
  userAgent?: string
  loginTime: string
  lastActivity: string
  onlineDuration: number // 在线时长（秒）
  locationCountry?: string
  locationProvince?: string
  locationCity?: string
  locationIsp?: string
  locationFull?: string
}

// 用户会话信息
export interface UserSession {
  id: string
  userId: string
  sessionToken: string
  socketId?: string
  ipAddress: string
  userAgent?: string
  loginTime: string
  lastActivity: string
  logoutTime?: string
  status: SessionStatus
  deviceInfo?: any
  locationCountry?: string
  locationProvince?: string
  locationCity?: string
  locationIsp?: string
  locationFull?: string
}

// 登录日志
export interface LoginLog {
  id: string
  userId?: string
  username: string
  ipAddress: string
  userAgent?: string
  loginTime: string
  logoutTime?: string
  loginResult: LoginResult
  failureReason?: string
  deviceInfo?: any
  sessionDuration?: number
  locationCountry?: string
  locationProvince?: string
  locationCity?: string
  locationIsp?: string
  locationFull?: string
}

// 在线用户筛选条件
export interface OnlineUserFilters {
  role?: UserRole
  ipAddress?: string
  locationProvince?: string
  locationCity?: string
  minOnlineTime?: number // 最小在线时长（分钟）
}

// 登录日志筛选条件
export interface LoginLogFilters {
  userId?: string
  username?: string
  ipAddress?: string
  loginResult?: LoginResult
  startDate?: string
  endDate?: string
  locationProvince?: string
  locationCity?: string
  page?: number
  limit?: number
}

// 在线用户统计
export interface OnlineUserStats {
  total: number
  byRole: {
    ADMIN: number
    BOSS: number
    EMPLOYEE: number
  }
  byLocation: Record<string, number>
  averageOnlineTime: number
  longestOnlineUser?: OnlineUser
}

// 登录统计
export interface LoginStats {
  period: string
  total: number
  successful: number
  failed: number
  blocked: number
  uniqueUsers: number
  uniqueIPs: number
  byDate: Record<string, number>
  byHour: Record<string, number>
  topLocations: Record<string, number>
  failureReasons: Record<string, number>
}

// 确保模块正确导出
export default {
  UserRole,
  UserStatus,
  OrderStatus,
  OrderPriority,
  AssignType,
  TaskStatus,
  SettlementStatus,
  SessionStatus,
  LoginResult
}
