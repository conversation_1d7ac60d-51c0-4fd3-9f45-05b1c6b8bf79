-- 添加游戏表单字段配置表
-- 根据新思路实现动态表单驱动系统

USE game_boost_db;

-- 创建表单字段类型枚举（如果数据库支持）
-- 或者在应用层进行验证

-- 创建游戏表单字段配置表
CREATE TABLE IF NOT EXISTS game_form_fields (
    id VARCHAR(191) NOT NULL PRIMARY KEY,
    gameId VARCHAR(191) NOT NULL,
    fieldKey VARCHAR(100) NOT NULL COMMENT '字段键名（程序内部使用的唯一标识）',
    fieldLabel VARCHAR(255) NOT NULL COMMENT '字段标签（显示给用户看的名字）',
    fieldType ENUM('TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE') NOT NULL COMMENT '字段类型',
    isRequired BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否必填',
    placeholder VARCHAR(255) NULL COMMENT '占位提示',
    sortOrder INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
    options JSON NULL COMMENT '选项列表（当字段类型是select或checkbox时使用）',
    config JSON NULL COMMENT '字段特殊配置（如最大长度、最小长度等）',
    isActive BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (gameId) REFERENCES games(id) ON DELETE CASCADE,
    
    -- 唯一约束：同一游戏内字段键名唯一
    UNIQUE KEY unique_game_field_key (gameId, fieldKey),
    
    -- 索引
    INDEX idx_game_sort (gameId, sortOrder),
    INDEX idx_game_active (gameId, isActive)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏表单字段配置表';

-- 为王者荣耀游戏添加示例字段配置
-- 首先查找王者荣耀游戏ID
SET @wzry_game_id = (SELECT id FROM games WHERE name = '王者荣耀' OR displayName LIKE '%王者荣耀%' LIMIT 1);

-- 如果没有王者荣耀游戏，先创建一个
INSERT IGNORE INTO games (id, name, displayName, description, isActive, sortOrder, createdAt, updatedAt) 
VALUES (
    'wzry_game_001', 
    '王者荣耀', 
    '王者荣耀', 
    '腾讯王者荣耀代练服务', 
    TRUE, 
    1, 
    NOW(), 
    NOW()
);

-- 重新获取游戏ID
SET @wzry_game_id = (SELECT id FROM games WHERE name = '王者荣耀' LIMIT 1);

-- 插入王者荣耀的表单字段配置
INSERT IGNORE INTO game_form_fields (id, gameId, fieldKey, fieldLabel, fieldType, isRequired, placeholder, sortOrder, options, config, isActive) VALUES
-- 基础信息字段
('field_001', @wzry_game_id, 'customer_name', '客户姓名', 'TEXT', TRUE, '请输入客户姓名', 1, NULL, '{"maxLength": 50}', TRUE),
('field_002', @wzry_game_id, 'customer_contact', '联系方式', 'TEXT', FALSE, '请输入手机号或QQ号', 2, NULL, '{"maxLength": 20}', TRUE),
('field_003', @wzry_game_id, 'game_account', '游戏账号', 'TEXT', TRUE, '请输入游戏账号', 3, NULL, '{"maxLength": 50}', TRUE),
('field_004', @wzry_game_id, 'game_password', '游戏密码', 'PASSWORD', TRUE, '请输入游戏密码', 4, NULL, '{"maxLength": 50}', TRUE),

-- 游戏相关字段
('field_005', @wzry_game_id, 'server_region', '游戏大区', 'SELECT', TRUE, '请选择游戏大区', 5, '["QQ安卓", "微信安卓", "QQ-iOS", "微信-iOS"]', NULL, TRUE),
('field_006', @wzry_game_id, 'current_rank', '当前段位', 'SELECT', TRUE, '请选择当前段位', 6, '["青铜V", "青铜IV", "青铜III", "青铜II", "青铜I", "白银V", "白银IV", "白银III", "白银II", "白银I", "黄金V", "黄金IV", "黄金III", "黄金II", "黄金I", "铂金V", "铂金IV", "铂金III", "铂金II", "铂金I", "钻石V", "钻石IV", "钻石III", "钻石II", "钻石I", "星耀V", "星耀IV", "星耀III", "星耀II", "星耀I", "王者"]', NULL, TRUE),
('field_007', @wzry_game_id, 'target_rank', '目标段位', 'SELECT', TRUE, '请选择目标段位', 7, '["青铜V", "青铜IV", "青铜III", "青铜II", "青铜I", "白银V", "白银IV", "白银III", "白银II", "白银I", "黄金V", "黄金IV", "黄金III", "黄金II", "黄金I", "铂金V", "铂金IV", "铂金III", "铂金II", "铂金I", "钻石V", "钻石IV", "钻石III", "钻石II", "钻石I", "星耀V", "星耀IV", "星耀III", "星耀II", "星耀I", "王者"]', NULL, TRUE),

-- 订单信息字段
('field_008', @wzry_game_id, 'price', '订单价格', 'NUMBER', TRUE, '请输入订单价格', 8, NULL, '{"min": 1, "max": 10000, "step": 1}', TRUE),
('field_009', @wzry_game_id, 'requirements', '特殊要求', 'TEXTAREA', FALSE, '请输入特殊要求或备注', 9, NULL, '{"maxLength": 500, "rows": 3}', TRUE);

COMMIT;
