<template>
  <div class="employees-page">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h2>员工管理</h2>
        <p>管理系统中的所有员工账户</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleCreate">
          新增员工
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、昵称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="激活" value="ACTIVE" />
            <el-option label="未激活" value="INACTIVE" />
            <el-option label="已封禁" value="BANNED" />
          </el-select>
        </el-form-item>

        <el-form-item label="等级">
          <el-select
            v-model="searchForm.level"
            placeholder="选择等级"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="level in [1, 2, 3, 4, 5]"
              :key="level"
              :label="`等级 ${level}`"
              :value="level"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 员工列表 -->
    <el-card class="table-card">
      <!-- 批量操作栏 -->
      <div class="table-toolbar" v-if="selectedEmployees.length > 0">
        <div class="selected-info">
          已选择 {{ selectedEmployees.length }} 个员工
        </div>
        <div class="batch-actions">
          <el-button size="small" @click="handleBatchActivate">批量激活</el-button>
          <el-button size="small" @click="handleBatchDeactivate">批量禁用</el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>

      <el-table
        :data="employees"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="username" label="用户名" width="120" />

        <el-table-column label="昵称" width="120">
          <template #default="{ row }">
            {{ row.nickname || '-' }}</template>
        </el-table-column>



        <el-table-column label="手机号" width="130">
          <template #default="{ row }">
            {{ row.phone || '-' }}
          </template>
        </el-table-column>

        <el-table-column label="等级" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" size="small">
              {{ row.level }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="总收益" width="120" align="right">
          <template #default="{ row }">
            ¥{{ row.totalEarnings.toFixed(2) }}
          </template>
        </el-table-column>

        <el-table-column label="注册时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="240" fixed="right" class-name="action-column">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button type="text" size="small" @click="handleView(row)" class="table-action-btn">
                查看
              </el-button>
              <el-button type="text" size="small" @click="handleEdit(row)" class="table-action-btn">
                编辑
              </el-button>
              <el-dropdown @command="(command) => handleStatusAction(command, row)" placement="bottom-end">
                <el-button type="text" size="small" class="table-action-btn">
                  状态 <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="activate" :disabled="row.status === 'ACTIVE'">
                      激活
                    </el-dropdown-item>
                    <el-dropdown-item command="deactivate" :disabled="row.status === 'INACTIVE'">
                      禁用
                    </el-dropdown-item>
                    <el-dropdown-item command="ban" :disabled="row.status === 'BANNED'">
                      封禁
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button type="text" size="small" @click="handleDelete(row)" class="table-action-btn danger">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 员工详情对话框 -->
    <EmployeeDetailDialog
      v-model="showDetailDialog"
      :employee-id="selectedEmployeeId"
      @edit="handleEditFromDetail"
    />

    <!-- 编辑员工对话框 -->
    <EmployeeEditDialog
      v-model="showEditDialog"
      :employee="selectedEmployee"
      @success="handleEditSuccess"
    />

    <!-- 新增员工对话框 -->
    <CreateEmployeeDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown } from '@element-plus/icons-vue'
import { userApi, type UserQuery } from '@/api/users'
import type { User, UserStatus } from '@/types'
import EmployeeDetailDialog from '@/components/EmployeeDetailDialog.vue'
import EmployeeEditDialog from '@/components/EmployeeEditDialog.vue'
import CreateEmployeeDialog from '@/components/CreateEmployeeDialog.vue'

// 日期格式化函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 响应式数据
const loading = ref(false)
const employees = ref<User[]>([])
const selectedEmployees = ref<User[]>([])
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showCreateDialog = ref(false)
const selectedEmployeeId = ref<string | null>(null)
const selectedEmployee = ref<User | null>(null)

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

const searchForm = reactive<UserQuery & { level?: number }>({
  keyword: '',
  status: undefined,
  level: undefined,
  role: 'EMPLOYEE' // 只查询员工
})

// 获取员工列表
const fetchEmployees = async () => {
  try {
    loading.value = true
    const response = await userApi.getUsers({
      ...searchForm,
      page: pagination.page,
      limit: pagination.limit
    })

    if (response.success && response.data) {
      employees.value = response.data.items
      pagination.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchEmployees()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    level: undefined,
    role: 'EMPLOYEE'
  })
  pagination.page = 1
  fetchEmployees()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchEmployees()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchEmployees()
}

// 选择处理
const handleSelectionChange = (selection: User[]) => {
  selectedEmployees.value = selection
}

// 新增员工
const handleCreate = () => {
  showCreateDialog.value = true
}

// 查看员工
const handleView = (employee: User) => {
  selectedEmployeeId.value = employee.id
  showDetailDialog.value = true
}

// 编辑员工
const handleEdit = (employee: User) => {
  selectedEmployee.value = employee
  showEditDialog.value = true
}

// 从详情页面编辑
const handleEditFromDetail = (employee: User) => {
  selectedEmployee.value = employee
  showEditDialog.value = true
}

// 创建成功回调
const handleCreateSuccess = () => {
  fetchEmployees()
}

// 编辑成功回调
const handleEditSuccess = () => {
  fetchEmployees()
}

// 状态操作
const handleStatusAction = async (command: string, employee: User) => {
  const statusMap = {
    activate: 'ACTIVE',
    deactivate: 'INACTIVE',
    ban: 'BANNED'
  }

  const actionMap = {
    activate: '激活',
    deactivate: '禁用',
    ban: '封禁'
  }

  try {
    await ElMessageBox.confirm(
      `确定要${actionMap[command]}员工 ${employee.nickname || employee.username} 吗？`,
      `确认${actionMap[command]}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await userApi.updateUser(employee.id, {
      status: statusMap[command] as UserStatus
    })

    if (response.success) {
      ElMessage.success(`${actionMap[command]}成功`)
      fetchEmployees()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${actionMap[command]}员工失败:`, error)
      ElMessage.error(`${actionMap[command]}员工失败`)
    }
  }
}

// 删除员工
const handleDelete = async (employee: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工 ${employee.nickname || employee.username} 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await userApi.deleteUser(employee.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchEmployees()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除员工失败:', error)
      ElMessage.error('删除员工失败')
    }
  }
}

// 批量激活
const handleBatchActivate = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要激活选中的 ${selectedEmployees.value.length} 个员工吗？`,
      '确认批量激活',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedEmployees.value.map(employee =>
      userApi.updateUser(employee.id, { status: 'ACTIVE' })
    )

    await Promise.all(promises)
    ElMessage.success('批量激活成功')
    fetchEmployees()
    selectedEmployees.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量激活失败:', error)
      ElMessage.error('批量激活失败')
    }
  }
}

// 批量禁用
const handleBatchDeactivate = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedEmployees.value.length} 个员工吗？`,
      '确认批量禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedEmployees.value.map(employee =>
      userApi.updateUser(employee.id, { status: 'INACTIVE' })
    )

    await Promise.all(promises)
    ElMessage.success('批量禁用成功')
    fetchEmployees()
    selectedEmployees.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedEmployees.value.length} 个员工吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedEmployees.value.map(employee =>
      userApi.deleteUser(employee.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    fetchEmployees()
    selectedEmployees.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 状态相关方法
const getStatusType = (status: UserStatus) => {
  const statusMap = {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    BANNED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: UserStatus) => {
  const statusMap = {
    ACTIVE: '激活',
    INACTIVE: '未激活',
    BANNED: '已封禁'
  }
  return statusMap[status] || status
}

const getLevelType = (level: number) => {
  if (level >= 4) return 'success'
  if (level >= 3) return 'primary'
  if (level >= 2) return 'warning'
  return 'info'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchEmployees()
})
</script>

<style lang="scss" scoped>
.employees-page {
  padding: var(--spacing-lg);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);

    .header-left {
      h2 {
        margin: 0 0 var(--spacing-xs) 0;
        color: var(--text-primary);
        font-size: var(--font-size-xxl);
        font-weight: var(--font-weight-bold);
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: var(--font-size-base);
      }
    }

    .header-right {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .search-card {
    margin-bottom: var(--spacing-lg);

    :deep(.el-card__body) {
      padding: var(--spacing-lg);
    }

    .el-form {
      margin-bottom: 0;
    }
  }

  .table-card {
    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md) 0;
      margin-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-lighter);

      .selected-info {
        color: var(--text-secondary);
        font-size: var(--font-size-base);
      }

      .batch-actions {
        display: flex;
        gap: var(--spacing-sm);
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: var(--spacing-lg);
      padding-top: var(--spacing-lg);
      border-top: 1px solid var(--border-lighter);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .employees-page {
    padding: var(--spacing-md);

    .page-header {
      flex-direction: column;
      gap: var(--spacing-md);

      .header-right {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .search-card {
      :deep(.el-form) {
        .el-form-item {
          margin-right: 0;
          margin-bottom: var(--spacing-sm);

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .table-card {
      .table-toolbar {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;

        .batch-actions {
          width: 100%;
          justify-content: flex-start;
        }
      }

      :deep(.el-table) {
        .el-table__body-wrapper {
          overflow-x: auto;
        }
      }
    }
  }
}

// 表格优化
:deep(.el-table) {
  .el-table__header {
    th {
      background-color: var(--bg-color);
      color: var(--text-primary);
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: var(--bg-page);
    }
  }
}

// 头像样式优化
:deep(.el-avatar) {
  background-color: var(--color-primary);
  color: white;
  font-weight: 600;
}

// 标签样式优化
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

// 下拉菜单样式
:deep(.el-dropdown) {
  .el-button {
    padding: 5px 8px;
  }
}

// 响应式操作列
@media (max-width: 1200px) {
  :deep(.action-column) {
    width: 200px !important;
    min-width: 200px !important;
  }
}

@media (max-width: 768px) {
  :deep(.action-column) {
    width: 120px !important;
    min-width: 120px !important;
  }

  .table-actions {
    flex-direction: column;
    gap: 2px;

    .table-action-btn {
      width: 100%;
      text-align: center;
      font-size: 10px;
      padding: 2px 4px;
    }

    .el-dropdown {
      width: 100%;

      .el-dropdown__trigger {
        width: 100%;
        text-align: center;
        font-size: 10px;
        padding: 2px 4px;
      }
    }
  }
}
</style>
