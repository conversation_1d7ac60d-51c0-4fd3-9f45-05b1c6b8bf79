import http from '@/api/http';

// 表单字段类型
export type FormFieldType = 'TEXT' | 'TEXTAREA' | 'SELECT' | 'CHECKBOX' | 'NUMBER' | 'PASSWORD' | 'IMAGE';

// 游戏表单字段接口
export interface GameFormField {
  id: string;
  gameId: string;
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  isRequired: boolean;
  placeholder?: string;
  sortOrder: number;
  options?: string[];
  config?: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  game?: {
    id: string;
    name: string;
    displayName: string;
  };
}

// 创建字段数据
export interface CreateGameFormFieldData {
  gameId: string;
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  isRequired?: boolean;
  placeholder?: string;
  sortOrder?: number;
  options?: string[];
  config?: Record<string, any>;
  isActive?: boolean;
}

// 更新字段数据
export interface UpdateGameFormFieldData {
  fieldKey?: string;
  fieldLabel?: string;
  fieldType?: FormFieldType;
  isRequired?: boolean;
  placeholder?: string;
  sortOrder?: number;
  options?: string[];
  config?: Record<string, any>;
  isActive?: boolean;
}

// 查询参数
export interface GameFormFieldQuery {
  gameId?: string;
  isActive?: boolean;
  fieldType?: FormFieldType;
  page?: number;
  limit?: number;
}

// 字段类型选项
export interface FieldTypeOption {
  value: FormFieldType;
  label: string;
  description: string;
}

// 字段排序数据
export interface FieldOrderData {
  id: string;
  sortOrder: number;
}

// 复制字段数据
export interface CopyFieldData {
  targetGameId: string;
  newFieldKey?: string;
}

// 验证配置数据
export interface ValidateConfigData {
  fieldType: FormFieldType;
  options?: string[];
  config?: Record<string, any>;
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// 字段使用情况
export interface FieldUsage {
  orderCount: number;
  usedInOrders: boolean;
}

/**
 * 获取字段类型选项
 */
export const getFieldTypes = async (): Promise<FieldTypeOption[]> => {
  const response = await http.get('/game-form-fields/field-types');
  return response.data;
};

/**
 * 验证字段配置
 */
export const validateFieldConfig = async (data: ValidateConfigData): Promise<ValidationResult> => {
  const response = await http.post('/game-form-fields/validate', data);
  return response.data;
};

/**
 * 根据游戏ID获取活跃字段
 */
export const getActiveFieldsByGameId = async (gameId: string): Promise<GameFormField[]> => {
  const response = await http.get(`/game-form-fields/game/${gameId}/active`);
  return response.data;
};

/**
 * 创建游戏表单字段
 */
export const createGameFormField = async (data: CreateGameFormFieldData): Promise<GameFormField> => {
  const response = await http.post('/game-form-fields', data);
  return response.data;
};

/**
 * 获取游戏表单字段列表
 */
export const getGameFormFields = async (params?: GameFormFieldQuery): Promise<{
  items: GameFormField[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> => {
  const response = await http.get('/game-form-fields', { params });
  return response.data;
};

/**
 * 获取字段详情
 */
export const getGameFormFieldById = async (id: string): Promise<GameFormField> => {
  const response = await http.get(`/game-form-fields/${id}`);
  return response.data;
};

/**
 * 更新字段
 */
export const updateGameFormField = async (id: string, data: UpdateGameFormFieldData): Promise<GameFormField> => {
  const response = await http.put(`/game-form-fields/${id}`, data);
  return response.data;
};

/**
 * 删除字段
 */
export const deleteGameFormField = async (id: string): Promise<void> => {
  await http.delete(`/game-form-fields/${id}`);
};

/**
 * 批量更新字段排序
 */
export const updateFieldsOrder = async (gameId: string, fieldOrders: FieldOrderData[]): Promise<GameFormField[]> => {
  const response = await http.put(`/game-form-fields/game/${gameId}/order`, { fieldOrders });
  return response.data;
};

/**
 * 复制字段到其他游戏
 */
export const copyFieldToGame = async (id: string, data: CopyFieldData): Promise<GameFormField> => {
  const response = await http.post(`/game-form-fields/${id}/copy`, data);
  return response.data;
};

/**
 * 获取字段使用情况
 */
export const getFieldUsage = async (id: string): Promise<FieldUsage> => {
  const response = await http.get(`/game-form-fields/${id}/usage`);
  return response.data;
};

// 字段类型映射
export const FIELD_TYPE_MAP: Record<FormFieldType, string> = {
  TEXT: '单行文本框',
  TEXTAREA: '多行文本框',
  SELECT: '下拉选择框',
  CHECKBOX: '多选框',
  NUMBER: '数字输入框',
  PASSWORD: '密码框',
  IMAGE: '图片上传控件'
};

// 获取字段类型标签
export const getFieldTypeLabel = (type: FormFieldType): string => {
  return FIELD_TYPE_MAP[type] || type;
};
