/**
 * 统一的组件样式
 */

// 按钮组件样式
.el-button {
  // 统一的按钮基础样式
  font-weight: 400;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  // 文本按钮特殊样式
  &.is-text {
    padding: 4px 8px;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  // 刷新按钮样式
  &.refresh-button {
    padding: 4px;
    color: #606266;
    border: none;
    background: transparent;
    
    &:hover {
      color: #409EFF;
      background-color: #f0f9ff;
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  // 表格操作按钮
  &.table-action-btn {
    padding: 4px 8px;
    margin: 0 2px;
    font-size: 12px;
    color: #606266;
    border-radius: 4px;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      color: #409EFF;
      background-color: #f0f9ff;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &.danger {
      color: #F56C6C;

      &:hover {
        color: #F56C6C;
        background-color: #fef0f0;
        transform: translateY(-1px);
      }
    }
  }
  
  // 对话框按钮
  &.dialog-btn {
    min-width: 80px;
  }
  
  // 页面头部按钮
  &.header-btn {
    margin-left: 12px;
    
    &:first-child {
      margin-left: 0;
    }
  }
}

// 按钮组样式
.button-group {
  display: flex;
  gap: 12px;
  
  &.button-group-right {
    justify-content: flex-end;
  }
  
  &.button-group-center {
    justify-content: center;
  }
  
  &.button-group-between {
    justify-content: space-between;
  }
}

// 对话框底部按钮样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  
  .el-button {
    min-width: 80px;
  }
}

// 页面头部样式
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    .page-description {
      color: #606266;
      margin: 0;
    }
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 卡片头部样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .card-actions {
    display: flex;
    gap: 8px;
  }
}

// 表格操作列样式
.table-actions {
  display: flex;
  gap: 8px;
  
  .el-button {
    padding: 2px 8px;
    font-size: 12px;
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 4px;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 状态按钮样式
.status-button {
  &.status-active {
    color: #67C23A;
    background-color: #f0f9ff;
    border-color: #67C23A;
  }
  
  &.status-inactive {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #909399;
  }
  
  &.status-warning {
    color: #E6A23C;
    background-color: #fdf6ec;
    border-color: #E6A23C;
  }
  
  &.status-danger {
    color: #F56C6C;
    background-color: #fef0f0;
    border-color: #F56C6C;
  }
}

// 响应式按钮样式
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    
    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }
  
  .button-group {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
    
    .el-button {
      width: 100%;
    }
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    
    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

// 加载状态样式
.loading-button {
  .el-loading-spinner {
    margin-right: 8px;
  }
}

// 图标按钮样式
.icon-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  
  .el-icon {
    font-size: 14px;
  }
  
  &.icon-only {
    padding: 8px;
    
    .el-icon {
      font-size: 16px;
    }
  }
}

// 按钮悬浮效果
.hover-button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// 禁用状态样式
.el-button.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;

  &:hover {
    transform: none;
    box-shadow: none;
  }
}

// 表格操作区域
.table-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
  z-index: 10;
  min-height: 32px;
  flex-wrap: nowrap;
  justify-content: flex-start;

  .el-dropdown {
    display: inline-flex;

    .el-dropdown__trigger {
      cursor: pointer;
    }
  }

  // 确保下拉菜单在正确的层级
  .el-dropdown-menu {
    z-index: 2000;
  }
}

// 响应式表格操作按钮
@media (max-width: 1200px) {
  .table-actions {
    gap: 2px;

    .table-action-btn {
      padding: 2px 6px;
      font-size: 11px;

      .el-icon {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .table-actions {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;

    .table-action-btn {
      width: 100%;
      text-align: center;
      padding: 4px 8px;
      font-size: 12px;
    }

    .el-dropdown {
      width: 100%;

      .el-dropdown__trigger {
        width: 100%;
        text-align: center;
      }
    }
  }

  // 移动端操作列宽度调整
  .el-table {
    .action-column {
      width: 100px !important;
      min-width: 100px !important;
    }
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }

  // 操作列样式
  .el-table__cell {
    &:last-child {
      .cell {
        padding-right: 8px;
      }
    }

    // 操作列特殊样式
    &.action-column {
      .cell {
        padding: 8px 12px;

        .table-actions {
          justify-content: flex-start;
          flex-wrap: nowrap;
          overflow: visible;
        }
      }
    }
  }
}

// 搜索筛选区域样式
.search-card {
  margin-bottom: 20px;

  .el-card__body {
    padding: 20px;
  }

  .el-form {
    &.el-form--inline {
      .el-form-item {
        margin-right: 16px;
        margin-bottom: 12px;

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
          width: auto;
          padding-right: 8px;
        }

        .el-form-item__content {
          .el-input {
            &.el-input--small {
              .el-input__inner {
                height: 32px;
                line-height: 32px;
              }
            }
          }

          .el-select {
            &.el-select--small {
              .el-input__inner {
                height: 32px;
                line-height: 32px;
              }
            }
          }
        }

        // 按钮组样式
        &:last-child {
          margin-right: 0;

          .el-button {
            margin-left: 8px;

            &:first-child {
              margin-left: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式筛选区域
@media (max-width: 768px) {
  .search-card {
    .el-form {
      &.el-form--inline {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;

          .el-form-item__label {
            display: block;
            margin-bottom: 4px;
            text-align: left;
          }

          .el-form-item__content {
            .el-input,
            .el-select {
              width: 100% !important;
            }
          }
        }
      }
    }
  }
}
