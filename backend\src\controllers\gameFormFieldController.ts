import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { gameFormFieldService } from '../services/gameFormFieldService';
import type { ApiResponse } from '../types/common';
import type { FormFieldType } from '@prisma/client';

/**
 * 创建游戏表单字段
 */
export const createGameFormField = asyncHandler(async (req: Request, res: Response) => {
  const field = await gameFormFieldService.createField(req.body);
  
  const response: ApiResponse = {
    success: true,
    data: field,
    message: '字段创建成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

/**
 * 获取游戏表单字段列表
 */
export const getGameFormFields = asyncHandler(async (req: Request, res: Response) => {
  const query = {
    gameId: req.query.gameId as string,
    isActive: req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined,
    fieldType: req.query.fieldType as FormFieldType,
    page: parseInt(req.query.page as string) || 1,
    limit: parseInt(req.query.limit as string) || 50,
  };
  
  const result = await gameFormFieldService.getFields(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取字段列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 根据游戏ID获取活跃的表单字段
 */
export const getActiveFieldsByGameId = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.params;
  
  const fields = await gameFormFieldService.getActiveFieldsByGameId(gameId);
  
  const response: ApiResponse = {
    success: true,
    data: fields,
    message: '获取游戏字段成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 获取字段详情
 */
export const getGameFormFieldById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const field = await gameFormFieldService.getFieldById(id);
  
  const response: ApiResponse = {
    success: true,
    data: field,
    message: '获取字段详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 更新字段
 */
export const updateGameFormField = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const field = await gameFormFieldService.updateField(id, req.body);
  
  const response: ApiResponse = {
    success: true,
    data: field,
    message: '字段更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 检查字段使用情况
 */
export const getFieldUsage = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const usage = await gameFormFieldService.getFieldUsageCount(id);

  const response: ApiResponse = {
    success: true,
    data: usage,
    message: '获取字段使用情况成功',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
});

/**
 * 删除字段
 */
export const deleteGameFormField = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  await gameFormFieldService.deleteField(id);

  const response: ApiResponse = {
    success: true,
    data: null,
    message: '字段删除成功',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
});

/**
 * 批量更新字段排序
 */
export const updateFieldsOrder = asyncHandler(async (req: Request, res: Response) => {
  const { gameId } = req.params;
  const { fieldOrders } = req.body;
  
  const fields = await gameFormFieldService.updateFieldsOrder(gameId, fieldOrders);
  
  const response: ApiResponse = {
    success: true,
    data: fields,
    message: '字段排序更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 复制字段到其他游戏
 */
export const copyFieldToGame = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { targetGameId, newFieldKey } = req.body;
  
  const field = await gameFormFieldService.copyFieldToGame(id, targetGameId, newFieldKey);
  
  const response: ApiResponse = {
    success: true,
    data: field,
    message: '字段复制成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 获取字段类型选项
 */
export const getFieldTypes = asyncHandler(async (req: Request, res: Response) => {
  const fieldTypes = [
    { value: 'TEXT', label: '单行文本框', description: '用于账号、昵称等短文本输入' },
    { value: 'TEXTAREA', label: '多行文本框', description: '用于备注、详细要求等长文本输入' },
    { value: 'SELECT', label: '下拉选择框', description: '用于单选的场景，如区服、段位' },
    { value: 'CHECKBOX', label: '多选框', description: '用于多选的场景，如代练项目' },
    { value: 'NUMBER', label: '数字输入框', description: '用于数量、小时数、价格等数字输入' },
    { value: 'PASSWORD', label: '密码框', description: '输入时显示为星号，用于密码输入' },
    { value: 'IMAGE', label: '图片上传控件', description: '用于上传截图凭证等图片文件' }
  ];
  
  const response: ApiResponse = {
    success: true,
    data: fieldTypes,
    message: '获取字段类型成功',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});

/**
 * 验证字段配置
 */
export const validateFieldConfig = asyncHandler(async (req: Request, res: Response) => {
  const { fieldType, options, config } = req.body;
  
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 验证字段类型
  const validFieldTypes = ['TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE'];
  if (!validFieldTypes.includes(fieldType)) {
    errors.push('无效的字段类型');
  }
  
  // 验证选项配置
  if (fieldType === 'SELECT' || fieldType === 'CHECKBOX') {
    if (!options || !Array.isArray(options) || options.length === 0) {
      errors.push('下拉选择框和多选框必须配置选项');
    } else if (options.some(option => !option || typeof option !== 'string')) {
      errors.push('选项必须是非空字符串');
    }
  }
  
  // 验证数字字段配置
  if (fieldType === 'NUMBER' && config) {
    if (config.min !== undefined && config.max !== undefined && config.min > config.max) {
      errors.push('最小值不能大于最大值');
    }
    if (config.step !== undefined && config.step <= 0) {
      errors.push('步长必须大于0');
    }
  }
  
  // 验证文本字段配置
  if ((fieldType === 'TEXT' || fieldType === 'TEXTAREA') && config) {
    if (config.minLength !== undefined && config.maxLength !== undefined && config.minLength > config.maxLength) {
      errors.push('最小长度不能大于最大长度');
    }
    if (config.maxLength !== undefined && config.maxLength > 1000) {
      warnings.push('建议最大长度不超过1000个字符');
    }
  }
  
  const response: ApiResponse = {
    success: true,
    data: {
      valid: errors.length === 0,
      errors,
      warnings
    },
    message: errors.length === 0 ? '配置验证通过' : '配置验证失败',
    timestamp: new Date().toISOString(),
  };
  
  res.json(response);
});
