### 技术栈规范

### 前端技术栈
框架: Vue.js 3 + TypeScript
UI组件库: Element Plus
状态管理: Pinia
路由: Vue Router 4
构建工具: Vite
CSS预处理器: Sass/SCSS
HTTP客户端: Axios
实时通信: Socket.IO Client

### 后端技术栈
运行环境: Node.js 18+
Web框架: Express.js
开发语言: TypeScript
ORM: Prisma
数据库: MySQL 8.0+
缓存: Redis 7+
实时通信: Socket.IO
身份认证: JWT + Passport.js
文件上传: Multer
任务队列: Bull Queue

### 开发工具与部署
版本控制: Git
代码规范: ESLint + Prettier
API文档: Swagger/OpenAPI
容器化: Docker + Docker Compose
反向代理: Nginx
进程管理: PM2
日志管理: Winston
监控: Prometheus + Grafana

### 数据库设计
主数据库: MySQL 8.0+（存储订单、用户、任务等核心数据）
缓存数据库: Redis（存储会话、实时状态、队列任务）
文件存储: 本地文件系统或阿里云OSS