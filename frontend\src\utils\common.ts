/**
 * 通用工具函数
 */

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  let previous = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func(...args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func(...args)
      }, remaining)
    }
  }
}

/**
 * 深拷贝函数
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 格式化错误消息
 * @param error 错误对象
 * @returns 格式化后的错误消息
 */
export const formatErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return error
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error?.message) {
    return error.message
  }
  
  return '操作失败，请稍后重试'
}

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
}

/**
 * 验证字段键名格式
 * @param fieldKey 字段键名
 * @returns 是否有效
 */
export const validateFieldKey = (fieldKey: string): boolean => {
  const pattern = /^[a-zA-Z][a-zA-Z0-9_]*$/
  return pattern.test(fieldKey)
}

/**
 * 字段类型配置常量
 */
export const FIELD_CONFIG = {
  MAX_SORT_ORDER: 999,
  MAX_FIELD_LABEL_LENGTH: 255,
  MAX_PLACEHOLDER_LENGTH: 255,
  DEFAULT_TEXTAREA_ROWS: 3,
  FIELD_KEY_PATTERN: /^[a-zA-Z][a-zA-Z0-9_]*$/,
  DEBOUNCE_DELAY: 500,
  THROTTLE_DELAY: 300
} as const

/**
 * 字段类型映射
 */
export const FIELD_TYPE_MAP = {
  TEXT: '单行文本框',
  TEXTAREA: '多行文本框',
  SELECT: '下拉选择框',
  CHECKBOX: '多选框',
  NUMBER: '数字输入框',
  PASSWORD: '密码框',
  IMAGE: '图片上传控件'
} as const

/**
 * 获取字段类型标签
 * @param type 字段类型
 * @returns 字段类型标签
 */
export const getFieldTypeLabel = (type: keyof typeof FIELD_TYPE_MAP): string => {
  return FIELD_TYPE_MAP[type] || type
}

/**
 * 获取字段类型标签样式
 * @param type 字段类型
 * @returns Element Plus 标签类型
 */
export const getFieldTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    TEXT: 'primary',
    TEXTAREA: 'success',
    SELECT: 'warning',
    CHECKBOX: 'info',
    NUMBER: 'danger',
    PASSWORD: '',
    IMAGE: 'success'
  }
  return typeMap[type] || ''
}

/**
 * 处理异步操作的通用函数
 * @param asyncFn 异步函数
 * @param loadingRef 加载状态引用
 * @param successMessage 成功消息
 * @param errorMessage 错误消息
 * @returns Promise
 */
export const handleAsyncOperation = async <T>(
  asyncFn: () => Promise<T>,
  loadingRef?: { value: boolean },
  successMessage?: string,
  errorMessage?: string
): Promise<T | null> => {
  try {
    if (loadingRef) loadingRef.value = true
    
    const result = await asyncFn()
    
    if (successMessage) {
      const { ElMessage } = await import('element-plus')
      ElMessage.success(successMessage)
    }
    
    return result
  } catch (error: any) {
    console.error('异步操作失败:', error)
    
    const { ElMessage } = await import('element-plus')
    const message = errorMessage || formatErrorMessage(error)
    ElMessage.error(message)
    
    return null
  } finally {
    if (loadingRef) loadingRef.value = false
  }
}

/**
 * 确认对话框的通用函数
 * @param message 确认消息
 * @param title 对话框标题
 * @param type 对话框类型
 * @returns Promise<boolean>
 */
export const showConfirmDialog = async (
  message: string,
  title: string = '确认操作',
  type: 'warning' | 'error' | 'info' | 'success' = 'warning'
): Promise<boolean> => {
  try {
    const { ElMessageBox } = await import('element-plus')
    await ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type
    })
    return true
  } catch {
    return false
  }
}
