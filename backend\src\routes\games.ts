import { Router } from 'express';
import {
  createGame,
  getGames,
  getActiveGames,
  getGameById,
  getGameWithFormFields,
  updateGame,
  deleteGame,
  getGameStatistics,
  getGameHotness,
  calculateOrderPrice,
  createGameRank,
  getGameRanks,
  getGameRanksByGameId,
  getGameRankById,
  updateGameRank,
  deleteGameRank,
  batchCreateGameRanks
} from '../controllers/gameController';
import { authenticateToken, requireBossOrAdmin, requireAdmin } from '../middleware/auth';
import { validate, validateQuery, validateParams, createFieldValidation } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 游戏查询参数验证schema
const gameQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().valid('name', 'displayName', 'sortOrder', 'createdAt').optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),
  isActive: Joi.boolean().optional()
});

// 游戏段位查询参数验证schema
const gameRankQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(50),
  sortBy: Joi.string().valid('level', 'name', 'createdAt').optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),
  gameId: Joi.string().optional(),
  isActive: Joi.boolean().optional(),
  minLevel: Joi.number().integer().min(1).optional(),
  maxLevel: Joi.number().integer().min(1).optional()
});

// 游戏创建验证schema - 使用统一的验证规则
const createGameSchema = Joi.object({
  name: createFieldValidation('name'),
  displayName: createFieldValidation('displayName'),
  description: createFieldValidation('description'),
  icon: createFieldValidation('icon'),
  isActive: Joi.boolean().optional().default(true),
  sortOrder: Joi.number().integer().min(0).optional().default(0),
  config: Joi.object().optional()
});

// 游戏更新验证schema - 使用统一的验证规则
const updateGameSchema = Joi.object({
  name: createFieldValidation('name').optional(),
  displayName: createFieldValidation('displayName').optional(),
  description: createFieldValidation('description'),
  icon: createFieldValidation('icon'),
  isActive: Joi.boolean().optional(),
  sortOrder: Joi.number().integer().min(0).optional(),
  config: Joi.object().optional()
});

// 游戏段位创建验证schema
const createGameRankSchema = Joi.object({
  gameId: Joi.string().required(),
  name: Joi.string().required().min(1).max(50),
  displayName: Joi.string().required().min(1).max(100),
  level: Joi.number().integer().min(1).required(),
  difficultyMultiplier: Joi.number().min(0.1).max(10).optional().default(1.0),
  icon: Joi.string().optional().uri(),
  description: Joi.string().optional().max(500),
  isActive: Joi.boolean().optional().default(true)
});

// 游戏段位更新验证schema
const updateGameRankSchema = Joi.object({
  name: Joi.string().optional().min(1).max(50),
  displayName: Joi.string().optional().min(1).max(100),
  level: Joi.number().integer().min(1).optional(),
  difficultyMultiplier: Joi.number().min(0.1).max(10).optional(),
  icon: Joi.string().optional().uri(),
  description: Joi.string().optional().max(500),
  isActive: Joi.boolean().optional()
});

// 价格计算验证schema
const calculatePriceSchema = Joi.object({
  gameId: Joi.string().required(),
  currentRankId: Joi.string().required(),
  targetRankId: Joi.string().required(),
  priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional().default('NORMAL')
});

// 批量创建段位验证schema
const batchCreateRanksSchema = Joi.object({
  ranks: Joi.array().items(
    Joi.object({
      name: Joi.string().required().min(1).max(50),
      displayName: Joi.string().required().min(1).max(100),
      level: Joi.number().integer().min(1).required(),
      difficultyMultiplier: Joi.number().min(0.1).max(10).optional().default(1.0),
      icon: Joi.string().optional().uri(),
      description: Joi.string().optional().max(500),
      isActive: Joi.boolean().optional().default(true)
    })
  ).min(1).required()
});

// ID参数验证schema
const idParamSchema = Joi.object({
  id: Joi.string().required()
});

const gameIdParamSchema = Joi.object({
  gameId: Joi.string().required()
});

// ==================== 游戏管理路由 ====================

// 获取活跃游戏简单列表（无需特殊权限）
router.get('/active', 
  authenticateToken,
  getActiveGames
);

// 获取游戏热度分析（需要老板或管理员权限）
router.get('/hotness',
  authenticateToken,
  requireBossOrAdmin,
  getGameHotness
);

// 价格计算（需要老板或管理员权限）
router.post('/calculate-price',
  authenticateToken,
  requireBossOrAdmin,
  validate(calculatePriceSchema),
  calculateOrderPrice
);

// 创建游戏（需要老板或管理员权限）
router.post('/',
  authenticateToken,
  requireBossOrAdmin,
  validate(createGameSchema),
  createGame
);

// 获取游戏列表
router.get('/',
  authenticateToken,
  validateQuery(gameQuerySchema),
  getGames
);

// 获取游戏详情
router.get('/:id',
  authenticateToken,
  validateParams(idParamSchema),
  getGameById
);

// 获取游戏及其表单字段
router.get('/:id/form-fields',
  authenticateToken,
  validateParams(idParamSchema),
  getGameWithFormFields
);

// 获取游戏统计信息（需要老板或管理员权限）
router.get('/:id/statistics',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  getGameStatistics
);

// 更新游戏（需要老板或管理员权限）
router.put('/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  validate(updateGameSchema),
  updateGame
);

// 删除游戏（需要老板或管理员权限）
router.delete('/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  deleteGame
);

// ==================== 游戏段位管理路由 ====================

// 获取指定游戏的段位列表
router.get('/:gameId/ranks',
  authenticateToken,
  validateParams(gameIdParamSchema),
  getGameRanksByGameId
);

// 批量创建段位（需要老板或管理员权限）
router.post('/:gameId/ranks/batch',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(gameIdParamSchema),
  validate(batchCreateRanksSchema),
  batchCreateGameRanks
);

// 创建游戏段位（需要老板或管理员权限）
router.post('/ranks',
  authenticateToken,
  requireBossOrAdmin,
  validate(createGameRankSchema),
  createGameRank
);

// 获取游戏段位列表
router.get('/ranks',
  authenticateToken,
  validateQuery(gameRankQuerySchema),
  getGameRanks
);

// 获取段位详情
router.get('/ranks/:id',
  authenticateToken,
  validateParams(idParamSchema),
  getGameRankById
);

// 更新段位（需要老板或管理员权限）
router.put('/ranks/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  validate(updateGameRankSchema),
  updateGameRank
);

// 删除段位（需要老板或管理员权限）
router.delete('/ranks/:id',
  authenticateToken,
  requireBossOrAdmin,
  validateParams(idParamSchema),
  deleteGameRank
);

export default router;
