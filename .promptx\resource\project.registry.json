{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T12:31:48.546Z", "updatedAt": "2025-08-04T12:31:48.552Z", "resourceCount": 3}, "resources": [{"id": "design-management-workflow", "source": "project", "protocol": "execution", "name": "Design Management Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-design-manager/execution/design-management-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T12:31:48.549Z", "updatedAt": "2025-08-04T12:31:48.549Z", "scannedAt": "2025-08-04T12:31:48.549Z", "path": "role/ui-design-manager/execution/design-management-workflow.execution.md"}}, {"id": "design-leadership", "source": "project", "protocol": "thought", "name": "Design Leadership 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-design-manager/thought/design-leadership.thought.md", "metadata": {"createdAt": "2025-08-04T12:31:48.550Z", "updatedAt": "2025-08-04T12:31:48.550Z", "scannedAt": "2025-08-04T12:31:48.550Z", "path": "role/ui-design-manager/thought/design-leadership.thought.md"}}, {"id": "ui-design-manager", "source": "project", "protocol": "role", "name": "Ui Design Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-design-manager/ui-design-manager.role.md", "metadata": {"createdAt": "2025-08-04T12:31:48.551Z", "updatedAt": "2025-08-04T12:31:48.551Z", "scannedAt": "2025-08-04T12:31:48.551Z", "path": "role/ui-design-manager/ui-design-manager.role.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "thought": 1, "role": 1}, "bySource": {"project": 3}}}