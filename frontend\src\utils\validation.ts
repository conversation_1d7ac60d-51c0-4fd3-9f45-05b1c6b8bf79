import type { FormRules, FormItemRule } from 'element-plus'

// 常用验证规则
export const validationRules = {
  // 必填验证
  required: (message: string = '此项为必填项'): FormItemRule => ({
    required: true,
    message,
    trigger: 'blur'
  }),



  // 手机号验证
  phone: (message: string = '请输入正确的手机号'): FormItemRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message,
    trigger: 'blur'
  }),

  // 密码验证
  password: (minLength: number = 6, message?: string): FormItemRule => ({
    min: minLength,
    message: message || `密码长度不能少于${minLength}位`,
    trigger: 'blur'
  }),

  // 用户名验证
  username: (message: string = '用户名只能包含字母、数字和下划线，长度3-20位'): FormItemRule => ({
    pattern: /^[a-zA-Z0-9_]{3,20}$/,
    message,
    trigger: 'blur'
  }),

  // 数字验证
  number: (min?: number, max?: number, message?: string): FormItemRule => {
    const rule: FormItemRule = {
      type: 'number',
      trigger: 'blur'
    }
    
    if (min !== undefined) {
      rule.min = min
    }
    
    if (max !== undefined) {
      rule.max = max
    }
    
    if (message) {
      rule.message = message
    } else {
      if (min !== undefined && max !== undefined) {
        rule.message = `请输入${min}-${max}之间的数字`
      } else if (min !== undefined) {
        rule.message = `请输入大于等于${min}的数字`
      } else if (max !== undefined) {
        rule.message = `请输入小于等于${max}的数字`
      } else {
        rule.message = '请输入有效的数字'
      }
    }
    
    return rule
  },

  // 长度验证
  length: (min?: number, max?: number, message?: string): FormItemRule => {
    const rule: FormItemRule = {
      trigger: 'blur'
    }
    
    if (min !== undefined) {
      rule.min = min
    }
    
    if (max !== undefined) {
      rule.max = max
    }
    
    if (message) {
      rule.message = message
    } else {
      if (min !== undefined && max !== undefined) {
        rule.message = `长度应在${min}-${max}个字符之间`
      } else if (min !== undefined) {
        rule.message = `长度不能少于${min}个字符`
      } else if (max !== undefined) {
        rule.message = `长度不能超过${max}个字符`
      }
    }
    
    return rule
  },

  // 自定义正则验证
  pattern: (pattern: RegExp, message: string): FormItemRule => ({
    pattern,
    message,
    trigger: 'blur'
  }),

  // 自定义验证函数
  custom: (validator: (rule: any, value: any, callback: any) => void): FormItemRule => ({
    validator,
    trigger: 'blur'
  }),

  // 确认密码验证
  confirmPassword: (passwordField: string, message: string = '两次输入的密码不一致'): FormItemRule => ({
    validator: (rule: any, value: any, callback: any) => {
      const form = rule.form
      if (value && value !== form[passwordField]) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  })
}

// 常用表单规则组合
export const commonFormRules = {
  // 登录表单
  login: (): FormRules => ({
    username: [
      validationRules.required('请输入用户名'),
      validationRules.username()
    ],
    password: [
      validationRules.required('请输入密码'),
      validationRules.password()
    ]
  }),

  // 注册表单
  register: (): FormRules => ({
    username: [
      validationRules.required('请输入用户名'),
      validationRules.username()
    ],
    password: [
      validationRules.required('请输入密码'),
      validationRules.password()
    ],
    confirmPassword: [
      validationRules.required('请确认密码'),
      validationRules.confirmPassword('password')
    ],
    phone: [
      validationRules.phone()
    ]
  }),

  // 订单表单
  order: (): FormRules => ({
    customerName: [
      validationRules.required('请输入客户姓名'),
      validationRules.length(1, 50)
    ],
    gameAccount: [
      validationRules.required('请输入游戏账号'),
      validationRules.length(1, 50)
    ],
    gamePassword: [
      validationRules.required('请输入游戏密码'),
      validationRules.length(1, 50)
    ],
    currentRank: [
      validationRules.required('请选择当前段位')
    ],
    targetRank: [
      validationRules.required('请选择目标段位')
    ],
    price: [
      validationRules.required('请输入价格'),
      validationRules.number(0.01, undefined, '价格必须大于0')
    ]
  }),

  // 任务表单
  task: (): FormRules => ({
    orderId: [
      validationRules.required('请选择订单')
    ],
    estimatedHours: [
      validationRules.required('请输入预计工时'),
      validationRules.number(0.1, 1000, '工时必须在0.1-1000小时之间')
    ],
    commission: [
      validationRules.required('请输入佣金'),
      validationRules.number(0.01, undefined, '佣金必须大于0')
    ]
  })
}

// 表单验证辅助函数
export const validateForm = async (formRef: any): Promise<boolean> => {
  if (!formRef) return false
  
  try {
    await formRef.validate()
    return true
  } catch (error) {
    console.warn('表单验证失败:', error)
    return false
  }
}

// 重置表单
export const resetForm = (formRef: any) => {
  if (formRef) {
    formRef.resetFields()
  }
}

// 清除表单验证
export const clearValidation = (formRef: any) => {
  if (formRef) {
    formRef.clearValidate()
  }
}
