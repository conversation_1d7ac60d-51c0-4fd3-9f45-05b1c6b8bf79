<template>
  <transition name="error-slide">
    <div v-if="hasError" class="global-error">
      <div class="error-content">
        <div class="error-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="error-message">
          <div class="error-title">操作失败</div>
          <div class="error-detail">{{ globalError }}</div>
        </div>
        <div class="error-actions">
          <el-button size="small" @click="handleRetry" v-if="showRetry">
            重试
          </el-button>
          <el-button size="small" @click="handleClose">
            关闭
          </el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'

interface Props {
  showRetry?: boolean
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  showRetry: false
})

const emit = defineEmits<{
  retry: []
}>()

const appStore = useAppStore()
const { globalError, hasError } = storeToRefs(appStore)

const handleClose = () => {
  appStore.clearError()
}

const handleRetry = () => {
  appStore.clearError()
  if (props.onRetry) {
    props.onRetry()
  } else {
    emit('retry')
  }
}
</script>

<style lang="scss" scoped>
.global-error {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9998;
  max-width: 400px;

  .error-content {
    display: flex;
    align-items: flex-start;
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .error-icon {
      margin-right: 12px;
      color: #f56c6c;
      font-size: 20px;
      flex-shrink: 0;
      margin-top: 2px;
    }

    .error-message {
      flex: 1;
      margin-right: 12px;

      .error-title {
        font-weight: 600;
        color: #f56c6c;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .error-detail {
        color: #909399;
        font-size: 13px;
        line-height: 1.4;
        word-break: break-word;
      }
    }

    .error-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex-shrink: 0;

      .el-button {
        margin: 0;
      }
    }
  }
}

.error-slide-enter-active,
.error-slide-leave-active {
  transition: all 0.3s ease;
}

.error-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.error-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>
