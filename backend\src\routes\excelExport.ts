import { Router } from 'express';
import {
  exportMonthlySettlement,
  exportEmployeeEarnings,
  exportOrderSummary,
  exportCustomReport
} from '../controllers/excelExportController';
import { authenticateToken, requireBossOrAdmin, requireEmployee } from '../middleware/auth';
import { validate, validateQuery, validateParams } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 验证模式
const monthlySettlementQuerySchema = Joi.object({
  year: Joi.number().integer().min(2020).max(2030).required(),
  month: Joi.number().integer().min(1).max(12).required()
});

const dateRangeQuerySchema = Joi.object({
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional()
});

const employeeIdParamSchema = Joi.object({
  employeeId: Joi.string().required()
});

const customReportSchema = Joi.object({
  reportType: Joi.string().valid('monthly_settlement', 'order_summary', 'employee_earnings').required(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  filters: Joi.object().optional()
});

// ==================== Excel导出路由 ====================

/**
 * 导出月度结算报表
 * GET /api/v1/excel/monthly-settlement?year=2024&month=1
 * 权限：老板和管理员
 */
router.get('/monthly-settlement',
  authenticateToken,
  requireBossOrAdmin,
  validateQuery(monthlySettlementQuerySchema),
  exportMonthlySettlement
);

/**
 * 导出员工个人收益明细
 * GET /api/v1/excel/employee-earnings/:employeeId?startDate=2024-01-01&endDate=2024-01-31
 * 权限：员工只能查看自己的，老板和管理员可以查看所有
 */
router.get('/employee-earnings/:employeeId',
  authenticateToken,
  requireEmployee, // 至少需要员工权限
  validateParams(employeeIdParamSchema),
  validateQuery(dateRangeQuerySchema),
  exportEmployeeEarnings
);

/**
 * 导出订单完成情况汇总
 * GET /api/v1/excel/order-summary?startDate=2024-01-01&endDate=2024-01-31
 * 权限：老板和管理员
 */
router.get('/order-summary',
  authenticateToken,
  requireBossOrAdmin,
  validateQuery(dateRangeQuerySchema),
  exportOrderSummary
);

/**
 * 导出自定义报表
 * POST /api/v1/excel/custom-report
 * 权限：老板和管理员
 */
router.post('/custom-report',
  authenticateToken,
  requireBossOrAdmin,
  validate(customReportSchema),
  exportCustomReport
);

/**
 * 获取可用的导出选项
 * GET /api/v1/excel/export-options
 * 权限：所有认证用户
 */
router.get('/export-options', authenticateToken, (req, res) => {
  const userRole = req.user?.role;
  
  const options = {
    monthly_settlement: {
      name: '月度结算报表',
      description: '导出指定月份的员工结算明细',
      permissions: ['BOSS', 'ADMIN'],
      parameters: [
        { name: 'year', type: 'number', required: true, description: '年份' },
        { name: 'month', type: 'number', required: true, description: '月份' }
      ]
    },
    order_summary: {
      name: '订单完成情况汇总',
      description: '导出指定时间范围内的订单完成情况',
      permissions: ['BOSS', 'ADMIN'],
      parameters: [
        { name: 'startDate', type: 'date', required: false, description: '开始日期' },
        { name: 'endDate', type: 'date', required: false, description: '结束日期' }
      ]
    },
    employee_earnings: {
      name: '员工个人收益明细',
      description: '导出员工的个人收益明细',
      permissions: ['EMPLOYEE', 'BOSS', 'ADMIN'],
      parameters: [
        { name: 'employeeId', type: 'string', required: true, description: '员工ID' },
        { name: 'startDate', type: 'date', required: false, description: '开始日期' },
        { name: 'endDate', type: 'date', required: false, description: '结束日期' }
      ]
    }
  };
  
  // 根据用户角色过滤可用选项
  const availableOptions = Object.entries(options).reduce((acc, [key, option]) => {
    if (userRole && option.permissions.includes(userRole)) {
      acc[key] = option;
    }
    return acc;
  }, {} as any);
  
  res.json({
    success: true,
    data: availableOptions,
    timestamp: new Date().toISOString()
  });
});

export default router;
