import { UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../config/database';
import { NotFoundError, ConflictError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';

export class UserService {
  // 获取用户列表
  async getUsers(query: PaginationQuery & { role?: UserRole; status?: UserStatus; keyword?: string }) {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', role, status, keyword } = query;
    
    const skip = (page - 1) * limit;
    
    // 构建查询条件
    const where: any = {};
    
    if (role) {
      where.role = role;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (keyword) {
      where.OR = [
        { username: { contains: keyword } },
        { email: { contains: keyword } },
        { nickname: { contains: keyword } },
      ];
    }
    
    // 查询用户
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          username: true,
          nickname: true,
          role: true,
          status: true,
          level: true,
          totalEarnings: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              assignedTasks: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.user.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / limit);
    
    const result: PaginatedResponse<typeof users[0]> = {
      items: users,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
    
    return result;
  }

  // 获取用户详情
  async getUserById(id: string) {
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        nickname: true,
        phone: true,
        role: true,
        status: true,
        level: true,
        totalEarnings: true,
        createdAt: true,
        updatedAt: true,
        assignedTasks: {
          select: {
            id: true,
            taskNo: true,
            status: true,
            commission: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    return user;
  }

  // 更新用户信息
  async updateUser(id: string, updateData: {
    nickname?: string;
    phone?: string;
    level?: number;
    status?: UserStatus;
  }) {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new NotFoundError('用户不存在');
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        username: true,
        nickname: true,
        phone: true,
        role: true,
        status: true,
        level: true,
        totalEarnings: true,
        updatedAt: true,
      },
    });

    return updatedUser;
  }

  // 删除用户
  async deleteUser(id: string) {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: {
        assignedTasks: {
          where: {
            status: {
              in: ['ACCEPTED', 'IN_PROGRESS'],
            },
          },
        },
      },
    });

    if (!existingUser) {
      throw new NotFoundError('用户不存在');
    }

    // 检查是否有进行中的任务
    if (existingUser.assignedTasks.length > 0) {
      throw new ConflictError('用户有进行中的任务，无法删除');
    }

    // 删除用户
    await prisma.user.delete({
      where: { id },
    });

    return { message: '用户删除成功' };
  }

  // 获取员工统计信息
  async getEmployeeStats(employeeId: string) {
    const stats = await prisma.user.findUnique({
      where: { id: employeeId, role: UserRole.EMPLOYEE },
      select: {
        id: true,
        username: true,
        nickname: true,
        level: true,
        totalEarnings: true,
        assignedTasks: {
          select: {
            status: true,
            commission: true,
            actualHours: true,
          },
        },
      },
    });

    if (!stats) {
      throw new NotFoundError('员工不存在');
    }

    const tasks = stats.assignedTasks;
    const completedTasks = tasks.filter(task => task.status === 'COMPLETED');
    const totalTasks = tasks.length;
    const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
    const totalHours = completedTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);
    const avgHoursPerTask = completedTasks.length > 0 ? totalHours / completedTasks.length : 0;

    return {
      user: {
        id: stats.id,
        username: stats.username,
        nickname: stats.nickname,
        level: stats.level,
        totalEarnings: stats.totalEarnings,
      },
      statistics: {
        totalTasks,
        completedTasks: completedTasks.length,
        completionRate: Math.round(completionRate * 100) / 100,
        totalHours,
        avgHoursPerTask: Math.round(avgHoursPerTask * 100) / 100,
      },
    };
  }

  // 获取当前用户的统计信息（根据角色返回不同数据）
  async getMyStats(userId: string, userRole: UserRole) {
    // 获取用户基本信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        nickname: true,
        role: true,
        level: true,
        totalEarnings: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 根据角色返回不同的统计数据
    if (userRole === UserRole.EMPLOYEE) {
      // 员工统计：任务相关数据
      const tasks = await prisma.task.findMany({
        where: { assigneeId: userId },
        select: {
          status: true,
          commission: true,
          actualHours: true,
          createdAt: true,
          endTime: true, // 添加endTime字段
        },
      });

      // 修复：统计APPROVED和COMPLETED状态的任务
      const completedTasks = tasks.filter(task =>
        task.status === 'APPROVED' || task.status === 'COMPLETED'
      );
      const totalTasks = tasks.length;
      const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
      const totalHours = completedTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);

      // 本月统计 - 修复：基于任务完成时间而非创建时间
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      // 获取本月完成的任务（基于endTime）
      const currentMonthCompleted = tasks.filter(task => {
        if (!task.endTime) return false; // 没有完成时间的任务不计入
        const endTime = new Date(task.endTime);
        return endTime >= currentMonth &&
               (task.status === 'APPROVED' || task.status === 'COMPLETED');
      });

      const currentMonthEarnings = currentMonthCompleted.reduce((sum, task) =>
        sum + (task.commission || 0), 0
      );

      return {
        user: {
          id: user.id,
          username: user.username,
          nickname: user.nickname,
          role: user.role,
          level: user.level,
          totalEarnings: user.totalEarnings,
        },
        completedTasks: completedTasks.length,
        totalEarnings: user.totalEarnings || 0,
        currentMonthTasks: currentMonthCompleted.length,
        currentMonthEarnings,
        totalTasks,
        completionRate: Math.round(completionRate * 100) / 100,
        totalHours,
      };
    } else {
      // 老板/管理员统计：业务概览数据
      const [totalOrders, totalTasks, totalEmployees, totalRevenue] = await Promise.all([
        // 总订单数
        prisma.order.count(),
        // 总任务数
        prisma.task.count(),
        // 员工数量
        prisma.user.count({
          where: { role: UserRole.EMPLOYEE }
        }),
        // 总营收（所有已完成订单的价格总和）
        prisma.order.aggregate({
          where: { status: 'COMPLETED' },
          _sum: { price: true }
        })
      ]);

      // 本月数据
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const [currentMonthOrders, currentMonthRevenue] = await Promise.all([
        prisma.order.count({
          where: {
            createdAt: { gte: currentMonth }
          }
        }),
        prisma.order.aggregate({
          where: {
            status: 'COMPLETED',
            createdAt: { gte: currentMonth }
          },
          _sum: { price: true }
        })
      ]);

      return {
        user: {
          id: user.id,
          username: user.username,
          nickname: user.nickname,
          role: user.role,
          level: user.level,
        },
        totalOrders,
        totalTasks,
        totalEmployees,
        totalRevenue: totalRevenue._sum.price || 0,
        currentMonthOrders,
        currentMonthRevenue: currentMonthRevenue._sum.price || 0,
      };
    }
  }

  // 更新用户收益
  async updateUserEarnings(userId: string, amount: number) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        totalEarnings: {
          increment: amount,
        },
      },
    });
  }
}
