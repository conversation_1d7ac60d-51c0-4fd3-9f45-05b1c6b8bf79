<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>欢迎来到王者荣耀代练任务分发管理系统</h1>
      <p>您已成功登录系统</p>
    </div>

    <div class="dashboard-content">
      <div class="welcome-card">
        <h2>系统概览</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">0</div>
            <div class="stat-label">待处理任务</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">0</div>
            <div class="stat-label">进行中任务</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">0</div>
            <div class="stat-label">已完成任务</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥0</div>
            <div class="stat-label">总收益</div>
          </div>
        </div>
      </div>

      <div class="actions-card">
        <h3>快速操作</h3>
        <div class="action-buttons">
          <el-button type="primary" size="large">
            创建新任务
          </el-button>
          <el-button type="success" size="large">
            查看任务列表
          </el-button>
          <el-button type="info" size="large" @click="logout">
            退出登录
          </el-button>
        </div>
      </div>
    </div></div></template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
const router = useRouter()

const logout = () => {
  // 清除本地存储
  localStorage.removeItem('token')
  localStorage.removeItem('user')

  ElMessage.success('已退出登录')
  router.push('/login')
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: var(--spacing-lg);
}

.dashboard-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
  }

  p {
    color: var(--text-color-secondary);
    font-size: 1.2rem;
  }
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: var(--spacing-xl);
}

.welcome-card,
.actions-card {
  background: var(--bg-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-light);

  h2, h3 {
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-lg);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: var(--border-radius-medium);
  color: white;

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
  }

  .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
  }
}

.action-buttons {
  @include flex(row, center, center);
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

@include respond-below(md) {
  .dashboard-header h1 {
    font-size: 2rem;
  }

  .action-buttons {
    @include flex(column, center, center);
  }
}
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
}

.header-left h1 {
  margin: 0;
  color: #303133;
}

.dashboard-main {
  padding: 24px;
}

.welcome-section {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-card {
  text-align: center;
  padding: 48px 24px;
  
  h2 {
    margin-bottom: 16px;
    color: #303133;
  }
  
  p {
    margin-bottom: 32px;
    color: #606266;
    font-size: 16px;
  }
}

.mode-selection {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

.mode-card {
  width: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
  
  .mode-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h3 {
    margin-bottom: 8px;
    color: #303133;
  }
  
  p {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }
}

.boss-mode:hover {
  border-color: #409eff;
}

.employee-mode:hover {
  border-color: #67c23a;
}
</style>
