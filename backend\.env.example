# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/game_boost_db"

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Socket.IO配置
SOCKET_CORS_ORIGIN=http://localhost:5173

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=

# 其他配置
API_PREFIX=/api/v1
CORS_ORIGIN=http://localhost:5173
