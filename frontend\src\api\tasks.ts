import { request } from './http'
import type {
  Task,
  TaskForm,
  PaginatedResponse,
  PaginationQuery,
  ApiResponse,
  TaskStatus,
  AssignType,
  CommissionCalculationParams,
  CommissionCalculationResult
} from '@/types'

export interface TaskQuery extends PaginationQuery {
  status?: TaskStatus
  assignType?: AssignType
  assigneeId?: string
  orderId?: string
  keyword?: string
}

export interface TaskProgressData {
  progress: number
  description?: string
  screenshots?: string[]
}

export interface TaskReviewData {
  approved: boolean
  feedback?: string
}

export interface TaskStats {
  totalTasks: number
  totalCommission: number
  avgHours: number
  statusBreakdown: Record<string, number>
}

export const taskApi = {
  // 创建任务
  createTask(data: TaskForm): Promise<ApiResponse<Task>> {
    return request.post('/tasks', data)
  },

  // 获取任务列表
  getTasks(params?: TaskQuery): Promise<ApiResponse<PaginatedResponse<Task>>> {
    return request.get('/tasks', { params })
  },

  // 获取可接单任务
  getAvailableTasks(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Task>>> {
    return request.get('/tasks/available', { params })
  },

  // 获取我的任务
  getMyTasks(params?: TaskQuery): Promise<ApiResponse<PaginatedResponse<Task>>> {
    return request.get('/tasks/my', { params })
  },

  // 获取任务详情
  getTaskById(id: string): Promise<ApiResponse<Task>> {
    return request.get(`/tasks/${id}`)
  },

  // 更新任务
  updateTask(id: string, data: Partial<TaskForm>): Promise<ApiResponse<Task>> {
    return request.put(`/tasks/${id}`, data)
  },

  // 接单
  acceptTask(id: string): Promise<ApiResponse<Task>> {
    return request.post(`/tasks/${id}/accept`)
  },

  // 更新任务进度
  updateTaskProgress(id: string, data: TaskProgressData): Promise<ApiResponse<any>> {
    return request.post(`/tasks/${id}/progress`, data)
  },

  // 员工更新任务状态（开始/暂停/提交）
  updateTaskStatus(id: string, status: TaskStatus): Promise<ApiResponse<Task>> {
    return request.patch(`/tasks/${id}/status`, { status })
  },

  // 审核任务
  reviewTask(id: string, data: TaskReviewData): Promise<ApiResponse<Task>> {
    return request.post(`/tasks/${id}/review`, data)
  },

  // 取消任务
  cancelTask(id: string): Promise<ApiResponse<Task>> {
    return request.post(`/tasks/${id}/cancel`)
  },

  // 获取任务统计
  getTaskStats(): Promise<ApiResponse<TaskStats>> {
    return request.get('/tasks/stats')
  },

  // 计算佣金
  calculateCommission(params: CommissionCalculationParams): Promise<ApiResponse<CommissionCalculationResult>> {
    return request.post('/tasks/calculate-commission', params)
  },


}
