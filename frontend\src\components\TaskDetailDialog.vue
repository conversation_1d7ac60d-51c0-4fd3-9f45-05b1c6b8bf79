<template>
  <el-dialog
    v-model="visible"
    title="任务详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="task" class="task-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(task.status)">
              {{ getStatusText(task.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务号">{{ task.taskNo }}</el-descriptions-item>
          <el-descriptions-item label="分配方式">
            <el-tag :type="task.assignType === 'DIRECT' ? 'success' : 'info'" size="small">
              {{ task.assignType === 'DIRECT' ? '直接分配' : '系统挂单' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(task.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ task.startTime ? formatDate(task.startTime) : '未开始' }}
          </el-descriptions-item>
          <el-descriptions-item label="预计工时">
            {{ task.estimatedHours ? `${task.estimatedHours}小时` : '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="佣金">
            {{ task.commission ? `¥${task.commission}` : '未设置' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="task.description" class="task-description">
          <h4>任务描述</h4>
          <p>{{ task.description }}</p>
        </div>
        
        <div v-if="task.notes" class="task-notes">
          <h4>备注</h4>
          <p>{{ task.notes }}</p>
        </div>
      </el-card>

      <!-- 订单信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span>订单信息</span>
        </template>
        
        <el-descriptions :column="2" border v-if="task.order">
          <el-descriptions-item label="订单号">{{ task.order.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ task.order.customerName }}</el-descriptions-item>
          <el-descriptions-item label="游戏类型" v-if="task.order.gameType">
            <el-tag type="primary" size="small">
              {{ getGameTypeName(task.order.gameType) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="订单价格">¥{{ task.order.price }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(task.order.priority)" size="small">
              {{ getPriorityText(task.order.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="截止时间" v-if="task.order.deadline">
            {{ formatDate(task.order.deadline) }}
          </el-descriptions-item>
          <el-descriptions-item label="特殊要求" v-if="task.order.requirements">
            {{ task.order.requirements }}
          </el-descriptions-item>

          <!-- 游戏账号密码信息 - 只有员工接单且状态合适时才显示 -->
          <template v-if="shouldShowGameCredentials">
            <el-descriptions-item label="游戏账号" v-if="task.order.gameAccount">
              <div class="credential-item">
                <span class="credential-value">{{ task.order.gameAccount }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="copyToClipboard(task.order.gameAccount, '游戏账号')"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="游戏密码" v-if="task.order.gamePassword">
              <div class="credential-item">
                <span class="credential-value" :class="{ 'password-hidden': !showPassword }">
                  {{ showPassword ? task.order.gamePassword : '••••••••' }}
                </span>
                <el-button
                  type="text"
                  size="small"
                  @click="togglePasswordVisibility"
                  style="margin-left: 8px;"
                >
                  <el-icon><View v-if="!showPassword" /><Hide v-else /></el-icon>
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="copyToClipboard(task.order.gamePassword, '游戏密码')"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-card>

      <!-- 员工信息 -->
      <el-card class="info-card" shadow="never" v-if="task.assignee">
        <template #header>
          <span>分配员工</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工姓名">
            {{ task.assignee.nickname || task.assignee.username }}
          </el-descriptions-item>
          <el-descriptions-item label="员工等级">{{ task.assignee.level }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 进度记录 -->
      <el-card class="info-card" shadow="never" v-if="task.progress && task.progress.length > 0">
        <template #header>
          <span>进度记录</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="progress in task.progress"
            :key="progress.id"
            :timestamp="formatDate(progress.createdAt)"
            placement="top"
          >
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-value">进度: {{ progress.progress }}%</span>
              </div>
              <div v-if="progress.description" class="progress-description">
                {{ progress.description }}
              </div>
              <div v-if="getScreenshots(progress.screenshots).length > 0" class="progress-screenshots">
                <h5>截图:</h5>
                <div class="screenshot-grid">
                  <el-image
                    v-for="(screenshot, index) in getScreenshots(progress.screenshots)"
                    :key="index"
                    :src="screenshot"
                    :preview-src-list="getScreenshots(progress.screenshots)"
                    :initial-index="index"
                    fit="cover"
                    class="screenshot-thumb"
                  />
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument, View, Hide } from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import type { Task, OrderPriority } from '@/types'
import { TaskStatus } from '@/types'
import { formatDate } from '@/utils/date'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue: boolean
  taskId: string | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const task = ref<Task | null>(null)
const loading = ref(false)
const showPassword = ref(false)

// 获取当前用户信息
const authStore = useAuthStore()

// 计算属性：是否应该显示游戏账号密码
const shouldShowGameCredentials = computed(() => {
  if (!task.value || !authStore.user) return false

  // 只有员工角色才能看到游戏账号密码
  if (authStore.user.role !== 'EMPLOYEE') return false

  // 只有分配给当前员工的任务才能看到
  if (task.value.assigneeId !== authStore.user.id) return false

  // 只有在开始任务后才能看到账号密码（进行中、已暂停、已提交状态）
  const allowedStatuses = [TaskStatus.IN_PROGRESS, TaskStatus.PAUSED, TaskStatus.SUBMITTED]
  return allowedStatuses.includes(task.value.status)
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.taskId) {
    fetchTaskDetail()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!props.taskId) return
  
  try {
    loading.value = true
    const response = await taskApi.getTaskById(props.taskId)
    
    if (response.success && response.data) {
      task.value = response.data
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusType = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    PENDING: 'warning',
    ACCEPTED: 'info',
    IN_PROGRESS: 'primary',
    PAUSED: 'info',
    SUBMITTED: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    COMPLETED: 'success',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: TaskStatus) => {
  const statusMap = {
    PENDING: '待处理',
    ACCEPTED: '已接受',
    IN_PROGRESS: '进行中',
    SUBMITTED: '已提交',
    APPROVED: '已审核',
    REJECTED: '已拒绝',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: 'info',
    NORMAL: 'primary',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityText = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return priorityMap[priority] || priority
}

// 获取截图数组（处理字符串格式的数组）
const getScreenshots = (screenshots: any): string[] => {
  if (!screenshots) return []

  let urls: string[] = []

  // 如果是字符串，尝试解析为数组
  if (typeof screenshots === 'string') {
    try {
      // 处理类似 "["/uploads/screenshots/xxx.jpg"]" 的字符串
      const parsed = JSON.parse(screenshots)
      urls = Array.isArray(parsed) ? parsed : [screenshots]
    } catch {
      // 如果解析失败，检查是否是单个URL
      urls = screenshots.startsWith('[') && screenshots.endsWith(']')
        ? screenshots.slice(1, -1).split(',').map(s => s.trim().replace(/"/g, ''))
        : [screenshots]
    }
  } else if (Array.isArray(screenshots)) {
    // 如果已经是数组，直接使用
    urls = screenshots
  }

  // 确保所有URL都是完整的URL
  // 静态文件服务在主服务器上，不是API路径
  const baseUrl = import.meta.env.VITE_API_BASE_URL?.replace('/api/v1', '') || 'http://localhost:3000'
  return urls.map(url => {
    if (!url) return ''
    // 如果已经是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }
    // 如果是相对路径，添加baseUrl
    return url.startsWith('/') ? `${baseUrl}${url}` : `${baseUrl}/${url}`
  }).filter(url => url) // 过滤掉空字符串
}

// 游戏类型名称映射
const gameTypeNames: Record<string, string> = {
  'wzry': '王者荣耀',
  'lol': '英雄联盟',
  'ys': '原神',
  'hpjy': '和平精英',
  'pubg': '绝地求生',
  'mc': '鸣潮',
  'csgo': 'CSGO',
  'dota2': 'DOTA2'
}

const getGameTypeName = (gameType: string): string => {
  return gameTypeNames[gameType] || gameType
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 复制到剪贴板
const copyToClipboard = async (text: string, label: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(`${label}已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  task.value = null
  showPassword.value = false // 重置密码显示状态
}
</script>

<style lang="scss" scoped>
.task-detail {
  .info-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .task-description,
  .task-notes {
    margin-top: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      color: var(--text-primary);
      font-size: 14px;
    }
    
    p {
      margin: 0;
      color: #606266;
      line-height: 1.5;
    }
  }
  
  .progress-item {
    .progress-header {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;
      
      .progress-value {
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .current-rank {
        color: #606266;
        font-size: 14px;
      }
    }
    
    .progress-description {
      margin-bottom: 12px;
      color: #606266;
      line-height: 1.5;
    }
    
    .progress-screenshots {
      h5 {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: var(--text-primary);
      }
      
      .screenshot-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
        
        .screenshot-thumb {
          width: 100px;
          height: 100px;
          border-radius: 4px;
          cursor: pointer;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .credential-value {
    font-family: 'Courier New', monospace;
    background: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    min-width: 120px;

    &.password-hidden {
      letter-spacing: 2px;
    }
  }
}
</style>
