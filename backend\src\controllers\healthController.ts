import { Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { checkDatabaseHealth } from '../config/database';
import { config } from '../config/env';

// 基础健康检查
export const healthCheck = asyncHandler(async (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.NODE_ENV,
      version: '1.0.0',
    },
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});

// 详细健康检查
export const detailedHealthCheck = asyncHandler(async (req: Request, res: Response) => {
  const dbHealth = await checkDatabaseHealth();
  
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  const healthData = {
    status: dbHealth.mysql ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.NODE_ENV,
    version: '1.0.0',
    database: dbHealth,
    system: {
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
    },
  };

  const response: ApiResponse = {
    success: true,
    data: healthData,
    message: healthData.status === 'healthy' ? '服务运行正常' : '服务存在问题',
    timestamp: new Date().toISOString(),
  };

  const statusCode = healthData.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(response);
});

// 就绪检查
export const readinessCheck = asyncHandler(async (req: Request, res: Response) => {
  const dbHealth = await checkDatabaseHealth();
  
  const isReady = dbHealth.mysql;
  
  const response: ApiResponse = {
    success: isReady,
    data: {
      ready: isReady,
      database: dbHealth,
      timestamp: new Date().toISOString(),
    },
    message: isReady ? '服务已就绪' : '服务未就绪',
    timestamp: new Date().toISOString(),
  };

  const statusCode = isReady ? 200 : 503;
  res.status(statusCode).json(response);
});

// 存活检查
export const livenessCheck = asyncHandler(async (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: {
      alive: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    },
    message: '服务存活',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});
