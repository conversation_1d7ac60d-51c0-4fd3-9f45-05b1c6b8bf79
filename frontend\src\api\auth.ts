import { request } from './http'
import type { LoginForm, RegisterForm, User, ApiResponse } from '@/types'

export interface LoginResponse {
  user: User
  token: string
  expiresIn: string
}

export interface RegisterResponse {
  user: User
  token: string
  expiresIn: string
}

export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return request.post('/auth/login', data)
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<RegisterResponse>> {
    return request.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<ApiResponse<User>> {
    return request.get('/auth/me')
  },

  // 更新密码
  updatePassword(data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<{ message: string }>> {
    return request.put('/auth/password', data)
  },



  // 用户登出
  logout(): Promise<ApiResponse<{ message: string }>> {
    return request.post('/auth/logout')
  }
}
