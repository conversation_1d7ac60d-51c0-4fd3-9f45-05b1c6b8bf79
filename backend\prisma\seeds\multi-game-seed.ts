import { PrismaClient, PriceRuleType, SkillLevel } from '@prisma/client';

const prisma = new PrismaClient();

// 游戏配置数据
const gamesData = [
  {
    id: 'game_wzry_001',
    name: 'wzry',
    displayName: '王者荣耀',
    description: '腾讯王者荣耀MOBA游戏',
    icon: '/images/games/wzry.png',
    isActive: true,
    sortOrder: 1,
    config: {
      maxRankJump: 2, // 最大段位跨越
      seasonDuration: 90, // 赛季持续天数
      features: ['排位赛', '巅峰赛', '娱乐模式']
    }
  },
  {
    id: 'game_lol_001',
    name: 'lol',
    displayName: '英雄联盟',
    description: '拳头游戏英雄联盟MOBA游戏',
    icon: '/images/games/lol.png',
    isActive: true,
    sortOrder: 2,
    config: {
      maxRankJump: 3,
      seasonDuration: 120,
      features: ['排位赛', '云顶之弈', '极地大乱斗']
    }
  },
  {
    id: 'game_pubg_001',
    name: 'pubg',
    displayName: '绝地求生',
    description: 'PUBG大逃杀游戏',
    icon: '/images/games/pubg.png',
    isActive: true,
    sortOrder: 3,
    config: {
      maxRankJump: 1,
      seasonDuration: 60,
      features: ['经典模式', '竞技模式', '创意工坊']
    }
  }
];

// 王者荣耀段位数据
const wzryRanks = [
  { name: '倔强青铜', level: 1, difficultyMultiplier: 1.0 },
  { name: '秩序白银', level: 2, difficultyMultiplier: 1.2 },
  { name: '荣耀黄金', level: 3, difficultyMultiplier: 1.4 },
  { name: '尊贵铂金', level: 4, difficultyMultiplier: 1.6 },
  { name: '永恒钻石', level: 5, difficultyMultiplier: 1.8 },
  { name: '至尊星耀', level: 6, difficultyMultiplier: 2.0 },
  { name: '最强王者', level: 7, difficultyMultiplier: 2.5 },
  { name: '荣耀王者', level: 8, difficultyMultiplier: 3.0 }
];

// 英雄联盟段位数据
const lolRanks = [
  { name: '黑铁', level: 1, difficultyMultiplier: 1.0 },
  { name: '青铜', level: 2, difficultyMultiplier: 1.1 },
  { name: '白银', level: 3, difficultyMultiplier: 1.3 },
  { name: '黄金', level: 4, difficultyMultiplier: 1.5 },
  { name: '铂金', level: 5, difficultyMultiplier: 1.7 },
  { name: '钻石', level: 6, difficultyMultiplier: 2.0 },
  { name: '大师', level: 7, difficultyMultiplier: 2.5 },
  { name: '宗师', level: 8, difficultyMultiplier: 3.0 },
  { name: '王者', level: 9, difficultyMultiplier: 3.5 }
];

// PUBG段位数据
const pubgRanks = [
  { name: '青铜', level: 1, difficultyMultiplier: 1.0 },
  { name: '白银', level: 2, difficultyMultiplier: 1.2 },
  { name: '黄金', level: 3, difficultyMultiplier: 1.4 },
  { name: '铂金', level: 4, difficultyMultiplier: 1.6 },
  { name: '钻石', level: 5, difficultyMultiplier: 1.8 },
  { name: '皇冠', level: 6, difficultyMultiplier: 2.2 },
  { name: '王牌', level: 7, difficultyMultiplier: 2.8 },
  { name: '征服者', level: 8, difficultyMultiplier: 3.5 }
];

// 价格规则数据
const priceRulesData = [
  {
    gameId: 'game_wzry_001',
    name: '王者荣耀基础价格',
    description: '基于段位等级的价格计算',
    ruleType: PriceRuleType.RANK_BASED,
    basePrice: 50.0,
    pricePerLevel: 30.0,
    multiplier: 1.0
  },
  {
    gameId: 'game_lol_001',
    name: '英雄联盟基础价格',
    description: '基于段位等级的价格计算',
    ruleType: PriceRuleType.RANK_BASED,
    basePrice: 60.0,
    pricePerLevel: 35.0,
    multiplier: 1.1
  },
  {
    gameId: 'game_pubg_001',
    name: 'PUBG基础价格',
    description: '基于段位等级的价格计算',
    ruleType: PriceRuleType.RANK_BASED,
    basePrice: 40.0,
    pricePerLevel: 25.0,
    multiplier: 0.9
  }
];

async function seedMultiGameData() {
  console.log('🎮 开始初始化多游戏数据...');

  try {
    // 1. 创建游戏
    console.log('📝 创建游戏配置...');
    for (const gameData of gamesData) {
      await prisma.game.upsert({
        where: { id: gameData.id },
        update: gameData,
        create: gameData
      });
    }

    // 2. 创建段位数据
    console.log('🏆 创建段位数据...');
    
    // 王者荣耀段位
    for (const rank of wzryRanks) {
      await prisma.gameRank.upsert({
        where: { 
          gameId_name: { 
            gameId: 'game_wzry_001', 
            name: rank.name 
          } 
        },
        update: {
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        },
        create: {
          gameId: 'game_wzry_001',
          name: rank.name,
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        }
      });
    }

    // 英雄联盟段位
    for (const rank of lolRanks) {
      await prisma.gameRank.upsert({
        where: { 
          gameId_name: { 
            gameId: 'game_lol_001', 
            name: rank.name 
          } 
        },
        update: {
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        },
        create: {
          gameId: 'game_lol_001',
          name: rank.name,
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        }
      });
    }

    // PUBG段位
    for (const rank of pubgRanks) {
      await prisma.gameRank.upsert({
        where: { 
          gameId_name: { 
            gameId: 'game_pubg_001', 
            name: rank.name 
          } 
        },
        update: {
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        },
        create: {
          gameId: 'game_pubg_001',
          name: rank.name,
          displayName: rank.name,
          level: rank.level,
          difficultyMultiplier: rank.difficultyMultiplier
        }
      });
    }

    // 3. 创建价格规则
    console.log('💰 创建价格规则...');
    for (const priceRule of priceRulesData) {
      await prisma.gamePriceRule.create({
        data: priceRule
      });
    }

    console.log('✅ 多游戏数据初始化完成！');
    
  } catch (error) {
    console.error('❌ 多游戏数据初始化失败:', error);
    throw error;
  }
}

export { seedMultiGameData };

// 如果直接运行此文件
if (require.main === module) {
  seedMultiGameData()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
