import { Request, Response } from 'express';
import { UserService } from '../services/userService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';

const userService = new UserService();

// 获取用户列表
export const getUsers = asyncHandler(async (req: Request, res: Response) => {
  const query = req.query as any;
  
  const result = await userService.getUsers(query);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '获取用户列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取用户详情
export const getUserById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const user = await userService.getUserById(id);
  
  const response: ApiResponse = {
    success: true,
    data: user,
    message: '获取用户详情成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 更新用户信息
export const updateUser = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData = req.body;
  
  const user = await userService.updateUser(id, updateData);
  
  const response: ApiResponse = {
    success: true,
    data: user,
    message: '用户信息更新成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 删除用户
export const deleteUser = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const result = await userService.deleteUser(id);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '用户删除成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取员工统计信息
export const getEmployeeStats = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const stats = await userService.getEmployeeStats(id);
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取员工统计信息成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取当前用户的统计信息
export const getMyStats = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const userRole = req.user!.role;

  const stats = await userService.getMyStats(userId, userRole);

  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取个人统计信息成功',
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(response);
});
