<template>
  <div class="nav-header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 菜单切换按钮 -->
      <button 
        class="nav-toggle"
        @click="$emit('toggle-sidebar')"
        :title="collapsed ? '展开菜单' : '收起菜单'"
      >
        <el-icon class="toggle-icon">
          <component :is="collapsed ? 'Expand' : 'Fold'" />
        </el-icon>
      </button>

      <!-- 面包屑导航 -->
      <div v-if="showBreadcrumb" class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item 
            v-for="item in breadcrumbItems" 
            :key="item.path || item.name"
          >
            <router-link 
              v-if="item.path && !item.current" 
              :to="item.path"
              class="breadcrumb-link"
            >
              {{ item.name }}
            </router-link>
            <span v-else class="breadcrumb-current">
              {{ item.name }}
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 通知中心 -->
      <div v-if="showNotifications" class="notification-container">
        <NotificationCenter />
      </div>

      <!-- 用户下拉菜单 -->
      <div class="user-dropdown">
        <el-dropdown @command="handleUserCommand">
          <div class="user-trigger">
            <div class="user-avatar">
              {{ getUserInitial() }}
            </div>
            <span class="user-name">
              {{ displayName }}
            </span>
            <el-icon class="dropdown-arrow">
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile" :icon="User">
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="logout" :icon="SwitchButton" divided>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  ArrowDown,
  User,
  SwitchButton,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import NotificationCenter from '@/components/NotificationCenter.vue'

interface BreadcrumbItem {
  name: string
  path?: string
  current?: boolean
}

interface Props {
  collapsed: boolean
  showBreadcrumb?: boolean
  showNotifications?: boolean
  customBreadcrumb?: BreadcrumbItem[]
}

interface Emits {
  (e: 'toggle-sidebar'): void
}

const props = withDefaults(defineProps<Props>(), {
  showBreadcrumb: true,
  showNotifications: true
})

const emit = defineEmits<Emits>()

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 用户显示名称
const displayName = computed(() => {
  return authStore.user?.nickname || authStore.user?.username || '用户'
})

// 获取用户头像首字母
const getUserInitial = () => {
  const name = displayName.value
  return name.charAt(0).toUpperCase()
}

// 面包屑导航项
const breadcrumbItems = computed(() => {
  if (props.customBreadcrumb) {
    return props.customBreadcrumb
  }

  // 自动生成面包屑
  const items: BreadcrumbItem[] = []
  
  // 添加首页
  const homePath = getHomePath()
  const homeName = getHomePageName()
  
  if (route.path !== homePath) {
    items.push({
      name: homeName,
      path: homePath
    })
  }

  // 添加当前页面
  if (route.meta.title) {
    items.push({
      name: route.meta.title as string,
      current: true
    })
  }

  return items
})

// 获取首页路径
const getHomePath = () => {
  if (!authStore.user) return '/'
  
  switch (authStore.user.role) {
    case 'BOSS':
    case 'ADMIN':
      return '/boss/orders'
    case 'EMPLOYEE':
      return '/employee/available-tasks'
    default:
      return '/'
  }
}

// 获取首页名称
const getHomePageName = () => {
  if (!authStore.user) return '首页'
  
  switch (authStore.user.role) {
    case 'BOSS':
    case 'ADMIN':
      return '订单管理'
    case 'EMPLOYEE':
      return '可接单任务'
    default:
      return '首页'
  }
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      authStore.logout()
      break
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/navigation.scss';

// 面包屑样式
.breadcrumb-container {
  margin-left: 16px;

  :deep(.el-breadcrumb) {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        color: #6b7280;
        font-weight: 500;
        transition: color 0.2s ease;

        &:hover {
          color: #374151;
        }
      }

      &:last-child .el-breadcrumb__inner {
        color: #111827;
        font-weight: 600;
      }
    }

    .el-breadcrumb__separator {
      color: #9ca3af;
    }
  }

  .breadcrumb-link {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: #2563eb;
    }
  }

  .breadcrumb-current {
    color: #111827;
    font-weight: 600;
  }
}

// 通知中心容器
.notification-container {
  margin-right: 16px;
}

// 响应式调整
@media (max-width: 768px) {
  .breadcrumb-container {
    display: none;
  }

  .header-left {
    .nav-toggle {
      margin-right: 0;
    }
  }

  .header-right {
    .notification-container {
      margin-right: 8px;
    }

    .user-dropdown .user-trigger {
      .user-name {
        display: none;
      }
    }
  }
}

@media (max-width: 480px) {
  .nav-header {
    padding: 0 12px;
  }

  .header-right {
    gap: 8px;

    .notification-container {
      margin-right: 4px;
    }
  }
}
</style>
