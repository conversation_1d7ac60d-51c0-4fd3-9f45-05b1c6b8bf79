<template>
  <el-dialog
    v-model="visible"
    title="创建任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="选择订单" prop="orderId">
        <el-select 
          v-model="form.orderId" 
          placeholder="请选择订单"
          filterable
          style="width: 100%"
          @change="handleOrderChange"
        >
          <el-option
            v-for="order in orders"
            :key="order.id"
            :label="`${order.orderNo} - ${order.customerName} (${order.currentRank} → ${order.targetRank})`"
            :value="order.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分配方式" prop="assignType">
        <el-radio-group v-model="form.assignType" @change="handleAssignTypeChange">
          <el-radio value="DIRECT">直接分配</el-radio>
          <el-radio value="SYSTEM">系统挂单</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        label="分配员工" 
        prop="assigneeId"
        v-if="form.assignType === 'DIRECT'"
      >
        <el-select 
          v-model="form.assigneeId" 
          placeholder="请选择员工"
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="employee in employees"
            :key="employee.id"
            :label="`${employee.nickname || employee.username} (等级${employee.level})`"
            :value="employee.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="预计工时" prop="estimatedHours">
        <el-input-number 
          v-model="form.estimatedHours" 
          :min="1" 
          :max="168"
          placeholder="请输入预计工时"
          style="width: 100%"
        />
        <div class="form-tip">单位：小时</div>
      </el-form-item>

      <el-form-item label="佣金设置方式" prop="commissionType">
        <el-radio-group v-model="form.commissionType" @change="handleCommissionTypeChange">
          <el-radio value="AUTO">自动计算佣金</el-radio>
          <el-radio value="MANUAL">手动输入佣金</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 自动计算佣金 -->
      <template v-if="form.commissionType === 'AUTO'">
        <el-form-item label="计算参数">
          <div class="commission-params">
            <div class="param-row">
              <label>基础费率:</label>
              <el-input-number
                v-model="commissionParams.baseRate"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                placeholder="0.30"
                style="width: 120px"
              />
              <span class="param-unit">（30% = 0.30）</span>
            </div>
            <div class="param-row">
              <label>难度系数:</label>
              <el-input-number
                v-model="commissionParams.difficultyMultiplier"
                :min="1"
                :max="2"
                :step="0.1"
                :precision="1"
                placeholder="自动计算"
                style="width: 120px"
              />
              <span class="param-unit">（1.0-2.0）</span>
            </div>
            <div class="param-row">
              <label>时间系数:</label>
              <el-input-number
                v-model="commissionParams.timeMultiplier"
                :min="1"
                :max="2"
                :step="0.1"
                :precision="1"
                placeholder="自动计算"
                style="width: 120px"
              />
              <span class="param-unit">（1.0-2.0）</span>
            </div>
          </div>
          <div class="commission-actions">
            <el-button type="primary" @click="calculateCommission" :loading="calculating">
              计算佣金
            </el-button>
            <el-button @click="showCalculationDetail = !showCalculationDetail">
              {{ showCalculationDetail ? '隐藏' : '查看' }}计算详情
            </el-button>
          </div>
        </el-form-item>

        <!-- 计算结果 -->
        <el-form-item v-if="calculationResult" label="计算结果">
          <div class="calculation-result">
            <div class="result-summary">
              <span class="label">计算佣金:</span>
              <span class="value">¥{{ calculationResult.commission }}</span>
            </div>
            <div v-if="showCalculationDetail" class="calculation-detail">
              <div class="detail-item">
                <span>基础佣金: ¥{{ calculationResult.calculation.baseCommission }}</span>
              </div>
              <div class="detail-item">
                <span>难度加成: ¥{{ calculationResult.calculation.difficultyBonus }}</span>
              </div>
              <div class="detail-item">
                <span>时间加成: ¥{{ calculationResult.calculation.timeBonus }}</span>
              </div>
              <div class="detail-item total">
                <span>总计: ¥{{ calculationResult.calculation.totalCommission }}</span>
              </div>
              <div class="formula">
                <el-text type="info" size="small">
                  <pre>{{ calculationResult.calculation.formula }}</pre>
                </el-text>
              </div>
            </div>
          </div>
        </el-form-item>
      </template>

      <!-- 手动输入佣金 -->
      <template v-else>
        <el-form-item label="佣金金额" prop="commission">
          <el-input-number
            v-model="form.commission"
            :min="0"
            :max="9999"
            :precision="2"
            placeholder="请输入佣金"
            style="width: 100%"
          />
          <div class="form-tip">
            单位：元
          </div>
        </el-form-item>
      </template>

      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述（可选）"
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { taskApi } from '@/api/tasks'
import { orderApi } from '@/api/orders'
import { userApi } from '@/api/users'
import type {
  TaskForm,
  Order,
  User,
  AssignType,
  CommissionType,
  CommissionCalculationParams,
  CommissionCalculationResult
} from '@/types'

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const calculating = ref(false)
const showCalculationDetail = ref(false)
const orders = ref<Order[]>([])
const employees = ref<User[]>([])
const calculationResult = ref<CommissionCalculationResult | null>(null)

const visible = ref(false)

const form = reactive<TaskForm>({
  orderId: '',
  assigneeId: '',
  assignType: 'SYSTEM',
  estimatedHours: undefined,
  commission: undefined,
  commissionType: 'MANUAL',
  commissionParams: undefined,
  description: '',
  notes: ''
})

const commissionParams = reactive<CommissionCalculationParams>({
  baseRate: 0.30,
  difficultyMultiplier: undefined,
  timeMultiplier: undefined
})

// 表单验证规则
const rules: FormRules = {
  orderId: [
    { required: true, message: '请选择订单', trigger: 'change' }
  ],
  assignType: [
    { required: true, message: '请选择分配方式', trigger: 'change' }
  ],
  assigneeId: [
    { 
      required: true, 
      message: '请选择员工', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.assignType === 'DIRECT' && !value) {
          callback(new Error('直接分配时必须选择员工'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    fetchOrders()
    fetchEmployees()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取待处理订单列表
const fetchOrders = async () => {
  try {
    const response = await orderApi.getOrders({
      status: 'PENDING',
      limit: 100
    })
    
    if (response.success && response.data) {
      orders.value = response.data.items
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  }
}

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await userApi.getUsers({
      role: 'EMPLOYEE',
      status: 'ACTIVE',
      limit: 100
    })
    
    if (response.success && response.data) {
      employees.value = response.data.items
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
  }
}

// 订单变化处理
const handleOrderChange = (orderId: string) => {
  const selectedOrder = orders.value.find(order => order.id === orderId)
  if (selectedOrder) {
    // 清除之前的计算结果
    calculationResult.value = null
  }
}

// 分配方式变化处理
const handleAssignTypeChange = (assignType: AssignType) => {
  if (assignType === 'SYSTEM') {
    form.assigneeId = ''
  }
}

// 佣金计算方式变化处理
const handleCommissionTypeChange = (commissionType: CommissionType) => {
  if (commissionType === 'AUTO') {
    calculationResult.value = null
    form.commission = undefined
  }
}

// 计算佣金
const calculateCommission = async () => {
  if (!form.orderId) {
    ElMessage.warning('请先选择订单')
    return
  }

  const selectedOrder = orders.value.find(order => order.id === form.orderId)
  if (!selectedOrder) {
    ElMessage.warning('订单信息不存在')
    return
  }

  try {
    calculating.value = true

    const params: CommissionCalculationParams = {
      orderPrice: selectedOrder.price,
      estimatedHours: form.estimatedHours,
      currentRank: selectedOrder.currentRank,
      targetRank: selectedOrder.targetRank,
      ...commissionParams
    }

    const response = await taskApi.calculateCommission(params)
    if (response.success) {
      calculationResult.value = response.data
      form.commission = response.data.commission
      form.commissionParams = {
        ...params,
        calculationResult: response.data.calculation
      }
      ElMessage.success('佣金计算成功')
    }
  } catch (error) {
    console.error('计算佣金失败:', error)
    ElMessage.error('计算佣金失败')
  } finally {
    calculating.value = false
  }
}



// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      loading.value = true
      
      const submitData = {
        ...form,
        assigneeId: form.assignType === 'DIRECT' ? form.assigneeId : undefined
      }
      
      const response = await taskApi.createTask(submitData)
      if (response.success) {
        ElMessage.success('任务创建成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('创建任务失败:', error)
    ElMessage.error('创建任务失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置表单
  formRef.value?.resetFields()
  Object.assign(form, {
    orderId: '',
    assigneeId: '',
    assignType: 'SYSTEM',
    estimatedHours: undefined,
    commission: undefined,
    commissionType: 'MANUAL',
    commissionParams: undefined,
    description: '',
    notes: ''
  })

  // 重置佣金相关状态
  calculationResult.value = null
  showCalculationDetail.value = false
  Object.assign(commissionParams, {
    baseRate: 0.30,
    difficultyMultiplier: undefined,
    timeMultiplier: undefined
  })
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;

  .suggested-range {
    color: #67c23a;
    font-weight: 500;
  }
}

.commission-params {
  .param-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    label {
      width: 80px;
      font-size: 14px;
      color: #606266;
    }

    .param-unit {
      margin-left: 8px;
      font-size: 12px;
      color: #909399;
    }
  }
}

.commission-actions {
  margin-top: 16px;

  .el-button {
    margin-right: 12px;
  }
}

.calculation-result {
  .result-summary {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .label {
      font-weight: 500;
      margin-right: 8px;
    }

    .value {
      font-size: 18px;
      font-weight: 600;
      color: #67c23a;
    }
  }

  .calculation-detail {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 4px;

    .detail-item {
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;

      &.total {
        font-weight: 600;
        color: #303133;
        border-top: 1px solid #dcdfe6;
        padding-top: 8px;
        margin-top: 8px;
      }
    }

    .formula {
      margin-top: 12px;

      pre {
        font-size: 12px;
        line-height: 1.4;
        margin: 0;
        white-space: pre-wrap;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
