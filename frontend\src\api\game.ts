import { request } from './http';
import type {
  Game,
  GameRank,
  GamePriceRule,
  EmployeeGameSkill,
  CreateGameRequest,
  UpdateGameRequest,
  CreateGameRankRequest,
  UpdateGameRankRequest,
  GameQuery,
  GameRankQuery,
  GameStatistics,
  GameHotness,
  PriceCalculationResult,
  SimpleGame,
  SimpleGameRank
} from '@/types/game';
import type { PaginatedResponse, ApiResponse } from '@/types';

// 游戏管理API
export const gameApi = {
  // 获取活跃游戏简单列表
  getActiveGames(): Promise<ApiResponse<SimpleGame[]>> {
    // 添加时间戳参数避免缓存
    const timestamp = new Date().getTime();
    return request.get(`/games/active?_t=${timestamp}`);
  },

  // 获取游戏及其表单字段
  getGameWithFormFields(id: string): Promise<ApiResponse<Game>> {
    return request.get(`/games/${id}/form-fields`);
  },

  // 获取游戏列表
  getGames(params?: GameQuery): Promise<PaginatedResponse<Game>> {
    return request.get('/games', { params });
  },

  // 获取游戏详情
  getGameById(id: string): Promise<Game> {
    return request.get(`/games/${id}`);
  },

  // 创建游戏
  createGame(data: CreateGameRequest): Promise<Game> {
    console.log('🎮 API发送创建游戏请求:', data);
    return request.post('/games', data);
  },

  // 更新游戏
  updateGame(id: string, data: UpdateGameRequest): Promise<Game> {
    return request.put(`/games/${id}`, data);
  },

  // 删除游戏
  deleteGame(id: string): Promise<{ message: string }> {
    return request.delete(`/games/${id}`);
  },

  // 获取游戏统计信息
  getGameStatistics(id: string): Promise<GameStatistics> {
    return request.get(`/games/${id}/statistics`);
  },

  // 获取游戏热度分析
  getGameHotness(): Promise<GameHotness[]> {
    return request.get('/games/hotness');
  },

  // 计算订单价格
  calculateOrderPrice(data: {
    gameId: string;
    currentRankId: string;
    targetRankId: string;
    priority?: string;
  }): Promise<PriceCalculationResult> {
    return request.post('/games/calculate-price', data);
  }
};





// 员工游戏技能API
export const employeeGameSkillApi = {
  // 获取员工游戏技能列表
  getEmployeeGameSkills(params?: {
    userId?: string;
    gameId?: string;
    skillLevel?: string;
    isActive?: boolean;
  }): Promise<PaginatedResponse<EmployeeGameSkill>> {
    return request.get('/games/employee-skills', { params });
  },

  // 获取员工游戏技能详情
  getEmployeeGameSkillById(id: string): Promise<EmployeeGameSkill> {
    return request.get(`/games/employee-skills/${id}`);
  },

  // 创建员工游戏技能
  createEmployeeGameSkill(data: any): Promise<EmployeeGameSkill> {
    return request.post('/games/employee-skills', data);
  },

  // 更新员工游戏技能
  updateEmployeeGameSkill(id: string, data: any): Promise<EmployeeGameSkill> {
    return request.put(`/games/employee-skills/${id}`, data);
  },

  // 删除员工游戏技能
  deleteEmployeeGameSkill(id: string): Promise<{ message: string }> {
    return request.delete(`/games/employee-skills/${id}`);
  },

  // 认证员工技能
  certifyEmployeeSkill(id: string): Promise<EmployeeGameSkill> {
    return request.post(`/games/employee-skills/${id}/certify`);
  }
};

// 游戏相关的统计和分析API
export const gameAnalyticsApi = {
  // 获取游戏订单分布统计
  getGameOrderDistribution(): Promise<{
    gameId: string;
    gameName: string;
    orderCount: number;
    percentage: number;
  }[]> {
    return request.get('/games/analytics/order-distribution');
  },

  // 获取游戏收益统计
  getGameRevenueStats(params?: {
    startDate?: string;
    endDate?: string;
    gameId?: string;
  }): Promise<{
    gameId: string;
    gameName: string;
    revenue: number;
    orderCount: number;
    averageOrderValue: number;
  }[]> {
    return request.get('/games/analytics/revenue', { params });
  },

  // 获取游戏趋势分析
  getGameTrends(params?: {
    period?: 'week' | 'month' | 'quarter';
    gameId?: string;
  }): Promise<{
    date: string;
    gameStats: {
      gameId: string;
      gameName: string;
      orderCount: number;
      revenue: number;
    }[];
  }[]> {
    return request.get('/games/analytics/trends', { params });
  },

  // 获取员工游戏技能分布
  getEmployeeSkillDistribution(gameId?: string): Promise<{
    skillLevel: string;
    count: number;
    percentage: number;
  }[]> {
    return request.get('/games/analytics/skill-distribution', {
      params: { gameId }
    });
  }
};

// 便捷导出函数
export const getActiveGames = async (): Promise<Game[]> => {
  const response = await gameApi.getActiveGames();
  return response.data;
};

export const getGameWithFormFields = async (id: string): Promise<Game> => {
  const response = await gameApi.getGameWithFormFields(id);
  return response.data;
};

export const getGames = async (params?: GameQuery): Promise<{
  items: Game[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> => {
  const response = await gameApi.getGames(params);
  return response.data;
};

export const updateGame = async (id: string, data: UpdateGameRequest): Promise<Game> => {
  const response = await gameApi.updateGame(id, data);
  return response.data;
};

export const deleteGame = async (id: string): Promise<void> => {
  await gameApi.deleteGame(id);
};

// 导出所有API
export default {
  game: gameApi,
  employeeGameSkill: employeeGameSkillApi,
  gameAnalytics: gameAnalyticsApi
};
