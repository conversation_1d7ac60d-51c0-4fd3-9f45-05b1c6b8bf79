<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑字段' : '添加字段'"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="字段键名" prop="fieldKey">
            <el-input
              v-model="formData.fieldKey"
              placeholder="如: customer_name"
              :disabled="isEdit"
            />
            <div class="form-tip">
              程序内部使用的唯一标识，只能包含字母、数字和下划线，且必须以字母开头
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段标签" prop="fieldLabel">
            <el-input
              v-model="formData.fieldLabel"
              placeholder="如: 客户姓名"
            />
            <div class="form-tip">
              显示给用户看的名字
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="字段类型" prop="fieldType">
            <el-select
              v-model="formData.fieldType"
              placeholder="请选择字段类型"
              style="width: 100%"
              @change="handleFieldTypeChange"
            >
              <el-option
                v-for="type in fieldTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
                <div>
                  <div>{{ type.label }}</div>
                  <div class="option-desc">{{ type.description }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序权重" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="999"
              style="width: 100%"
            />
            <div class="form-tip">
              数字越小越靠前显示
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否必填" prop="isRequired">
            <el-switch v-model="formData.isRequired" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="isActive">
            <el-switch v-model="formData.isActive" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="占位提示" prop="placeholder">
        <el-input
          v-model="formData.placeholder"
          placeholder="如: 请输入客户姓名"
        />
        <div class="form-tip">
          输入框内的灰色提示文字
        </div>
      </el-form-item>

      <!-- 选项配置（当字段类型是select或checkbox时显示） -->
      <el-form-item
        v-if="needOptions"
        label="选项配置"
        prop="options"
      >
        <div class="options-config">
          <div class="options-header">
            <span>选项列表（每行一个选项）</span>
            <el-button size="small" @click="addOption">添加选项</el-button>
          </div>
          <div class="options-list">
            <div
              v-for="(option, index) in formData.options"
              :key="index"
              class="option-item"
            >
              <el-input
                v-model="formData.options[index]"
                placeholder="请输入选项内容"
                size="small"
              />
              <el-button
                size="small"
                type="danger"
                @click="removeOption(index)"
                :icon="Delete"
              />
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 高级配置 -->
      <el-form-item label="高级配置">
        <el-collapse>
          <el-collapse-item title="字段配置" name="config">
            <div class="config-section">
              <!-- 文本字段配置 -->
              <div v-if="formData.fieldType === 'TEXT' || formData.fieldType === 'TEXTAREA'">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="最小长度">
                      <el-input-number
                        v-model="configData.minLength"
                        :min="0"
                        placeholder="不限制"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="最大长度">
                      <el-input-number
                        v-model="configData.maxLength"
                        :min="1"
                        placeholder="不限制"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item v-if="formData.fieldType === 'TEXTAREA'" label="显示行数">
                  <el-input-number
                    v-model="configData.rows"
                    :min="1"
                    :max="10"
                    placeholder="3"
                  />
                </el-form-item>
              </div>

              <!-- 数字字段配置 -->
              <div v-if="formData.fieldType === 'NUMBER'">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="最小值">
                      <el-input-number
                        v-model="configData.min"
                        placeholder="不限制"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最大值">
                      <el-input-number
                        v-model="configData.max"
                        placeholder="不限制"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="步长">
                      <el-input-number
                        v-model="configData.step"
                        :min="0.01"
                        placeholder="1"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import {
  getFieldTypes,
  createGameFormField,
  updateGameFormField,
  validateFieldConfig,
  type GameFormField,
  type FormFieldType,
  type FieldTypeOption,
  type CreateGameFormFieldData,
  type UpdateGameFormFieldData
} from '@/api/gameFormFields'

// Props
interface Props {
  modelValue: boolean
  gameId: string
  field?: GameFormField | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const fieldTypes = ref<FieldTypeOption[]>([])

// 表单数据
const formData = reactive({
  fieldKey: '',
  fieldLabel: '',
  fieldType: 'TEXT' as FormFieldType,
  isRequired: false,
  placeholder: '',
  sortOrder: 0,
  options: [] as string[],
  isActive: true
})

// 配置数据
const configData = reactive({
  minLength: undefined as number | undefined,
  maxLength: undefined as number | undefined,
  rows: 3,
  min: undefined as number | undefined,
  max: undefined as number | undefined,
  step: 1
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.field)

const needOptions = computed(() => {
  return formData.fieldType === 'SELECT' || formData.fieldType === 'CHECKBOX'
})

// 表单验证规则
const formRules = {
  fieldKey: [
    { required: true, message: '请输入字段键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段键名只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  fieldLabel: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ],
  options: [
    {
      validator: (rule: any, value: string[], callback: Function) => {
        if (needOptions.value && (!value || value.length === 0)) {
          callback(new Error('请至少添加一个选项'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 方法
const loadFieldTypes = async () => {
  try {
    fieldTypes.value = await getFieldTypes()
  } catch (error) {
    console.error('加载字段类型失败:', error)
    ElMessage.error('加载字段类型失败')
  }
}

const initFormData = () => {
  if (props.field) {
    // 编辑模式
    Object.assign(formData, {
      fieldKey: props.field.fieldKey,
      fieldLabel: props.field.fieldLabel,
      fieldType: props.field.fieldType,
      isRequired: props.field.isRequired,
      placeholder: props.field.placeholder || '',
      sortOrder: props.field.sortOrder,
      options: props.field.options ? [...props.field.options] : [],
      isActive: props.field.isActive
    })

    // 初始化配置数据
    if (props.field.config) {
      Object.assign(configData, props.field.config)
    }
  } else {
    // 新建模式
    Object.assign(formData, {
      fieldKey: '',
      fieldLabel: '',
      fieldType: 'TEXT' as FormFieldType,
      isRequired: false,
      placeholder: '',
      sortOrder: 0,
      options: [],
      isActive: true
    })

    // 重置配置数据
    Object.assign(configData, {
      minLength: undefined,
      maxLength: undefined,
      rows: 3,
      min: undefined,
      max: undefined,
      step: 1
    })
  }
}

const handleFieldTypeChange = () => {
  // 当字段类型改变时，清空选项
  if (!needOptions.value) {
    formData.options = []
  } else if (formData.options.length === 0) {
    // 如果是选择类型，添加一个默认选项
    addOption()
  }
}

const addOption = () => {
  formData.options.push('')
}

const removeOption = (index: number) => {
  formData.options.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    loading.value = true

    // 构建配置对象
    const config: Record<string, any> = {}
    if (formData.fieldType === 'TEXT' || formData.fieldType === 'TEXTAREA') {
      if (configData.minLength !== undefined) config.minLength = configData.minLength
      if (configData.maxLength !== undefined) config.maxLength = configData.maxLength
      if (formData.fieldType === 'TEXTAREA' && configData.rows) config.rows = configData.rows
    } else if (formData.fieldType === 'NUMBER') {
      if (configData.min !== undefined) config.min = configData.min
      if (configData.max !== undefined) config.max = configData.max
      if (configData.step !== undefined) config.step = configData.step
    }

    // 验证字段配置
    const validationResult = await validateFieldConfig({
      fieldType: formData.fieldType,
      options: needOptions.value ? formData.options : undefined,
      config: Object.keys(config).length > 0 ? config : undefined
    })

    if (!validationResult.valid) {
      ElMessage.error(validationResult.errors.join(', '))
      return
    }

    if (validationResult.warnings.length > 0) {
      ElMessage.warning(validationResult.warnings.join(', '))
    }

    if (isEdit.value) {
      // 更新字段
      const updateData: UpdateGameFormFieldData = {
        fieldLabel: formData.fieldLabel,
        fieldType: formData.fieldType,
        isRequired: formData.isRequired,
        placeholder: formData.placeholder,
        sortOrder: formData.sortOrder,
        options: needOptions.value ? formData.options : undefined,
        config: Object.keys(config).length > 0 ? config : undefined,
        isActive: formData.isActive
      }

      await updateGameFormField(props.field!.id, updateData)
      ElMessage.success('字段更新成功')
    } else {
      // 创建字段
      const createData: CreateGameFormFieldData = {
        gameId: props.gameId,
        fieldKey: formData.fieldKey,
        fieldLabel: formData.fieldLabel,
        fieldType: formData.fieldType,
        isRequired: formData.isRequired,
        placeholder: formData.placeholder,
        sortOrder: formData.sortOrder,
        options: needOptions.value ? formData.options : undefined,
        config: Object.keys(config).length > 0 ? config : undefined,
        isActive: formData.isActive
      }

      await createGameFormField(createData)
      ElMessage.success('字段创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存字段失败:', error)
    ElMessage.error('保存字段失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadFieldTypes()
    initFormData()
  }
})
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.options-config {
  width: 100%;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.options-list {
  max-height: 200px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.config-section {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style>
