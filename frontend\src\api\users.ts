import { request } from './http'
import type { User, PaginatedResponse, PaginationQuery, ApiResponse, UserRole, UserStatus } from '@/types'

export interface UserQuery extends PaginationQuery {
  role?: UserRole
  status?: UserStatus
  keyword?: string
}

export interface UpdateUserData {
  nickname?: string
  phone?: string
  level?: number
  status?: UserStatus
}

export interface UserStats {
  user: {
    id: string
    username: string
    nickname?: string
    role: string
    level: number
    totalEarnings: number
  }
  // 员工统计数据（直接在根级别）
  totalTasks: number
  completedTasks: number
  completionRate: number
  totalHours: number
  totalEarnings: number
  currentMonthTasks: number
  currentMonthEarnings: number
}

export const userApi = {
  // 获取用户列表
  getUsers(params?: UserQuery): Promise<ApiResponse<PaginatedResponse<User>>> {
    return request.get('/users', { params })
  },

  // 获取用户详情
  getUserById(id: string): Promise<ApiResponse<User>> {
    return request.get(`/users/${id}`)
  },

  // 更新用户信息
  updateUser(id: string, data: UpdateUserData): Promise<ApiResponse<User>> {
    return request.put(`/users/${id}`, data)
  },

  // 删除用户
  deleteUser(id: string): Promise<ApiResponse<{ message: string }>> {
    return request.delete(`/users/${id}`)
  },

  // 获取员工统计信息
  getEmployeeStats(id: string): Promise<ApiResponse<UserStats>> {
    return request.get(`/users/${id}/stats`)
  },

  // 获取当前用户的统计信息
  getMyStats(): Promise<ApiResponse<UserStats>> {
    return request.get('/users/me/stats')
  }
}
