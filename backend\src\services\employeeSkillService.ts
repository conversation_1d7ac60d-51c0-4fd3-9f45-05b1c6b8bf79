import { prisma } from '../config/database';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';
import {
  CreateEmployeeGameSkillRequest,
  UpdateEmployeeGameSkillRequest,
  EmployeeGameSkillQuery,
  EmployeeSkillMatch
} from '../types/game';

export class EmployeeSkillService {
  // 创建员工游戏技能
  async createEmployeeGameSkill(data: CreateEmployeeGameSkillRequest) {
    // 检查用户是否存在且为员工
    const user = await prisma.user.findUnique({
      where: { id: data.userId }
    });

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    if (user.role !== 'EMPLOYEE') {
      throw new ValidationError('只能为员工添加游戏技能');
    }

    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: data.gameId }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    // 检查是否已存在相同的技能记录
    const existingSkill = await prisma.employeeGameSkill.findUnique({
      where: {
        userId_gameId: {
          userId: data.userId,
          gameId: data.gameId
        }
      }
    });

    if (existingSkill) {
      throw new ValidationError('该员工已存在此游戏的技能记录');
    }

    // 验证最大段位等级
    if (data.maxRankLevel) {
      const maxRank = await prisma.gameRank.findFirst({
        where: {
          gameId: data.gameId,
          level: data.maxRankLevel,
          isActive: true
        }
      });

      if (!maxRank) {
        throw new ValidationError('指定的最大段位等级不存在');
      }
    }

    const skill = await prisma.employeeGameSkill.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        },
        game: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        }
      }
    });

    return skill;
  }

  // 获取员工游戏技能列表
  async getEmployeeGameSkills(query: PaginationQuery & EmployeeGameSkillQuery) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      userId,
      gameId,
      skillLevel,
      isActive,
      certified
    } = query;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    
    if (userId) {
      where.userId = userId;
    }
    
    if (gameId) {
      where.gameId = gameId;
    }
    
    if (skillLevel) {
      where.skillLevel = skillLevel;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    if (certified !== undefined) {
      if (certified) {
        where.certifiedAt = { not: null };
      } else {
        where.certifiedAt = null;
      }
    }

    // 查询技能记录
    const [skills, total] = await Promise.all([
      prisma.employeeGameSkill.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              level: true
            }
          },
          game: {
            select: {
              id: true,
              name: true,
              displayName: true,
              icon: true
            }
          },
          certifier: {
            select: {
              id: true,
              username: true,
              nickname: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.employeeGameSkill.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    const result: PaginatedResponse<typeof skills[0]> = {
      items: skills,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return result;
  }

  // 获取员工游戏技能详情
  async getEmployeeGameSkillById(id: string) {
    const skill = await prisma.employeeGameSkill.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true
          }
        },
        game: {
          select: {
            id: true,
            name: true,
            displayName: true,
            icon: true
          }
        },
        certifier: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    });

    if (!skill) {
      throw new NotFoundError('员工游戏技能不存在');
    }

    return skill;
  }

  // 更新员工游戏技能
  async updateEmployeeGameSkill(id: string, updateData: UpdateEmployeeGameSkillRequest) {
    // 检查技能记录是否存在
    const existingSkill = await prisma.employeeGameSkill.findUnique({
      where: { id }
    });

    if (!existingSkill) {
      throw new NotFoundError('员工游戏技能不存在');
    }

    // 验证最大段位等级
    if (updateData.maxRankLevel) {
      const maxRank = await prisma.gameRank.findFirst({
        where: {
          gameId: existingSkill.gameId,
          level: updateData.maxRankLevel,
          isActive: true
        }
      });

      if (!maxRank) {
        throw new ValidationError('指定的最大段位等级不存在');
      }
    }

    const skill = await prisma.employeeGameSkill.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true
          }
        },
        game: {
          select: {
            id: true,
            name: true,
            displayName: true,
            icon: true
          }
        },
        certifier: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    });

    return skill;
  }

  // 删除员工游戏技能
  async deleteEmployeeGameSkill(id: string) {
    // 检查技能记录是否存在
    const existingSkill = await prisma.employeeGameSkill.findUnique({
      where: { id }
    });

    if (!existingSkill) {
      throw new NotFoundError('员工游戏技能不存在');
    }

    await prisma.employeeGameSkill.delete({
      where: { id }
    });

    return { message: '员工游戏技能删除成功' };
  }

  // 认证员工技能
  async certifyEmployeeSkill(id: string, certifierId: string) {
    // 检查技能记录是否存在
    const existingSkill = await prisma.employeeGameSkill.findUnique({
      where: { id }
    });

    if (!existingSkill) {
      throw new NotFoundError('员工游戏技能不存在');
    }

    // 检查认证人是否存在且有权限
    const certifier = await prisma.user.findUnique({
      where: { id: certifierId }
    });

    if (!certifier || (certifier.role !== 'ADMIN' && certifier.role !== 'BOSS')) {
      throw new ValidationError('只有管理员或老板可以认证员工技能');
    }

    const skill = await prisma.employeeGameSkill.update({
      where: { id },
      data: {
        certifiedAt: new Date(),
        certifiedBy: certifierId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true
          }
        },
        game: {
          select: {
            id: true,
            name: true,
            displayName: true,
            icon: true
          }
        },
        certifier: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    });

    return skill;
  }

  // 获取指定游戏的技能员工匹配
  async getSkillMatchesForGame(gameId: string, targetRankLevel?: number): Promise<EmployeeSkillMatch[]> {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: gameId }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    // 获取该游戏的员工技能
    const skills = await prisma.employeeGameSkill.findMany({
      where: {
        gameId,
        isActive: true,
        ...(targetRankLevel && {
          OR: [
            { maxRankLevel: null },
            { maxRankLevel: { gte: targetRankLevel } }
          ]
        })
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            level: true
          }
        }
      }
    });

    // 获取每个员工的任务完成统计
    const matches: EmployeeSkillMatch[] = await Promise.all(
      skills.map(async (skill) => {
        // 获取该员工在此游戏的任务完成情况
        const taskStats = await prisma.task.aggregate({
          where: {
            assigneeId: skill.userId,
            order: { gameId },
            status: 'COMPLETED'
          },
          _count: true
        });

        // 计算匹配度评分
        let matchScore = 0;
        
        // 技能等级评分 (0-40分)
        const skillLevelScores = {
          BEGINNER: 10,
          INTERMEDIATE: 20,
          ADVANCED: 30,
          EXPERT: 35,
          MASTER: 40
        };
        matchScore += skillLevelScores[skill.skillLevel] || 0;

        // 认证状态评分 (0-20分)
        if (skill.certifiedAt) {
          matchScore += 20;
        }

        // 完成任务数量评分 (0-30分)
        const completedTasks = taskStats._count;
        if (completedTasks >= 50) matchScore += 30;
        else if (completedTasks >= 20) matchScore += 25;
        else if (completedTasks >= 10) matchScore += 20;
        else if (completedTasks >= 5) matchScore += 15;
        else if (completedTasks >= 1) matchScore += 10;

        // 段位能力评分 (0-10分)
        if (targetRankLevel && skill.maxRankLevel) {
          if (skill.maxRankLevel >= targetRankLevel + 2) matchScore += 10;
          else if (skill.maxRankLevel >= targetRankLevel) matchScore += 8;
          else if (skill.maxRankLevel >= targetRankLevel - 1) matchScore += 5;
        }

        return {
          userId: skill.user.id,
          username: skill.user.username,
          nickname: skill.user.nickname || undefined,
          skillLevel: skill.skillLevel,
          maxRankLevel: skill.maxRankLevel ?? undefined,
          matchScore,
          completedTasks,
          averageRating: 0 // TODO: 实现评分系统后计算平均评分
        };
      })
    );

    // 按匹配度排序
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }
}
