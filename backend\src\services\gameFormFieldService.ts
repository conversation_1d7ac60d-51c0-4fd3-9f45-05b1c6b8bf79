import { prisma } from '../config/database';
import type { FormFieldType } from '@prisma/client';

// 游戏表单字段数据类型
export interface GameFormFieldData {
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  isRequired?: boolean;
  placeholder?: string;
  sortOrder?: number;
  options?: string[];
  config?: Record<string, any>;
  isActive?: boolean;
}

export interface CreateGameFormFieldData extends GameFormFieldData {
  gameId: string;
}

export interface UpdateGameFormFieldData extends Partial<GameFormFieldData> {}

export interface GameFormFieldQuery {
  gameId?: string;
  isActive?: boolean;
  fieldType?: FormFieldType;
  page?: number;
  limit?: number;
}

/**
 * 游戏表单字段服务
 */
export class GameFormFieldService {
  /**
   * 创建游戏表单字段
   */
  async createField(data: CreateGameFormFieldData) {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: data.gameId }
    });
    
    if (!game) {
      throw new Error('游戏不存在');
    }

    // 检查字段键名是否已存在
    const existingField = await prisma.gameFormField.findUnique({
      where: {
        gameId_fieldKey: {
          gameId: data.gameId,
          fieldKey: data.fieldKey
        }
      }
    });

    if (existingField) {
      throw new Error('字段键名已存在');
    }

    // 如果没有指定排序，自动设置为最大值+1
    if (data.sortOrder === undefined) {
      const maxOrder = await prisma.gameFormField.aggregate({
        where: { gameId: data.gameId },
        _max: { sortOrder: true }
      });
      data.sortOrder = (maxOrder._max.sortOrder || 0) + 1;
    }

    return await prisma.gameFormField.create({
      data: {
        gameId: data.gameId,
        fieldKey: data.fieldKey,
        fieldLabel: data.fieldLabel,
        fieldType: data.fieldType,
        isRequired: data.isRequired || false,
        placeholder: data.placeholder,
        sortOrder: data.sortOrder,
        options: data.options || undefined,
        config: data.config || undefined,
        isActive: data.isActive !== undefined ? data.isActive : true
      }
    });
  }

  /**
   * 获取游戏表单字段列表
   */
  async getFields(query: GameFormFieldQuery = {}) {
    const {
      gameId,
      isActive,
      fieldType,
      page = 1,
      limit = 50
    } = query;

    const where: any = {};
    
    if (gameId) where.gameId = gameId;
    if (isActive !== undefined) where.isActive = isActive;
    if (fieldType) where.fieldType = fieldType;

    const [fields, total] = await Promise.all([
      prisma.gameFormField.findMany({
        where,
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'asc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          game: {
            select: {
              id: true,
              name: true,
              displayName: true
            }
          }
        }
      }),
      prisma.gameFormField.count({ where })
    ]);

    return {
      items: fields,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * 根据游戏ID获取活跃的表单字段
   */
  async getActiveFieldsByGameId(gameId: string) {
    return await prisma.gameFormField.findMany({
      where: {
        gameId,
        isActive: true
      },
      orderBy: [
        { sortOrder: 'asc' },
        { createdAt: 'asc' }
      ]
    });
  }

  /**
   * 获取字段详情
   */
  async getFieldById(id: string) {
    const field = await prisma.gameFormField.findUnique({
      where: { id },
      include: {
        game: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        }
      }
    });

    if (!field) {
      throw new Error('字段不存在');
    }

    return field;
  }

  /**
   * 更新字段
   */
  async updateField(id: string, data: UpdateGameFormFieldData) {
    const field = await this.getFieldById(id);

    // 如果更新字段键名，检查是否冲突
    if (data.fieldKey && data.fieldKey !== field.fieldKey) {
      const existingField = await prisma.gameFormField.findUnique({
        where: {
          gameId_fieldKey: {
            gameId: field.gameId,
            fieldKey: data.fieldKey
          }
        }
      });

      if (existingField) {
        throw new Error('字段键名已存在');
      }
    }

    return await prisma.gameFormField.update({
      where: { id },
      data: {
        fieldKey: data.fieldKey,
        fieldLabel: data.fieldLabel,
        fieldType: data.fieldType,
        isRequired: data.isRequired,
        placeholder: data.placeholder,
        sortOrder: data.sortOrder,
        options: data.options === null ? undefined : data.options,
        config: data.config === null ? undefined : data.config,
        isActive: data.isActive
      }
    });
  }

  /**
   * 检查字段使用情况
   */
  async getFieldUsageCount(fieldId: string): Promise<{ orderCount: number; usedInOrders: boolean }> {
    const field = await this.getFieldById(fieldId);

    // 使用原生SQL查询来检查JSON字段中是否包含指定的fieldKey
    // 这是最可靠的方法，避免了Prisma JSON查询的类型问题
    const result = await prisma.$queryRaw<[{ count: bigint }]>`
      SELECT COUNT(*) as count
      FROM orders
      WHERE gameId = ${field.gameId}
        AND (
          JSON_EXTRACT(details, ${`$.${field.fieldKey}`}) IS NOT NULL
          OR JSON_EXTRACT(formData, ${`$.${field.fieldKey}`}) IS NOT NULL
        )
    `;

    const orderCount = Number(result[0]?.count || 0);

    return {
      orderCount,
      usedInOrders: orderCount > 0
    };
  }

  /**
   * 删除字段
   */
  async deleteField(id: string) {
    // 先验证字段是否存在
    await this.getFieldById(id);

    // 检查字段使用情况
    const usage = await this.getFieldUsageCount(id);

    if (usage.usedInOrders) {
      throw new Error(`该字段正在被 ${usage.orderCount} 个订单使用，无法删除。请先处理相关订单或联系管理员。`);
    }

    return await prisma.gameFormField.delete({
      where: { id }
    });
  }

  /**
   * 批量更新字段排序
   */
  async updateFieldsOrder(gameId: string, fieldOrders: { id: string; sortOrder: number }[]) {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: gameId }
    });
    
    if (!game) {
      throw new Error('游戏不存在');
    }

    // 批量更新排序
    const updatePromises = fieldOrders.map(({ id, sortOrder }) =>
      prisma.gameFormField.update({
        where: { id },
        data: { sortOrder }
      })
    );

    return await Promise.all(updatePromises);
  }

  /**
   * 复制字段到其他游戏
   */
  async copyFieldToGame(fieldId: string, targetGameId: string, newFieldKey?: string) {
    const sourceField = await this.getFieldById(fieldId);
    
    // 检查目标游戏是否存在
    const targetGame = await prisma.game.findUnique({
      where: { id: targetGameId }
    });
    
    if (!targetGame) {
      throw new Error('目标游戏不存在');
    }

    const fieldKey = newFieldKey || sourceField.fieldKey;

    // 检查目标游戏中是否已存在相同键名的字段
    const existingField = await prisma.gameFormField.findUnique({
      where: {
        gameId_fieldKey: {
          gameId: targetGameId,
          fieldKey
        }
      }
    });

    if (existingField) {
      throw new Error('目标游戏中已存在相同键名的字段');
    }

    // 获取目标游戏的最大排序值
    const maxOrder = await prisma.gameFormField.aggregate({
      where: { gameId: targetGameId },
      _max: { sortOrder: true }
    });

    return await prisma.gameFormField.create({
      data: {
        gameId: targetGameId,
        fieldKey,
        fieldLabel: sourceField.fieldLabel,
        fieldType: sourceField.fieldType,
        isRequired: sourceField.isRequired,
        placeholder: sourceField.placeholder,
        sortOrder: (maxOrder._max.sortOrder || 0) + 1,
        options: sourceField.options === null ? undefined : (sourceField.options as string[] | undefined),
        config: sourceField.config === null ? undefined : (sourceField.config as Record<string, any> | undefined),
        isActive: true
      }
    });
  }
}

export const gameFormFieldService = new GameFormFieldService();
