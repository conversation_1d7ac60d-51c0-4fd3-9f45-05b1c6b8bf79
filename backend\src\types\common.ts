// 通用API响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  timestamp: string;
}

// 分页请求参数
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应数据
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 搜索查询参数
export interface SearchQuery extends PaginationQuery {
  keyword?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// 文件上传响应
export interface FileUploadResponse {
  filename: string;
  originalName: string;
  path: string;
  size: number;
  mimeType: string;
  url: string;
}

// 统计数据
export interface Statistics {
  totalUsers: number;
  totalOrders: number;
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  totalEarnings: number;
}

// 通知相关类型定义
export interface Notification {
  id: string;
  userId: string;
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  data?: any; // 额外的通知数据
}

export interface CreateNotificationRequest {
  userId: string;
  title: string;
  content: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  data?: any;
}

export interface NotificationQuery extends PaginationQuery {
  type?: string;
  isRead?: boolean;
  startDate?: string;
  endDate?: string;
}
