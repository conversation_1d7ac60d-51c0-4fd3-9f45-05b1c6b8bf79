import { request } from './http'
import type { Order, OrderForm, PaginatedResponse, PaginationQuery, ApiResponse, OrderStatus, OrderPriority } from '@/types'

export interface OrderQuery extends PaginationQuery {
  status?: OrderStatus
  priority?: OrderPriority
  keyword?: string
  createdById?: string
  gameId?: string
}

export interface OrderStats {
  totalOrders: number
  totalValue: number
  statusBreakdown: Record<string, number>
}

export const orderApi = {
  // 创建订单
  createOrder(data: OrderForm): Promise<ApiResponse<Order>> {
    return request.post('/orders', data)
  },

  // 获取订单列表
  getOrders(params?: OrderQuery): Promise<ApiResponse<PaginatedResponse<Order>>> {
    return request.get('/orders', { params })
  },

  // 获取我的订单
  getMyOrders(params?: OrderQuery): Promise<ApiResponse<PaginatedResponse<Order>>> {
    return request.get('/orders/my', { params })
  },

  // 获取订单详情
  getOrderById(id: string): Promise<ApiResponse<Order>> {
    return request.get(`/orders/${id}`)
  },

  // 更新订单
  updateOrder(id: string, data: Partial<OrderForm>): Promise<ApiResponse<Order>> {
    return request.put(`/orders/${id}`, data)
  },

  // 更新订单状态
  updateOrderStatus(id: string, status: string): Promise<ApiResponse<Order>> {
    return request.put(`/orders/${id}/status`, { status })
  },

  // 删除订单
  deleteOrder(id: string): Promise<ApiResponse<{ message: string }>> {
    return request.delete(`/orders/${id}`)
  },

  // 获取订单统计
  getOrderStats(): Promise<ApiResponse<OrderStats>> {
    return request.get('/orders/stats')
  }
}
