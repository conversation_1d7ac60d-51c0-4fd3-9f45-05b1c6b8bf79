<template>
  <el-dialog
    v-model="visible"
    title="Excel导出"
    width="600px"
    :before-close="handleClose"
  >
    <div class="export-dialog">
      <!-- 导出类型选择 -->
      <el-form :model="form" label-width="120px">
        <el-form-item label="导出类型">
          <el-select
            v-model="form.reportType"
            placeholder="请选择导出类型"
            style="width: 100%"
            @change="handleReportTypeChange"
          >
            <el-option
              v-for="(option, key) in availableOptions"
              :key="key"
              :label="option.name"
              :value="key"
            >
              <div>
                <div>{{ option.name }}</div>
                <div style="font-size: 12px; color: #999;">{{ option.description }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 动态参数表单 -->
        <template v-if="selectedOption">
          <!-- 年份月份选择（月度结算报表） -->
          <template v-if="form.reportType === 'monthly_settlement'">
            <el-form-item label="年份">
              <el-select v-model="form.year" placeholder="选择年份">
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="year"
                  :value="year"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="月份">
              <el-select v-model="form.month" placeholder="选择月份">
                <el-option
                  v-for="month in monthOptions"
                  :key="month.value"
                  :label="month.label"
                  :value="month.value"
                />
              </el-select>
            </el-form-item>
          </template>

          <!-- 员工选择（员工收益明细） -->
          <template v-if="form.reportType === 'employee_earnings'">
            <el-form-item label="选择员工">
              <el-select
                v-model="form.employeeId"
                placeholder="选择员工"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="employee in employees"
                  :key="employee.id"
                  :label="employee.nickname || employee.username"
                  :value="employee.id"
                />
              </el-select>
            </el-form-item>
          </template>

          <!-- 日期范围选择 -->
          <template v-if="form.reportType !== 'monthly_settlement'">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </template>
      </el-form>

      <!-- 导出说明 -->
      <div v-if="selectedOption" class="export-description">
        <el-alert
          :title="selectedOption.description"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="exporting"
          :disabled="!canExport"
          @click="handleExport"
        >
          <el-icon><Download /></el-icon>
          {{ exporting ? '导出中...' : '导出Excel' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { excelApi, downloadFile, type ExportOptions } from '@/api/excel';
import { userApi } from '@/api/users';
import { useAuthStore } from '@/stores/auth';
import type { User } from '@/types';

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const authStore = useAuthStore();
const exporting = ref(false);
const availableOptions = ref<ExportOptions>({} as ExportOptions);
const employees = ref<User[]>([]);

// 表单数据
const form = ref({
  reportType: '',
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  employeeId: '',
});

const dateRange = ref<[string, string] | null>(null);

// 计算属性
const selectedOption = computed(() => {
  return form.value.reportType ? availableOptions.value[form.value.reportType] : null;
});

const canExport = computed(() => {
  if (!form.value.reportType) return false;
  
  switch (form.value.reportType) {
    case 'monthly_settlement':
      return form.value.year && form.value.month;
    case 'employee_earnings':
      return form.value.employeeId;
    case 'order_summary':
      return true;
    default:
      return false;
  }
});

// 年份选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 2; i <= currentYear + 1; i++) {
    years.push(i);
  }
  return years;
});

// 月份选项
const monthOptions = [
  { label: '1月', value: 1 },
  { label: '2月', value: 2 },
  { label: '3月', value: 3 },
  { label: '4月', value: 4 },
  { label: '5月', value: 5 },
  { label: '6月', value: 6 },
  { label: '7月', value: 7 },
  { label: '8月', value: 8 },
  { label: '9月', value: 9 },
  { label: '10月', value: 10 },
  { label: '11月', value: 11 },
  { label: '12月', value: 12 }
];

// 方法
const handleClose = () => {
  visible.value = false;
  resetForm();
};

const resetForm = () => {
  form.value = {
    reportType: '',
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    employeeId: '',
  };
  dateRange.value = null;
};

const handleReportTypeChange = () => {
  // 重置相关字段
  form.value.employeeId = '';
  dateRange.value = null;
  
  // 如果是员工收益明细且当前用户是员工，自动选择自己
  if (form.value.reportType === 'employee_earnings' && authStore.user?.role === 'EMPLOYEE') {
    form.value.employeeId = authStore.user.id;
  }
};

const handleExport = async () => {
  try {
    exporting.value = true;
    
    let blob: Blob;
    let filename: string;
    
    switch (form.value.reportType) {
      case 'monthly_settlement':
        blob = await excelApi.exportMonthlySettlement(form.value.year, form.value.month);
        filename = `月度结算报表_${form.value.year}年${form.value.month}月.xlsx`;
        break;
        
      case 'employee_earnings':
        const employee = employees.value.find(e => e.id === form.value.employeeId);
        const employeeName = employee?.nickname || employee?.username || '员工';
        blob = await excelApi.exportEmployeeEarnings(
          form.value.employeeId,
          dateRange.value?.[0],
          dateRange.value?.[1]
        );
        filename = `${employeeName}_收益明细.xlsx`;
        break;
        
      case 'order_summary':
        blob = await excelApi.exportOrderSummary(
          dateRange.value?.[0],
          dateRange.value?.[1]
        );
        filename = `订单完成情况汇总.xlsx`;
        break;
        
      default:
        throw new Error('不支持的导出类型');
    }
    
    downloadFile(blob, filename);
    ElMessage.success('导出成功');
    handleClose();
    
  } catch (error: any) {
    console.error('导出失败:', error);
    ElMessage.error(error.message || '导出失败');
  } finally {
    exporting.value = false;
  }
};

// 获取可用导出选项
const fetchExportOptions = async () => {
  try {
    const response = await excelApi.getExportOptions();
    if (response.success) {
      availableOptions.value = response.data;
    }
  } catch (error) {
    console.error('获取导出选项失败:', error);
  }
};

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await userApi.getUsers({
      role: 'EMPLOYEE',
      status: 'ACTIVE',
      limit: 100
    });
    
    if (response.success && response.data) {
      employees.value = response.data.items;
    }
  } catch (error) {
    console.error('获取员工列表失败:', error);
  }
};

// 生命周期
onMounted(() => {
  fetchExportOptions();
  if (authStore.user?.role !== 'EMPLOYEE') {
    fetchEmployees();
  }
});

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    fetchExportOptions();
    if (authStore.user?.role !== 'EMPLOYEE') {
      fetchEmployees();
    }
  }
});
</script>

<style lang="scss" scoped>
.export-dialog {
  .export-description {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
