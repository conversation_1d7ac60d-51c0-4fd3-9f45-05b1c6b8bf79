import { ref, computed } from 'vue'
import type { FormFieldType } from '@/api/gameFormFields'
import { validateFieldConfig, type ValidateConfigData } from '@/api/gameFormFields'
import { FIELD_CONFIG } from '@/utils/common'

/**
 * 字段验证组合函数
 */
export function useFieldValidation() {
  const validating = ref(false)
  const validationResult = ref<{
    valid: boolean
    errors: string[]
    warnings: string[]
  } | null>(null)

  /**
   * 验证字段键名格式
   */
  const validateFieldKey = (fieldKey: string): boolean => {
    return FIELD_CONFIG.FIELD_KEY_PATTERN.test(fieldKey)
  }

  /**
   * 验证字段标签长度
   */
  const validateFieldLabel = (fieldLabel: string): boolean => {
    return fieldLabel.length > 0 && fieldLabel.length <= FIELD_CONFIG.MAX_FIELD_LABEL_LENGTH
  }

  /**
   * 验证占位提示长度
   */
  const validatePlaceholder = (placeholder: string): boolean => {
    return placeholder.length <= FIELD_CONFIG.MAX_PLACEHOLDER_LENGTH
  }

  /**
   * 验证排序值
   */
  const validateSortOrder = (sortOrder: number): boolean => {
    return sortOrder >= 0 && sortOrder <= FIELD_CONFIG.MAX_SORT_ORDER
  }

  /**
   * 验证选项配置
   */
  const validateOptions = (fieldType: FormFieldType, options?: string[]): boolean => {
    if (fieldType === 'SELECT' || fieldType === 'CHECKBOX') {
      return options && options.length > 0 && options.every(option => option.trim().length > 0)
    }
    return true
  }

  /**
   * 验证数字字段配置
   */
  const validateNumberConfig = (config?: Record<string, any>): boolean => {
    if (!config) return true
    
    const { min, max, step } = config
    
    if (min !== undefined && max !== undefined && min > max) {
      return false
    }
    
    if (step !== undefined && step <= 0) {
      return false
    }
    
    return true
  }

  /**
   * 验证文本字段配置
   */
  const validateTextConfig = (config?: Record<string, any>): boolean => {
    if (!config) return true
    
    const { minLength, maxLength } = config
    
    if (minLength !== undefined && maxLength !== undefined && minLength > maxLength) {
      return false
    }
    
    return true
  }

  /**
   * 验证字段配置
   */
  const validateField = async (data: ValidateConfigData): Promise<boolean> => {
    try {
      validating.value = true
      const result = await validateFieldConfig(data)
      validationResult.value = result
      return result.valid
    } catch (error) {
      console.error('字段配置验证失败:', error)
      validationResult.value = {
        valid: false,
        errors: ['验证失败，请稍后重试'],
        warnings: []
      }
      return false
    } finally {
      validating.value = false
    }
  }

  /**
   * 本地验证字段数据
   */
  const validateFieldData = (
    fieldKey: string,
    fieldLabel: string,
    fieldType: FormFieldType,
    sortOrder: number,
    placeholder?: string,
    options?: string[],
    config?: Record<string, any>
  ): { valid: boolean; errors: string[] } => {
    const errors: string[] = []

    // 验证字段键名
    if (!validateFieldKey(fieldKey)) {
      errors.push('字段键名格式不正确，只能包含字母、数字和下划线，且必须以字母开头')
    }

    // 验证字段标签
    if (!validateFieldLabel(fieldLabel)) {
      errors.push(`字段标签长度必须在1-${FIELD_CONFIG.MAX_FIELD_LABEL_LENGTH}个字符之间`)
    }

    // 验证排序值
    if (!validateSortOrder(sortOrder)) {
      errors.push(`排序值必须在0-${FIELD_CONFIG.MAX_SORT_ORDER}之间`)
    }

    // 验证占位提示
    if (placeholder && !validatePlaceholder(placeholder)) {
      errors.push(`占位提示长度不能超过${FIELD_CONFIG.MAX_PLACEHOLDER_LENGTH}个字符`)
    }

    // 验证选项
    if (!validateOptions(fieldType, options)) {
      errors.push('下拉选择框和多选框必须配置至少一个选项')
    }

    // 验证数字字段配置
    if (fieldType === 'NUMBER' && !validateNumberConfig(config)) {
      errors.push('数字字段配置不正确')
    }

    // 验证文本字段配置
    if ((fieldType === 'TEXT' || fieldType === 'TEXTAREA') && !validateTextConfig(config)) {
      errors.push('文本字段配置不正确')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 清除验证结果
   */
  const clearValidation = () => {
    validationResult.value = null
  }

  /**
   * 获取验证错误信息
   */
  const validationErrors = computed(() => {
    return validationResult.value?.errors || []
  })

  /**
   * 获取验证警告信息
   */
  const validationWarnings = computed(() => {
    return validationResult.value?.warnings || []
  })

  /**
   * 是否验证通过
   */
  const isValid = computed(() => {
    return validationResult.value?.valid ?? true
  })

  return {
    validating,
    validationResult,
    validateFieldKey,
    validateFieldLabel,
    validatePlaceholder,
    validateSortOrder,
    validateOptions,
    validateNumberConfig,
    validateTextConfig,
    validateField,
    validateFieldData,
    clearValidation,
    validationErrors,
    validationWarnings,
    isValid
  }
}
