<template>
  <el-dialog
    v-model="visible"
    title="更新任务进度"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="任务信息">
        <div class="task-info">
          <div><strong>{{ task?.taskNo }}</strong></div>
          <div class="text-gray">{{ task?.order?.customerName }}</div>
          <div class="text-gray">{{ task?.order?.currentRank }} → {{ task?.order?.targetRank }}</div>
        </div>
      </el-form-item>

      <el-form-item label="完成进度" prop="progress">
        <el-slider 
          v-model="form.progress" 
          :min="0" 
          :max="100"
          :step="5"
          show-input
          :format-tooltip="formatTooltip"
        />
      </el-form-item>



      <el-form-item label="进度说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请描述当前进度和完成情况"
        />
      </el-form-item>

      <el-form-item label="截图上传" prop="screenshots">
        <el-upload
          :file-list="fileList"
          :action="uploadUrl"
          list-type="picture-card"
          :headers="uploadHeaders"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          multiple
          :limit="5"
          name="screenshots"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        <div class="upload-tip">支持jpg、png格式，最多上传5张图片</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          更新进度
        </el-button>
      </div>
    </template>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="800px">
      <img :src="previewImageUrl" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadFile, type UploadFiles } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import type { Task } from '@/types'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue: boolean
  task: Task | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const authStore = useAuthStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const visible = ref(false)
const previewVisible = ref(false)
const previewImageUrl = ref('')
const fileList = ref<UploadFiles>([])

// 上传URL
const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1'
  return `${baseUrl}/upload/screenshots`
})

// 上传请求头
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

const form = reactive({
  progress: 0,
  description: '',
  screenshots: [] as string[]
})

// 表单验证规则
const rules: FormRules = {
  progress: [
    { required: true, message: '请设置完成进度', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入进度说明', trigger: 'blur' }
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.task) {
    // 初始化表单数据
    const latestProgress = props.task.progress?.[0]
    form.progress = latestProgress?.progress || 0
    form.description = ''
    form.screenshots = []
    fileList.value = []
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 格式化进度提示
const formatTooltip = (value: number) => {
  return `${value}%`
}

// 图片预览
const handlePictureCardPreview = (file: UploadFile) => {
  previewImageUrl.value = file.url!
  previewVisible.value = true
}

// 移除图片
const handleRemove = (file: UploadFile) => {
  const response = file.response as any
  const index = form.screenshots.findIndex(url =>
    url === response?.data?.url || url === file.url
  )
  if (index > -1) {
    form.screenshots.splice(index, 1)
  }
}

// 上传成功
const handleUploadSuccess = (response: any, file: UploadFile) => {
  if (response.success && response.data) {
    // 如果是多文件上传，response.data是数组
    if (Array.isArray(response.data)) {
      response.data.forEach((fileData: any) => {
        form.screenshots.push(fileData.url)
      })
    } else {
      form.screenshots.push(response.data.url)
    }
  } else {
    ElMessage.error('图片上传失败')
    // 移除失败的文件
    const index = fileList.value.findIndex(f => f.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }
}

// 上传失败
const handleUploadError = (error: any, file: UploadFile) => {
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败')
  // 移除失败的文件
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.task) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      loading.value = true
      
      const response = await taskApi.updateTaskProgress(props.task.id, {
        progress: form.progress,
        description: form.description,
        screenshots: form.screenshots
      })
      
      if (response.success) {
        ElMessage.success('进度更新成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('更新进度失败:', error)
    ElMessage.error('更新进度失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置表单
  formRef.value?.resetFields()
  Object.assign(form, {
    progress: 0,
    description: '',
    screenshots: []
  })
  fileList.value = []
}
</script>

<style lang="scss" scoped>
.task-info {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  .text-gray {
    color: #909399;
    font-size: 14px;
    margin-top: 4px;
  }
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
