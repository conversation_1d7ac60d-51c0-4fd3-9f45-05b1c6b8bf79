import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '../utils/logger';
import { config } from '../config/env';

// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 验证错误类
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

// 认证错误类
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401);
  }
}

// 授权错误类
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403);
  }
}

// 资源未找到错误类
export class NotFoundError extends AppError {
  constructor(message: string = '资源未找到') {
    super(message, 404);
  }
}

// 冲突错误类
export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409);
  }
}

// Prisma错误处理
function handlePrismaError(error: Prisma.PrismaClientKnownRequestError): AppError {
  switch (error.code) {
    case 'P2002':
      // 唯一约束违反
      const field = error.meta?.target as string[];
      return new ConflictError(`${field?.join(', ')} 已存在`);
    
    case 'P2025':
      // 记录未找到
      return new NotFoundError('请求的资源不存在');
    
    case 'P2003':
      // 外键约束违反
      return new ValidationError('关联的资源不存在');
    
    case 'P2014':
      // 关系违反
      return new ValidationError('操作违反了数据关系约束');
    
    default:
      return new AppError('数据库操作失败', 500);
  }
}

// 错误响应格式
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  path: string;
}

// 主错误处理中间件
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let appError: AppError;

  // 处理不同类型的错误
  if (error instanceof AppError) {
    appError = error;
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    appError = handlePrismaError(error);
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    appError = new ValidationError('数据验证失败');
  } else if (error.name === 'ValidationError') {
    appError = new ValidationError(error.message);
  } else if (error.name === 'JsonWebTokenError') {
    appError = new AuthenticationError('无效的访问令牌');
  } else if (error.name === 'TokenExpiredError') {
    appError = new AuthenticationError('访问令牌已过期');
  } else if (error.name === 'MulterError') {
    appError = new ValidationError('文件上传失败: ' + error.message);
  } else {
    // 未知错误
    appError = new AppError('服务器内部错误', 500);
  }

  // 记录错误日志
  logger.error('API错误', {
    message: appError.message,
    statusCode: appError.statusCode,
    stack: appError.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  });

  // 构建错误响应
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: appError.message,
      code: error.name,
    },
    timestamp: new Date().toISOString(),
    path: req.path,
  };

  // 开发环境返回详细错误信息
  if (config.NODE_ENV === 'development') {
    errorResponse.error.details = error;
    errorResponse.error.stack = appError.stack;
  }

  // 发送错误响应
  res.status(appError.statusCode).json(errorResponse);
}

// 异步错误处理包装器
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 404处理中间件
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new NotFoundError(`路径 ${req.originalUrl} 不存在`);
  next(error);
}
