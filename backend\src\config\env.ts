import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// 环境配置接口
interface EnvConfig {
  // 服务器配置
  NODE_ENV: string;
  PORT: number;
  HOST: string;
  
  // 数据库配置
  DATABASE_URL: string;
  
  // Redis配置
  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_PASSWORD?: string;
  
  // JWT配置
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  
  // 文件上传配置
  UPLOAD_PATH: string;
  MAX_FILE_SIZE: number;
  
  // Socket.IO配置
  SOCKET_CORS_ORIGIN: string;
  
  // 日志配置
  LOG_LEVEL: string;
  LOG_FILE: string;
  
  // API配置
  API_PREFIX: string;
  CORS_ORIGIN: string;
}

// 验证必需的环境变量
function validateEnv(): EnvConfig {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }

  return {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '3001'),
    HOST: process.env.HOST || 'localhost',
    
    DATABASE_URL: process.env.DATABASE_URL!,
    
    REDIS_HOST: process.env.REDIS_HOST || 'localhost',
    REDIS_PORT: parseInt(process.env.REDIS_PORT || '6379'),
    REDIS_PASSWORD: process.env.REDIS_PASSWORD,
    
    JWT_SECRET: process.env.JWT_SECRET!,
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    
    UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB
    
    SOCKET_CORS_ORIGIN: process.env.SOCKET_CORS_ORIGIN || 'http://localhost:5173',
    
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_FILE: process.env.LOG_FILE || './logs/app.log',
    
    API_PREFIX: process.env.API_PREFIX || '/api/v1',
    CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:5173',
  };
}

// 导出配置
export const config = validateEnv();

// 判断是否为开发环境
export const isDevelopment = config.NODE_ENV === 'development';

// 判断是否为生产环境
export const isProduction = config.NODE_ENV === 'production';

// 判断是否为测试环境
export const isTest = config.NODE_ENV === 'test';

// 环境配置已加载，详细信息通过日志系统输出
