// 颜色变量
:root {
  // 主色调
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67c23a;
  --success-light: #95d475;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #e6a23c;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #f56c6c;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景颜色
  --bg-color: #ffffff;
  --bg-page: #f5f5f5;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  // 阴影 - 现代化设计
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-elevation-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  --shadow-elevation-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
  
  // 圆角 - 现代化设计
  --border-radius-base: 8px;
  --border-radius-small: 6px;
  --border-radius-large: 12px;
  --border-radius-xl: 16px;
  --border-radius-round: 24px;
  --border-radius-circle: 50%;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  // 行高
  --line-height-base: 1.5;
  --line-height-sm: 1.25;
  --line-height-lg: 1.75;
  
  // 字重
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  // 层级
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  // 过渡动画 - 现代化设计
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-elastic: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  // 现代化渐变
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
  --gradient-danger: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);

  // 毛玻璃效果
  --backdrop-blur: blur(10px);
  --backdrop-blur-light: blur(5px);
  --backdrop-blur-heavy: blur(20px);
  --transition-fade: opacity 0.15s linear;
  --transition-collapse: height 0.35s ease;
  
  // 布局
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --footer-height: 50px;
}

// 暗色主题
[data-theme='dark'] {
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;
  
  --border-base: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2b2b2c;
  
  --bg-color: #1d1e1f;
  --bg-page: #141414;
  --bg-overlay: rgba(0, 0, 0, 0.9);
  
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.24), 0 0 6px rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  --shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.24), 0 2px 4px rgba(0, 0, 0, 0.16);
}

// 断点
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
) !default;

// 容器最大宽度
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
) !default;

// 网格系统
$grid-columns: 24 !default;
$grid-gutter-width: 20px !default;
