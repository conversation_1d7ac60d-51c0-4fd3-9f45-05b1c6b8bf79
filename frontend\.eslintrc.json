{"root": true, "env": {"browser": true, "es2021": true, "node": true}, "extends": ["plugin:vue/vue3-recommended", "eslint:recommended", "@vue/typescript/recommended", "prettier", "./.eslintrc-auto-import.json"], "parserOptions": {"ecmaVersion": 2021}, "plugins": ["vue"], "rules": {"vue/no-v-model-argument": "off", "vue/multi-word-component-names": "off", "vue/no-multiple-template-root": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off"}}