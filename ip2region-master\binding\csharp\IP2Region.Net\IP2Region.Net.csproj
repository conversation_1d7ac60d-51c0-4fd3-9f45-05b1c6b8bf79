<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <id>IP2Region.Net</id>
        <version>2.0.2</version>
        <title>IP2Region.Net</title>
        <authors><PERSON></authors>
        <PackageLicenseExpression>Apache-2.0</PackageLicenseExpression>
        <PackageReadmeFile>README.md</PackageReadmeFile>
        <PackageProjectUrl>https://github.com/lionsoul2014/ip2region/tree/master/binding/csharp</PackageProjectUrl>
        <RepositoryUrl>https://github.com/lionsoul2014/ip2region/tree/master/binding/csharp</RepositoryUrl>
        <PackageReleaseNotes>Please refer to CHANGELOG.md for details</PackageReleaseNotes>
        <Description>.NET client library for ip2region</Description>        
        <PackageTags>IP2Region GeoIP IPSearch</PackageTags>
        <RepositoryType>git</RepositoryType>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <TargetFrameworks>netstandard2.0;netstandard2.1;net6.0;net7.0</TargetFrameworks>
        <LangVersion>10.0</LangVersion>
        <UserSecretsId>c2f07fe1-a300-4de3-8200-1278ed8cb5b7</UserSecretsId>
    </PropertyGroup>
    <ItemGroup>
        <None Include="..\README.md" Pack="true" PackagePath="\" />
    </ItemGroup>
    <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
      <PackageReference Include="System.Buffers" Version="4.5.1" />
      <PackageReference Include="System.Memory" Version="4.5.5" />
    </ItemGroup>
    <ItemGroup>
      <None Include="..\CHANGELOG.md">
        <Link>CHANGELOG.md</Link>
      </None>
    </ItemGroup>
</Project>
