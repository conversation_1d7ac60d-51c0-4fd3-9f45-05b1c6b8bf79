<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BenchmarkDotNet" Version="0.13.2" />
      <PackageReference Include="BenchmarkDotNet.Annotations" Version="0.13.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\IP2Region.Net\IP2Region.Net.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\..\data\ip2region.xdb">
        <Link>IP2Region/ip2region.xdb</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

</Project>
