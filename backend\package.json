{"name": "game-boost-backend", "version": "1.0.0", "description": "王者荣耀代练任务分发管理系统 - 后端API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node --transpile-only prisma/seed.ts"}, "prisma": {"seed": "ts-node --transpile-only prisma/seed.ts"}, "keywords": ["game-boost", "task-management", "express", "typescript", "prisma"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.7.1", "@types/node-cron": "^3.0.11", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "redis": "^4.6.11", "socket.io": "^4.7.4", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.19.9", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "nodemon": "^3.0.2", "prettier": "^3.1.0", "prisma": "^5.7.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}