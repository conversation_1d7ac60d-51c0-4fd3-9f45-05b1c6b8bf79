import { prisma } from '../config/database';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';
import {
  CreateGameRankRequest,
  UpdateGameRankRequest,
  GameRankQuery
} from '../types/game';

export class GameRankService {
  // 创建游戏段位
  async createGameRank(rankData: CreateGameRankRequest) {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: rankData.gameId }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    // 检查同一游戏下段位名称是否已存在
    const existingRank = await prisma.gameRank.findUnique({
      where: {
        gameId_name: {
          gameId: rankData.gameId,
          name: rankData.name
        }
      }
    });

    if (existingRank) {
      throw new ValidationError('该游戏下段位名称已存在');
    }

    // 检查同一游戏下段位等级是否已存在
    const existingLevel = await prisma.gameRank.findFirst({
      where: {
        gameId: rankData.gameId,
        level: rankData.level
      }
    });

    if (existingLevel) {
      throw new ValidationError('该游戏下段位等级已存在');
    }

    const rank = await prisma.gameRank.create({
      data: rankData,
      include: {
        game: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        }
      }
    });

    return rank;
  }

  // 获取游戏段位列表
  async getGameRanks(query: PaginationQuery & GameRankQuery) {
    const {
      page = 1,
      limit = 50,
      sortBy = 'level',
      sortOrder = 'asc',
      gameId,
      isActive,
      minLevel,
      maxLevel
    } = query;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    
    if (gameId) {
      where.gameId = gameId;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    if (minLevel !== undefined) {
      where.level = { ...where.level, gte: minLevel };
    }
    
    if (maxLevel !== undefined) {
      where.level = { ...where.level, lte: maxLevel };
    }

    // 查询段位
    const [ranks, total] = await Promise.all([
      prisma.gameRank.findMany({
        where,
        include: {
          game: {
            select: {
              id: true,
              name: true,
              displayName: true
            }
          },
          _count: {
            select: {
              ordersFrom: true,
              ordersTo: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.gameRank.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    const result: PaginatedResponse<typeof ranks[0]> = {
      items: ranks,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return result;
  }

  // 获取指定游戏的段位列表（简化版，用于下拉选择）
  async getGameRanksByGameId(gameId: string) {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: gameId }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    return await prisma.gameRank.findMany({
      where: {
        gameId,
        isActive: true
      },
      select: {
        id: true,
        name: true,
        displayName: true,
        level: true,
        difficultyMultiplier: true,
        icon: true
      },
      orderBy: { level: 'asc' }
    });
  }

  // 获取段位详情
  async getGameRankById(id: string) {
    const rank = await prisma.gameRank.findUnique({
      where: { id },
      include: {
        game: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        },
        _count: {
          select: {
            ordersFrom: true,
            ordersTo: true
          }
        }
      }
    });

    if (!rank) {
      throw new NotFoundError('段位不存在');
    }

    return rank;
  }

  // 更新段位
  async updateGameRank(id: string, updateData: UpdateGameRankRequest) {
    // 检查段位是否存在
    const existingRank = await prisma.gameRank.findUnique({
      where: { id }
    });

    if (!existingRank) {
      throw new NotFoundError('段位不存在');
    }

    // 如果更新名称，检查是否与同游戏下其他段位冲突
    if (updateData.name && updateData.name !== existingRank.name) {
      const nameConflict = await prisma.gameRank.findUnique({
        where: {
          gameId_name: {
            gameId: existingRank.gameId,
            name: updateData.name
          }
        }
      });

      if (nameConflict) {
        throw new ValidationError('该游戏下段位名称已存在');
      }
    }

    // 如果更新等级，检查是否与同游戏下其他段位冲突
    if (updateData.level && updateData.level !== existingRank.level) {
      const levelConflict = await prisma.gameRank.findFirst({
        where: {
          gameId: existingRank.gameId,
          level: updateData.level,
          id: { not: id }
        }
      });

      if (levelConflict) {
        throw new ValidationError('该游戏下段位等级已存在');
      }
    }

    const rank = await prisma.gameRank.update({
      where: { id },
      data: updateData,
      include: {
        game: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        },
        _count: {
          select: {
            ordersFrom: true,
            ordersTo: true
          }
        }
      }
    });

    return rank;
  }

  // 删除段位
  async deleteGameRank(id: string) {
    // 检查段位是否存在
    const existingRank = await prisma.gameRank.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            ordersFrom: true,
            ordersTo: true
          }
        }
      }
    });

    if (!existingRank) {
      throw new NotFoundError('段位不存在');
    }

    // 检查是否有关联的订单
    const totalOrders = existingRank._count.ordersFrom + existingRank._count.ordersTo;
    if (totalOrders > 0) {
      throw new ValidationError('无法删除有关联订单的段位，请先处理相关订单');
    }

    await prisma.gameRank.delete({
      where: { id }
    });

    return { message: '段位删除成功' };
  }

  // 批量创建段位（用于快速初始化游戏段位）
  async batchCreateGameRanks(gameId: string, ranks: Omit<CreateGameRankRequest, 'gameId'>[]) {
    // 检查游戏是否存在
    const game = await prisma.game.findUnique({
      where: { id: gameId }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    // 检查段位名称和等级是否有重复
    const names = ranks.map(r => r.name);
    const levels = ranks.map(r => r.level);
    
    if (new Set(names).size !== names.length) {
      throw new ValidationError('段位名称不能重复');
    }
    
    if (new Set(levels).size !== levels.length) {
      throw new ValidationError('段位等级不能重复');
    }

    // 检查是否与现有段位冲突
    const existingRanks = await prisma.gameRank.findMany({
      where: { gameId },
      select: { name: true, level: true }
    });

    const existingNames = existingRanks.map(r => r.name);
    const existingLevels = existingRanks.map(r => r.level);

    const conflictNames = names.filter(name => existingNames.includes(name));
    const conflictLevels = levels.filter(level => existingLevels.includes(level));

    if (conflictNames.length > 0) {
      throw new ValidationError(`段位名称冲突: ${conflictNames.join(', ')}`);
    }

    if (conflictLevels.length > 0) {
      throw new ValidationError(`段位等级冲突: ${conflictLevels.join(', ')}`);
    }

    // 批量创建
    const createData = ranks.map(rank => ({
      ...rank,
      gameId
    }));

    const createdRanks = await prisma.gameRank.createMany({
      data: createData
    });

    return {
      message: `成功创建 ${createdRanks.count} 个段位`,
      count: createdRanks.count
    };
  }
}
