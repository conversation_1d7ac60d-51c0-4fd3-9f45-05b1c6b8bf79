### 模块三：【工作台】订单处理模块
员工在这里接收并处理“生产”出来的订单。
工作流程与前端实现
1、查看订单列表: 员工登录后，看到分配给自己的订单列表。
2、查看订单详情: 点击一个订单后，进入详情页。
前端首先请求该订单的基础信息：GET /api/orders/789。
API返回的数据中，包含了 details 这个JSON字段，以及 game_id。
Generated json
{
  "id": 789,
  "game_id": 123,
  "status": "processing",
  "details": {
    "server_region": "QQ安卓",
    "current_rank": "钻石V",
    ...
  }
}
Use code with caution.
Json
关键一步： 前端发现 game_id 是123，所以它再次请求该游戏的表单配置：GET /api/games/123/form-fields。
（优化：这个配置可以缓存，不需要每次都请求）
3、格式化展示信息:
前端现在手上有两份数据：
订单数据 (Values): details JSON对象。
表单模板 (Labels): 游戏表单配置的JSON数组。
前端遍历表单模板数组，而不是订单数据。
对于模板中的每一个字段配置 field：
从 field 中获取 field_label (如：“游戏大区”)。
从 details 中，用 field.field_key (如："server_region") 作为键，来获取对应的值 (如："QQ安卓")。
将它们配对并展示出来：<div><strong>游戏大区:</strong> QQ安卓</div>。