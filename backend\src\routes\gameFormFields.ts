import { Router } from 'express';
import { authenticateToken, requireBossOrAdmin } from '../middleware/auth';
import {
  createG<PERSON><PERSON><PERSON><PERSON>ield,
  getGameFormFields,
  getActiveFieldsByGameId,
  getGameFormFieldById,
  updateGameFormField,
  deleteGameFormField,
  updateFieldsOrder,
  copyFieldToGame,
  getFieldTypes,
  validateFieldConfig,
  getFieldUsage
} from '../controllers/gameFormFieldController';
import { validate, validateParams, validateQuery } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 字段类型选项（所有用户都可以访问）
router.get('/field-types', getFieldTypes);

// 验证字段配置（所有用户都可以访问）
router.post('/validate', validate(Joi.object({
  fieldType: Joi.string().valid('TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE').required(),
  options: Joi.array().items(Joi.string()).optional(),
  config: Joi.object().optional()
})), validateFieldConfig);

// 根据游戏ID获取活跃字段（所有用户都可以访问，用于创建订单）
router.get('/game/:gameId/active', 
  validateParams(Joi.object({ gameId: Joi.string().required() })),
  getActiveFieldsByGameId
);

// 以下路由需要老板或管理员权限
router.use(requireBossOrAdmin);

// 字段管理路由
router.post('/', 
  validate(Joi.object({
    gameId: Joi.string().required(),
    fieldKey: Joi.string().pattern(/^[a-zA-Z][a-zA-Z0-9_]*$/).max(50).required()
      .messages({
        'string.pattern.base': '字段键名只能包含字母、数字和下划线，且必须以字母开头'
      }),
    fieldLabel: Joi.string().max(255).required(),
    fieldType: Joi.string().valid('TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE').required(),
    isRequired: Joi.boolean().default(false),
    placeholder: Joi.string().max(255).optional(),
    sortOrder: Joi.number().integer().min(0).optional(),
    options: Joi.array().items(Joi.string()).when('fieldType', {
      is: Joi.string().valid('SELECT', 'CHECKBOX'),
      then: Joi.required(),
      otherwise: Joi.optional().allow(null)
    }),
    config: Joi.object().optional().allow(null),
    isActive: Joi.boolean().default(true)
  })),
  createGameFormField
);

router.get('/', 
  validateQuery(Joi.object({
    gameId: Joi.string().optional(),
    isActive: Joi.string().valid('true', 'false').optional(),
    fieldType: Joi.string().valid('TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE').optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(50)
  })),
  getGameFormFields
);

router.get('/:id',
  validateParams(Joi.object({ id: Joi.string().required() })),
  getGameFormFieldById
);

// 获取字段使用情况
router.get('/:id/usage',
  validateParams(Joi.object({ id: Joi.string().required() })),
  getFieldUsage
);

router.put('/:id',
  validateParams(Joi.object({ id: Joi.string().required() })),
  validate(Joi.object({
    fieldKey: Joi.string().pattern(/^[a-zA-Z][a-zA-Z0-9_]*$/).max(50).optional(),
    fieldLabel: Joi.string().max(255).optional(),
    fieldType: Joi.string().valid('TEXT', 'TEXTAREA', 'SELECT', 'CHECKBOX', 'NUMBER', 'PASSWORD', 'IMAGE').optional(),
    isRequired: Joi.boolean().optional(),
    placeholder: Joi.string().max(255).optional(),
    sortOrder: Joi.number().integer().min(0).optional(),
    options: Joi.array().items(Joi.string()).optional().allow(null),
    config: Joi.object().optional().allow(null),
    isActive: Joi.boolean().optional()
  })),
  updateGameFormField
);

router.delete('/:id',
  validateParams(Joi.object({ id: Joi.string().required() })),
  deleteGameFormField
);

// 批量更新字段排序
router.put('/game/:gameId/order', 
  validateParams(Joi.object({ gameId: Joi.string().required() })),
  validate(Joi.object({
    fieldOrders: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        sortOrder: Joi.number().integer().min(0).required()
      })
    ).required()
  })),
  updateFieldsOrder
);

// 复制字段到其他游戏
router.post('/:id/copy', 
  validateParams(Joi.object({ id: Joi.string().required() })),
  validate(Joi.object({
    targetGameId: Joi.string().required(),
    newFieldKey: Joi.string().pattern(/^[a-zA-Z][a-zA-Z0-9_]*$/).max(50).optional()
  })),
  copyFieldToGame
);

export default router;
