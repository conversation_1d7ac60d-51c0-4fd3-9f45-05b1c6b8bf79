<template>
  <el-dialog
    v-model="visible"
    title="编辑员工信息"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" disabled />
        <div class="form-tip">用户名不可修改</div>
      </el-form-item>

      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="form.nickname" placeholder="请输入昵称（可选）" />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号（可选）" />
      </el-form-item>



      <el-form-item label="员工等级" prop="level">
        <el-select v-model="form.level" placeholder="请选择员工等级" style="width: 100%">
          <el-option
            v-for="level in [1, 2, 3, 4, 5]"
            :key="level"
            :label="`等级 ${level}`"
            :value="level"
          >
            <div class="level-option">
              <span>等级 {{ level }}</span>
              <el-tag :type="getLevelType(level)" size="small">
                {{ getLevelDescription(level) }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="账户状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="ACTIVE">
            <el-tag type="success" size="small">激活</el-tag>
            <span class="status-desc">正常使用</span>
          </el-radio>
          <el-radio value="INACTIVE">
            <el-tag type="warning" size="small">未激活</el-tag>
            <span class="status-desc">暂停使用</span>
          </el-radio>
          <el-radio value="BANNED">
            <el-tag type="danger" size="small">已封禁</el-tag>
            <span class="status-desc">禁止使用</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="添加备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { userApi } from '@/api/users'
import type { User, UserStatus, UpdateUserData } from '@/types'

// Props
interface Props {
  modelValue: boolean
  employee: User | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  username: '',
  nickname: '',
  phone: '',
  level: 1,
  status: 'ACTIVE' as UserStatus,
  notes: ''
})

// 表单验证规则
const rules: FormRules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择员工等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择账户状态', trigger: 'change' }
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.employee) {
    loadEmployeeData()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 加载员工数据
const loadEmployeeData = () => {
  if (!props.employee) return
  
  Object.assign(form, {
    username: props.employee.username,
    nickname: props.employee.nickname || '',
    phone: props.employee.phone || '',
    level: props.employee.level,
    status: props.employee.status,
    notes: ''
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.employee) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true

      // 准备更新数据，只包含可修改的字段
      const updateData: UpdateUserData = {}
      
      if (form.nickname !== (props.employee.nickname || '')) {
        updateData.nickname = form.nickname || undefined
      }
      
      if (form.phone !== (props.employee.phone || '')) {
        updateData.phone = form.phone || undefined
      }
      

      
      if (form.level !== props.employee.level) {
        updateData.level = form.level
      }
      
      if (form.status !== props.employee.status) {
        updateData.status = form.status
      }

      // 如果没有任何变更，直接关闭
      if (Object.keys(updateData).length === 0) {
        ElMessage.info('没有检测到任何变更')
        handleClose()
        return
      }

      const response = await userApi.updateUser(props.employee.id, updateData)
      if (response.success) {
        ElMessage.success('员工信息更新成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('更新员工信息失败:', error)
    ElMessage.error('更新员工信息失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    username: '',
    nickname: '',
    phone: '',
    level: 1,
    status: 'ACTIVE',
    notes: ''
  })
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}



// 等级相关方法
const getLevelType = (level: number) => {
  if (level >= 4) return 'success'
  if (level >= 3) return 'primary'
  if (level >= 2) return 'warning'
  return 'info'
}

const getLevelDescription = (level: number) => {
  const descriptions = {
    1: '初级',
    2: '中级',
    3: '高级',
    4: '专家',
    5: '大师'
  }
  return descriptions[level] || '未知'
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--text-placeholder);
  margin-top: 4px;
}

.avatar-upload {
  display: flex;
  align-items: center;
  gap: 16px;

  .upload-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.level-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.status-desc {
  margin-left: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.dialog-footer {
  text-align: right;
}

// 单选按钮组样式优化
:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .el-radio {
    margin-right: 0;
    display: flex;
    align-items: center;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// 头像样式优化
:deep(.el-avatar) {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  color: white;
  font-weight: 600;
}

// 表单项样式优化
:deep(.el-form-item) {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 500;
    color: var(--text-primary);
  }

  .el-form-item__content {
    .el-input__wrapper {
      &.is-disabled {
        background-color: var(--bg-page);
      }
    }
  }
}

// 选择器选项样式
:deep(.el-select-dropdown) {
  .el-select-dropdown__item {
    height: auto;
    padding: 8px 20px;

    .level-option {
      line-height: 1.5;
    }
  }
}
</style>
