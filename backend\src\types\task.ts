import { AssignType, TaskStatus, OrderPriority, CommissionType } from '@prisma/client';

// 佣金计算参数
export interface CommissionCalculationParams {
  baseRate?: number;        // 基础费率
  difficultyMultiplier?: number; // 难度系数
  priorityMultiplier?: number;   // 优先级系数
  timeMultiplier?: number;       // 时间系数
  orderPrice?: number;           // 订单价格
  estimatedHours?: number;       // 预计工时

}

// 任务创建请求
export interface CreateTaskRequest {
  orderId: string;
  assigneeId?: string;
  assignType: AssignType;
  estimatedHours?: number;
  commission?: number;
  commissionType: CommissionType;
  commissionParams?: CommissionCalculationParams;
  description?: string;
  notes?: string;
}

// 任务更新请求
export interface UpdateTaskRequest {
  assigneeId?: string;
  status?: TaskStatus;
  estimatedHours?: number;
  actualHours?: number;
  commission?: number;
  commissionType?: CommissionType;
  commissionParams?: CommissionCalculationParams;
  description?: string;
  notes?: string;
}

// 任务接单请求
export interface AcceptTaskRequest {
  taskId: string;
}

// 任务进度更新请求
export interface UpdateTaskProgressRequest {
  taskId: string;
  progress: number;
  description?: string;
  screenshots?: string[];
}

// 任务审核请求
export interface ReviewTaskRequest {
  taskId: string;
  approved: boolean;
  feedback?: string;
}

// 任务查询参数
export interface TaskQuery {
  status?: TaskStatus;
  assignType?: AssignType;
  assigneeId?: string;
  orderId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 任务详情响应
export interface TaskDetailResponse {
  id: string;
  taskNo: string;
  order: {
    id: string;
    orderNo: string;
    customerName: string;
    price: number;
    priority: OrderPriority;
  };
  assignee?: {
    id: string;
    username: string;
    nickname?: string;
    level: number;
  };
  assignType: AssignType;
  status: TaskStatus;
  startTime?: Date;
  endTime?: Date;
  estimatedHours?: number;
  actualHours?: number;
  commission?: number;
  description?: string;
  notes?: string;
  progress: Array<{
    id: string;
    progress: number;
    description?: string;
    screenshots?: string[];
    createdAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}
