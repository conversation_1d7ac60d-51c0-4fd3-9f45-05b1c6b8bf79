<template>
  <AppLayout
    theme="boss"
    logo-text="管理后台"
    :menu-items="menuItems"
    :show-breadcrumb="true"
    :show-notifications="true"
  >
    <router-view />
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/components/Navigation/AppLayout.vue'
import { Document, List, User, DataAnalysis, Money, Monitor, View, Setting, Grid } from '@element-plus/icons-vue'

// 菜单配置
const menuItems = [
  {
    index: '/boss/orders',
    title: '订单管理',
    icon: Document,
    permission: ['BOSS', 'ADMIN']
  },

  {
    index: '/boss/tasks',
    title: '任务管理',
    icon: List,
    permission: ['BOSS', 'ADMIN']
  },
  {
    index: '/boss/employees',
    title: '员工管理',
    icon: User,
    permission: ['BOSS', 'ADMIN']
  },
  {
    index: '/boss/statistics',
    title: '数据统计',
    icon: DataAnalysis,
    permission: ['BOSS', 'ADMIN']
  },
  {
    index: '/boss/settlements',
    title: '结算管理',
    icon: Money,
    permission: ['BOSS', 'ADMIN']
  },
  {
    index: '/boss/games',
    title: '游戏管理',
    icon: Grid,
    permission: ['BOSS', 'ADMIN']
  },
  {
    index: 'monitoring',
    title: '系统监控',
    icon: Monitor,
    permission: ['BOSS', 'ADMIN'],
    children: [
      {
        index: '/boss/monitoring-dashboard',
        title: '监控仪表板',
        icon: DataAnalysis
      },
      {
        index: '/boss/monitoring',
        title: '在线监控',
        icon: View
      },
      {
        index: '/boss/login-logs',
        title: '登录日志',
        icon: Document
      }
    ]
  }
]
</script>
