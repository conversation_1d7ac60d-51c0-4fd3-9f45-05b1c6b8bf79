// 创建示例订单模板的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleTemplate() {
  try {
    // 获取admin用户
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });

    if (!adminUser) {
      console.error('未找到admin用户');
      return;
    }

    // 王者荣耀订单模板
    const wzryTemplate = {
      name: "王者荣耀代练",
      gameType: "wzry",
      description: "王者荣耀段位代练订单模板",
      fields: [
        {
          id: "customer_name",
          name: "customerName",
          label: "客户姓名",
          type: "text",
          required: true,
          config: {
            minLength: 2,
            maxLength: 20
          },
          display: {
            order: 1,
            group: "customer_info",
            placeholder: "请输入客户真实姓名"
          }
        },
        {
          id: "customer_contact",
          name: "customerContact",
          label: "联系方式",
          type: "text",
          required: true,
          config: {
            pattern: "^1[3-9]\\d{9}$"
          },
          display: {
            order: 2,
            group: "customer_info",
            placeholder: "请输入手机号码"
          }
        },
        {
          id: "game_account",
          name: "gameAccount",
          label: "游戏账号",
          type: "text",
          required: true,
          display: {
            order: 3,
            group: "game_info",
            placeholder: "请输入游戏账号"
          }
        },
        {
          id: "game_password",
          name: "gamePassword",
          label: "游戏密码",
          type: "text",
          required: true,
          display: {
            order: 4,
            group: "game_info",
            placeholder: "请输入游戏密码"
          }
        },
        {
          id: "game_server",
          name: "gameServer",
          label: "游戏区服",
          type: "cascader",
          required: true,
          config: {
            cascaderOptions: [
              {
                value: "qq",
                label: "QQ区",
                children: [
                  { value: "qq_1", label: "艾欧尼亚" },
                  { value: "qq_2", label: "祖安" },
                  { value: "qq_3", label: "诺克萨斯" },
                  { value: "qq_4", label: "班德尔城" }
                ]
              },
              {
                value: "wx",
                label: "微信区",
                children: [
                  { value: "wx_1", label: "王者峡谷" },
                  { value: "wx_2", label: "墨子" },
                  { value: "wx_3", label: "妲己" },
                  { value: "wx_4", label: "貂蝉" }
                ]
              }
            ]
          },
          display: {
            order: 5,
            group: "game_info"
          }
        },
        {
          id: "current_rank",
          name: "currentRank",
          label: "当前段位",
          type: "select",
          required: true,
          config: {
            options: [
              { value: "bronze", label: "青铜" },
              { value: "silver", label: "白银" },
              { value: "gold", label: "黄金" },
              { value: "platinum", label: "铂金" },
              { value: "diamond", label: "钻石" },
              { value: "master", label: "大师" },
              { value: "king", label: "王者" }
            ]
          },
          display: {
            order: 6,
            group: "rank_info"
          }
        },
        {
          id: "target_rank",
          name: "targetRank",
          label: "目标段位",
          type: "select",
          required: true,
          config: {
            options: [
              { value: "silver", label: "白银" },
              { value: "gold", label: "黄金" },
              { value: "platinum", label: "铂金" },
              { value: "diamond", label: "钻石" },
              { value: "master", label: "大师" },
              { value: "king", label: "王者" }
            ]
          },
          display: {
            order: 7,
            group: "rank_info"
          }
        },
        {
          id: "boost_type",
          name: "boostType",
          label: "代练类型",
          type: "radio",
          required: true,
          config: {
            options: [
              { value: "rank", label: "段位代练" },
              { value: "star", label: "星数代练" },
              { value: "placement", label: "定位赛代练" }
            ]
          },
          display: {
            order: 8,
            group: "service_info"
          }
        },
        {
          id: "special_requirements",
          name: "specialRequirements",
          label: "特殊要求",
          type: "checkbox",
          required: false,
          config: {
            options: [
              { value: "keep_winrate", label: "保持胜率70%以上" },
              { value: "use_specific_hero", label: "使用指定英雄" },
              { value: "night_only", label: "仅夜间代练" },
              { value: "record_video", label: "录制代练视频" }
            ]
          },
          display: {
            order: 9,
            group: "service_info"
          }
        },
        {
          id: "deadline",
          name: "deadline",
          label: "完成期限",
          type: "datetime",
          required: false,
          display: {
            order: 10,
            group: "service_info",
            helpText: "不填写则按标准时间完成"
          }
        },
        {
          id: "price",
          name: "price",
          label: "订单价格",
          type: "number",
          required: true,
          config: {
            min: 0.01,
            precision: 2
          },
          display: {
            order: 11,
            group: "price_info"
          }
        },
        {
          id: "requirements",
          name: "requirements",
          label: "其他要求",
          type: "textarea",
          required: false,
          config: {
            maxLength: 500
          },
          display: {
            order: 12,
            group: "other_info",
            placeholder: "请输入其他特殊要求"
          }
        }
      ],
      displayConfig: {
        listView: [
          { field: "customerName", width: 100, label: "客户姓名" },
          { field: "gameServer", width: 120, label: "区服" },
          { field: "currentRank", width: 80, label: "当前段位" },
          { field: "targetRank", width: 80, label: "目标段位" },
          { field: "price", width: 80, label: "价格" },
          { field: "status", width: 100, label: "状态" },
          { field: "createdAt", width: 120, label: "创建时间" }
        ],
        detailView: [
          { field: "customerName", label: "客户姓名" },
          { field: "customerContact", label: "联系方式" },
          { field: "gameAccount", label: "游戏账号" },
          { field: "gameServer", label: "游戏区服" },
          { field: "currentRank", label: "当前段位" },
          { field: "targetRank", label: "目标段位" },
          { field: "boostType", label: "代练类型" },
          { field: "specialRequirements", label: "特殊要求" },
          { field: "deadline", label: "完成期限" },
          { field: "price", label: "订单价格" },
          { field: "requirements", label: "其他要求" }
        ],
        createForm: {
          groups: [
            {
              name: "customer_info",
              label: "客户信息",
              columns: 2
            },
            {
              name: "game_info",
              label: "游戏信息",
              columns: 2
            },
            {
              name: "rank_info",
              label: "段位信息",
              columns: 2
            },
            {
              name: "service_info",
              label: "服务信息",
              columns: 1
            },
            {
              name: "price_info",
              label: "价格信息",
              columns: 1
            },
            {
              name: "other_info",
              label: "其他信息",
              columns: 1
            }
          ]
        }
      },
      businessRules: {
        priceCalculation: [
          {
            condition: "currentRank == 'bronze' && targetRank == 'silver'",
            basePrice: 50,
            multiplier: 1.0
          },
          {
            condition: "currentRank == 'silver' && targetRank == 'gold'",
            basePrice: 80,
            multiplier: 1.2
          },
          {
            condition: "currentRank == 'gold' && targetRank == 'platinum'",
            basePrice: 120,
            multiplier: 1.5
          }
        ],
        validation: [
          {
            name: "targetRankMustBeHigher",
            rule: "getRankLevel(targetRank) > getRankLevel(currentRank)",
            message: "目标段位必须高于当前段位"
          }
        ]
      }
    };

    // 创建模板
    const template = await prisma.orderTemplate.create({
      data: {
        ...wzryTemplate,
        createdById: adminUser.id
      }
    });

    console.log('✅ 王者荣耀订单模板创建成功:', template.id);

    // 创建原神订单模板
    const ysTemplate = {
      name: "原神代练",
      gameType: "ys",
      description: "原神冒险等级和任务代练订单模板",
      fields: [
        {
          id: "customer_name",
          name: "customerName",
          label: "客户姓名",
          type: "text",
          required: true,
          display: { order: 1, group: "customer_info" }
        },
        {
          id: "customer_contact",
          name: "customerContact",
          label: "联系方式",
          type: "text",
          required: true,
          display: { order: 2, group: "customer_info" }
        },
        {
          id: "game_account",
          name: "gameAccount",
          label: "游戏账号",
          type: "text",
          required: true,
          display: { order: 3, group: "game_info" }
        },
        {
          id: "game_password",
          name: "gamePassword",
          label: "游戏密码",
          type: "text",
          required: true,
          display: { order: 4, group: "game_info" }
        },
        {
          id: "current_level",
          name: "currentLevel",
          label: "当前冒险等级",
          type: "number",
          required: true,
          config: { min: 1, max: 60 },
          display: { order: 5, group: "level_info" }
        },
        {
          id: "target_level",
          name: "targetLevel",
          label: "目标冒险等级",
          type: "number",
          required: true,
          config: { min: 1, max: 60 },
          display: { order: 6, group: "level_info" }
        },
        {
          id: "service_type",
          name: "serviceType",
          label: "服务类型",
          type: "checkbox",
          required: true,
          config: {
            options: [
              { value: "level", label: "冒险等级提升" },
              { value: "quest", label: "主线任务" },
              { value: "daily", label: "每日委托" },
              { value: "domain", label: "秘境挑战" },
              { value: "boss", label: "BOSS挑战" }
            ]
          },
          display: { order: 7, group: "service_info" }
        },
        {
          id: "price",
          name: "price",
          label: "订单价格",
          type: "number",
          required: true,
          config: { min: 0.01, precision: 2 },
          display: { order: 8, group: "price_info" }
        }
      ],
      displayConfig: {
        listView: [
          { field: "customerName", width: 100 },
          { field: "currentLevel", width: 80 },
          { field: "targetLevel", width: 80 },
          { field: "serviceType", width: 120 },
          { field: "price", width: 80 },
          { field: "status", width: 100 }
        ],
        createForm: {
          groups: [
            { name: "customer_info", label: "客户信息", columns: 2 },
            { name: "game_info", label: "游戏信息", columns: 2 },
            { name: "level_info", label: "等级信息", columns: 2 },
            { name: "service_info", label: "服务信息", columns: 1 },
            { name: "price_info", label: "价格信息", columns: 1 }
          ]
        }
      }
    };

    const ysTemplateResult = await prisma.orderTemplate.create({
      data: {
        ...ysTemplate,
        createdById: adminUser.id
      }
    });

    console.log('✅ 原神订单模板创建成功:', ysTemplateResult.id);

    // 创建鸣潮订单模板
    const mcTemplate = {
      name: "鸣潮代练",
      gameType: "mc",
      description: "鸣潮游戏代练订单模板",
      fields: [
        {
          id: "customer_name",
          name: "customerName",
          label: "客户姓名",
          type: "text",
          required: true,
          display: { order: 1, group: "customer_info" }
        },
        {
          id: "customer_contact",
          name: "customerContact",
          label: "联系方式",
          type: "text",
          required: true,
          display: { order: 2, group: "customer_info" }
        },
        {
          id: "game_account",
          name: "gameAccount",
          label: "游戏账号",
          type: "text",
          required: true,
          display: { order: 3, group: "game_info" }
        },
        {
          id: "game_password",
          name: "gamePassword",
          label: "游戏密码",
          type: "text",
          required: true,
          display: { order: 4, group: "game_info" }
        },
        {
          id: "daily_checkin",
          name: "dailyCheckin",
          label: "每日签到",
          type: "checkbox",
          required: false,
          defaultValue: false,
          config: {
            helpText: "是否需要每日签到服务"
          },
          display: {
            order: 5,
            group: "service_info",
            helpText: "勾选此项将包含每日签到服务"
          }
        },
        {
          id: "service_type",
          name: "serviceType",
          label: "服务类型",
          type: "select",
          required: true,
          config: {
            options: [
              { value: "level_boost", label: "等级提升" },
              { value: "quest_complete", label: "任务完成" },
              { value: "resource_farm", label: "资源收集" },
              { value: "daily_tasks", label: "日常任务" }
            ]
          },
          display: { order: 6, group: "service_info" }
        },
        {
          id: "current_level",
          name: "currentLevel",
          label: "当前等级",
          type: "number",
          required: true,
          config: { min: 1, max: 100 },
          display: { order: 7, group: "level_info" }
        },
        {
          id: "target_level",
          name: "targetLevel",
          label: "目标等级",
          type: "number",
          required: true,
          config: { min: 1, max: 100 },
          display: { order: 8, group: "level_info" }
        },
        {
          id: "price",
          name: "price",
          label: "订单价格",
          type: "number",
          required: true,
          config: { min: 0.01, precision: 2 },
          display: { order: 9, group: "price_info" }
        }
      ],
      displayConfig: {
        listView: [
          { field: "customerName", width: 100 },
          { field: "currentLevel", width: 80 },
          { field: "targetLevel", width: 80 },
          { field: "serviceType", width: 120 },
          { field: "dailyCheckin", width: 80 },
          { field: "price", width: 80 },
          { field: "status", width: 100 }
        ],
        detailView: [
          { field: "customerName", label: "客户姓名" },
          { field: "customerContact", label: "联系方式" },
          { field: "gameAccount", label: "游戏账号" },
          { field: "currentLevel", label: "当前等级" },
          { field: "targetLevel", label: "目标等级" },
          { field: "serviceType", label: "服务类型" },
          { field: "dailyCheckin", label: "每日签到" },
          { field: "price", label: "订单价格" }
        ],
        createForm: {
          groups: [
            { name: "customer_info", label: "客户信息", columns: 2 },
            { name: "game_info", label: "游戏信息", columns: 2 },
            { name: "level_info", label: "等级信息", columns: 2 },
            { name: "service_info", label: "服务信息", columns: 1 },
            { name: "price_info", label: "价格信息", columns: 1 }
          ]
        }
      }
    };

    const mcTemplateResult = await prisma.orderTemplate.create({
      data: {
        ...mcTemplate,
        createdById: adminUser.id
      }
    });

    console.log('✅ 鸣潮订单模板创建成功:', mcTemplateResult.id);

  } catch (error) {
    console.error('❌ 创建示例模板失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleTemplate();
