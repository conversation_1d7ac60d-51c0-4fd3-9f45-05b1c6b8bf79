import winston from 'winston';
import path from 'path';
import { config } from '../config/env';

// 创建日志目录
import fs from 'fs';
const logDir = path.dirname(config.LOG_FILE);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack) {
      log += `\n${stack}`;
    }
    return log;
  })
);

// 创建logger实例
export const logger = winston.createLogger({
  level: config.LOG_LEVEL,
  format: logFormat,
  defaultMeta: { service: 'game-boost-api' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: config.LOG_FILE,
      maxsize: 5242880, // 5MB
      maxFiles: 10,
    }),
  ],
});

// 开发环境添加控制台输出
if (config.NODE_ENV === 'development') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
  }));
}

// 生产环境添加更多传输方式
if (config.NODE_ENV === 'production') {
  // 可以添加其他传输方式，如发送到日志服务
}

// 导出设置函数
export function setupLogger() {
  // 处理未捕获的异常
  logger.exceptions.handle(
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
    })
  );

  // 处理未处理的Promise拒绝
  logger.rejections.handle(
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
    })
  );

  return logger;
}

// 创建不同级别的日志函数
export const log = {
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  
  // 特殊用途的日志
  auth: (message: string, userId?: string, meta?: any) => 
    logger.info(`[AUTH] ${message}`, { userId, ...meta }),
  
  api: (method: string, url: string, userId?: string, meta?: any) =>
    logger.info(`[API] ${method} ${url}`, { userId, ...meta }),
  
  db: (message: string, meta?: any) =>
    logger.info(`[DB] ${message}`, meta),
  
  socket: (message: string, socketId?: string, meta?: any) =>
    logger.info(`[SOCKET] ${message}`, { socketId, ...meta }),
};

export default logger;
