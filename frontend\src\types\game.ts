// 游戏相关类型定义

// 表单字段类型
export type FormFieldType = 'TEXT' | 'TEXTAREA' | 'SELECT' | 'CHECKBOX' | 'NUMBER' | 'PASSWORD' | 'IMAGE';

// 游戏表单字段接口
export interface GameFormField {
  id: string;
  gameId: string;
  fieldKey: string;
  fieldLabel: string;
  fieldType: FormFieldType;
  isRequired: boolean;
  placeholder?: string;
  sortOrder: number;
  options?: string[];
  config?: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  game?: {
    id: string;
    name: string;
    displayName: string;
  };
}

export interface Game {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  icon?: string;
  isActive: boolean;
  sortOrder: number;
  config?: GameConfig;
  createdAt: string;
  updatedAt: string;
  ranks?: GameRank[];
  priceRules?: GamePriceRule[];
  _count?: {
    orders: number;
    employeeSkills: number;
    formFields: number;
  };
  formFields?: GameFormField[];
}

export interface GameConfig {
  maxRankJump?: number;      // 最大段位跨越
  seasonDuration?: number;   // 赛季持续天数
  features?: string[];       // 游戏特色功能
  [key: string]: any;        // 其他自定义配置
}

export interface GameRank {
  id: string;
  gameId: string;
  name: string;
  displayName: string;
  level: number;
  difficultyMultiplier: number;
  icon?: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  game?: {
    id: string;
    name: string;
    displayName: string;
  };
  _count?: {
    ordersFrom: number;
    ordersTo: number;
  };
}

export interface GamePriceRule {
  id: string;
  gameId: string;
  name: string;
  description?: string;
  ruleType: PriceRuleType;
  basePrice: number;
  pricePerLevel: number;
  multiplier: number;
  minLevel?: number;
  maxLevel?: number;
  priority?: OrderPriority;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  game?: {
    id: string;
    name: string;
    displayName: string;
  };
}

export interface EmployeeGameSkill {
  id: string;
  userId: string;
  gameId: string;
  skillLevel: SkillLevel;
  maxRankLevel?: number;
  isActive: boolean;
  certifiedAt?: string;
  certifiedBy?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    username: string;
    nickname?: string;
  };
  game?: {
    id: string;
    name: string;
    displayName: string;
  };
  certifier?: {
    id: string;
    username: string;
    nickname?: string;
  };
}

// 枚举类型
export enum PriceRuleType {
  RANK_BASED = 'RANK_BASED',    // 基于段位
  TIME_BASED = 'TIME_BASED',    // 基于时间
  FIXED_PRICE = 'FIXED_PRICE',  // 固定价格
  CUSTOM = 'CUSTOM'             // 自定义规则
}

export enum SkillLevel {
  BEGINNER = 'BEGINNER',        // 初级
  INTERMEDIATE = 'INTERMEDIATE', // 中级
  ADVANCED = 'ADVANCED',        // 高级
  EXPERT = 'EXPERT',            // 专家
  MASTER = 'MASTER'             // 大师
}

export enum OrderPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 创建游戏请求
export interface CreateGameRequest {
  name: string;
  displayName: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
  sortOrder?: number;
  config?: GameConfig;
}

// 更新游戏请求
export interface UpdateGameRequest {
  name?: string;
  displayName?: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
  sortOrder?: number;
  config?: GameConfig;
}

// 创建游戏段位请求
export interface CreateGameRankRequest {
  gameId: string;
  name: string;
  displayName: string;
  level: number;
  difficultyMultiplier?: number;
  icon?: string;
  description?: string;
  isActive?: boolean;
}

// 更新游戏段位请求
export interface UpdateGameRankRequest {
  name?: string;
  displayName?: string;
  level?: number;
  difficultyMultiplier?: number;
  icon?: string;
  description?: string;
  isActive?: boolean;
}

// 游戏统计信息
export interface GameStatistics {
  gameId: string;
  gameName: string;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completionRate: number;
  popularRanks: {
    rankName: string;
    orderCount: number;
  }[];
  monthlyTrend: {
    month: string;
    orderCount: number;
    revenue: number;
  }[];
}

// 价格计算结果
export interface PriceCalculationResult {
  basePrice: number;
  levelDifference: number;
  difficultyMultiplier: number;
  priorityMultiplier: number;
  finalPrice: number;
  appliedRules: string[];
}

// 游戏热度分析
export interface GameHotness {
  gameId: string;
  gameName: string;
  orderCount: number;
  revenue: number;
  averageCompletionTime: number;
  employeeCount: number;
  hotnesScore: number;
  trend: 'rising' | 'stable' | 'declining';
}

// 员工技能匹配结果
export interface EmployeeSkillMatch {
  userId: string;
  username: string;
  nickname?: string;
  skillLevel: SkillLevel;
  maxRankLevel?: number;
  matchScore: number;
  completedTasks: number;
  averageRating: number;
}

// 查询参数
export interface GameQuery {
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'displayName' | 'sortOrder' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  isActive?: boolean;
}

export interface GameRankQuery {
  page?: number;
  limit?: number;
  sortBy?: 'level' | 'name' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  gameId?: string;
  isActive?: boolean;
  minLevel?: number;
  maxLevel?: number;
}

// 简化的游戏信息（用于下拉选择）
export interface SimpleGame {
  id: string;
  name: string;
  displayName: string;
  icon?: string;
}

// 简化的段位信息（用于下拉选择）
export interface SimpleGameRank {
  id: string;
  name: string;
  displayName: string;
  level: number;
  difficultyMultiplier: number;
  icon?: string;
}

// 技能等级选项
export const SKILL_LEVEL_OPTIONS = [
  { value: SkillLevel.BEGINNER, label: '初级', color: '#909399' },
  { value: SkillLevel.INTERMEDIATE, label: '中级', color: '#409EFF' },
  { value: SkillLevel.ADVANCED, label: '高级', color: '#67C23A' },
  { value: SkillLevel.EXPERT, label: '专家', color: '#E6A23C' },
  { value: SkillLevel.MASTER, label: '大师', color: '#F56C6C' }
];

// 价格规则类型选项
export const PRICE_RULE_TYPE_OPTIONS = [
  { value: PriceRuleType.RANK_BASED, label: '基于段位' },
  { value: PriceRuleType.TIME_BASED, label: '基于时间' },
  { value: PriceRuleType.FIXED_PRICE, label: '固定价格' },
  { value: PriceRuleType.CUSTOM, label: '自定义规则' }
];

// 订单优先级选项
export const ORDER_PRIORITY_OPTIONS = [
  { value: OrderPriority.LOW, label: '低', color: '#909399' },
  { value: OrderPriority.NORMAL, label: '普通', color: '#409EFF' },
  { value: OrderPriority.HIGH, label: '高', color: '#E6A23C' },
  { value: OrderPriority.URGENT, label: '紧急', color: '#F56C6C' }
];
