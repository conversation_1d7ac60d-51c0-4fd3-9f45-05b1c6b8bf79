import { Router } from 'express';
import {
  healthCheck,
  detailedHealthCheck,
  readinessCheck,
  livenessCheck,
} from '../controllers/healthController';

const router = Router();

// 基础健康检查
router.get('/', healthCheck);

// 详细健康检查
router.get('/detailed', detailedHealthCheck);

// 就绪检查（Kubernetes readiness probe）
router.get('/ready', readinessCheck);

// 存活检查（Kubernetes liveness probe）
router.get('/live', livenessCheck);

export default router;
