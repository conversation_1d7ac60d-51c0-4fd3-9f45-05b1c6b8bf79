-- AlterTable
ALTER TABLE `orders` ADD COLUMN `formData` J<PERSON>N NULL,
    ADD COLUMN `templateId` VARCHAR(191) NULL,
    ADD COLUMN `templateVersion` INTEGER NULL;

-- CreateTable
CREATE TABLE `order_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `gameType` VARCHAR(191) NOT NULL,
    `version` INTEGER NOT NULL DEFAULT 1,
    `status` ENUM('ACTIVE', 'INACTIVE', 'DRAFT') NOT NULL DEFAULT 'ACTIVE',
    `description` VARCHAR(191) NULL,
    `fields` JSON NOT NULL,
    `displayConfig` JSON NOT NULL,
    `businessRules` JSON NULL,
    `createdById` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `order_templates_gameType_status_idx`(`gameType`, `status`),
    UNIQUE INDEX `order_templates_gameType_version_key`(`gameType`, `version`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `order_templates` ADD CONSTRAINT `order_templates_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_templateId_fkey` FOREIGN KEY (`templateId`) REFERENCES `order_templates`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
