/**
 * 修复订单模板数据的脚本
 * 修复原神模板中的异常字段标签
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixTemplateData() {
  try {
    console.log('🔧 开始修复订单模板数据...\n');

    // 获取原神模板
    const ysTemplate = await prisma.orderTemplate.findFirst({
      where: {
        gameType: 'ys',
        status: 'ACTIVE'
      }
    });

    if (!ysTemplate) {
      console.log('❌ 未找到原神活跃模板');
      return;
    }

    console.log(`📝 找到原神模板: ${ysTemplate.name} (${ysTemplate.id})`);
    console.log('🔍 当前字段配置:');
    
    ysTemplate.fields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.label} (${field.name}) - ${field.type}`);
    });

    // 修复字段标签
    const fixedFields = ysTemplate.fields.map(field => {
      const fixed = { ...field };
      
      // 修复异常标签
      switch (field.label) {
        case '123':
          fixed.label = '联系方式';
          fixed.name = 'customerContact';
          break;
        case '测试1':
          fixed.label = '游戏区服';
          fixed.name = 'gameServer';
          break;
        case '你好':
          fixed.label = '特殊要求';
          fixed.name = 'specialRequirements';
          fixed.type = 'textarea';
          break;
        case '客户名称':
          // 统一字段名
          fixed.name = 'customerName';
          break;
        case '客户姓名':
          // 这是重复字段，改为备注
          fixed.label = '备注信息';
          fixed.name = 'notes';
          fixed.required = false;
          break;
        case '订单价格':
          fixed.name = 'price';
          break;
        case '游戏账号':
          fixed.name = 'gameAccount';
          break;
        case '游戏密码':
          fixed.name = 'gamePassword';
          break;
      }
      
      return fixed;
    });

    console.log('\n🔧 修复后的字段配置:');
    fixedFields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.label} (${field.name}) - ${field.type} ${field.required ? '[必填]' : '[可选]'}`);
    });

    // 更新模板
    const updatedTemplate = await prisma.orderTemplate.update({
      where: {
        id: ysTemplate.id
      },
      data: {
        fields: fixedFields,
        updatedAt: new Date()
      }
    });

    console.log('\n✅ 原神模板修复完成');

    // 同时为孤儿订单分配正确的模板ID
    console.log('\n🔗 修复孤儿订单...');
    
    const orphanOrders = await prisma.order.findMany({
      where: {
        gameType: 'ys',
        templateId: null
      }
    });

    if (orphanOrders.length > 0) {
      console.log(`📦 找到 ${orphanOrders.length} 个原神孤儿订单`);
      
      for (const order of orphanOrders) {
        await prisma.order.update({
          where: {
            id: order.id
          },
          data: {
            templateId: ysTemplate.id
          }
        });
        console.log(`  ✅ 订单 ${order.id} 已关联到模板`);
      }
    }

    console.log('\n🎉 所有修复完成！');

  } catch (error) {
    console.error('❌ 修复模板数据时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复
fixTemplateData();
