<template>
  <div class="game-management">
    <div class="page-header">
      <h2>游戏管理</h2>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加游戏
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>


    </div>

    <!-- 游戏列表 -->
    <el-card>
      <el-table
        ref="tableRef"
        :data="gameStore.games"
        :loading="gameStore.gamesLoading"
        stripe
        style="width: 100%"
        row-key="id"
        class="sortable-table"
      >
        <el-table-column label="拖拽" width="60" align="center">
          <template #default>
            <el-icon style="cursor: move; color: var(--el-text-color-placeholder);">
              <svg viewBox="0 0 1024 1024" width="16" height="16">
                <path d="M384 128h256v128H384V128z m0 256h256v128H384V384z m0 256h256v128H384V640z" fill="currentColor"/>
              </svg>
            </el-icon></template>
        </el-table-column>

        <el-table-column prop="displayName" label="游戏名称" min-width="150">
          <template #default="{ row }">
            <div class="game-info">
              <img v-if="row.icon" :src="row.icon" class="game-icon" />
              <div>
                <div class="game-name">{{ row.displayName }}</div>
                <div class="game-code">{{ row.name }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column label="订单数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="success">{{ row._count?.orders || 0 }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="表单字段" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="primary">{{ row._count?.formFields || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleToggleStatus(row)"
              :loading="loading"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
              :disabled="row._count?.orders > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="gameStore.gamesPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑游戏对话框 -->
    <GameEditDialog
      v-model="showCreateDialog"
      :game-id="editingGameId"
      @success="handleDialogSuccess"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { useGameStore } from '@/stores/game';
import GameEditDialog from '@/components/GameEditDialog.vue';
import type { Game } from '@/types/game';
import Sortable from 'sortablejs';

const gameStore = useGameStore();
const tableRef = ref();

// 响应式数据
const loading = ref(false);
const showCreateDialog = ref(false);
const editingGameId = ref<string>('');

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);

// 方法
const fetchGames = async () => {
  try {
    console.log('🔍 开始获取游戏列表...');
    const result = await gameStore.fetchGames({
      page: currentPage.value,
      limit: pageSize.value,
      sortBy: 'sortOrder',
      sortOrder: 'asc'
    });
    console.log('🎮 游戏列表获取成功:', result);
    console.log('🎮 当前游戏数据:', gameStore.games);
    console.log('🎮 分页信息:', gameStore.gamesPagination);
  } catch (error) {
    console.error('❌ 获取游戏列表失败:', error);
    ElMessage.error('获取游戏列表失败');
  }
};

const handleRefresh = () => {
  fetchGames();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchGames();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchGames();
};

const handleToggleStatus = async (game: Game) => {
  try {
    loading.value = true;
    await gameStore.updateGame(game.id, { isActive: game.isActive });
    ElMessage.success(`游戏已${game.isActive ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('更新游戏状态失败:', error);
    ElMessage.error('更新游戏状态失败');
    // 恢复原状态
    game.isActive = !game.isActive;
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (game: Game) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除游戏"${game.displayName}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    loading.value = true;
    await gameStore.deleteGame(game.id);
    ElMessage.success('游戏删除成功');
    await fetchGames();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除游戏失败:', error);
      ElMessage.error('删除游戏失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleEdit = (game: Game) => {
  editingGameId.value = game.id;
  showCreateDialog.value = true;
};

const handleDialogSuccess = () => {
  showCreateDialog.value = false;
  editingGameId.value = '';
  fetchGames();
};

// 初始化拖拽排序
const initSortable = () => {
  nextTick(() => {
    if (tableRef.value && tableRef.value.$el) {
      const tbody = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
      if (tbody) {
        Sortable.create(tbody, {
          animation: 150,
          ghostClass: 'sortable-ghost',
          onEnd: async (evt) => {
            const { oldIndex, newIndex } = evt;
            if (oldIndex !== newIndex && oldIndex !== undefined && newIndex !== undefined) {
              // 更新本地数据
              const games = [...gameStore.games];
              const movedItem = games.splice(oldIndex, 1)[0];
              games.splice(newIndex, 0, movedItem);

              // 重新计算sortOrder
              const updates = games.map((game, index) => ({
                id: game.id,
                sortOrder: index + 1
              }));

              try {
                // 批量更新排序
                await updateGamesSortOrder(updates);
                ElMessage.success('排序更新成功');
                await fetchGames(); // 重新获取数据
              } catch (error) {
                console.error('更新排序失败:', error);
                ElMessage.error('更新排序失败');
                await fetchGames(); // 恢复原状态
              }
            }
          }
        });
      }
    }
  });
};

// 批量更新游戏排序
const updateGamesSortOrder = async (updates: { id: string; sortOrder: number }[]) => {
  // 这里需要调用API批量更新排序，暂时逐个更新
  for (const update of updates) {
    await gameStore.updateGame(update.id, { sortOrder: update.sortOrder });
  }
};

// 生命周期
onMounted(async () => {
  await fetchGames();
  initSortable();
});
</script>

<style lang="scss" scoped>
.game-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
  }

  p {
    margin: 0;
    color: var(--el-text-color-regular);
  }
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.game-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.game-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.game-code {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

// 拖拽排序样式
.sortable-table {
  :deep(.el-table__body-wrapper tbody tr) {
    cursor: move;
  }

  :deep(.sortable-ghost) {
    opacity: 0.5;
    background-color: var(--el-color-primary-light-9);
  }
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
