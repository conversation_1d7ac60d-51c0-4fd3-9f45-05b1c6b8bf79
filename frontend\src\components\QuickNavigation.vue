<template>
  <div class="quick-navigation">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
        <router-link v-if="item.path && !item.current" :to="item.path" class="breadcrumb-link">
          {{ item.name }}
        </router-link>
        <span v-else class="breadcrumb-current">{{ item.name }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 快捷操作按钮 -->
    <div class="quick-actions" v-if="showActions">
      <el-button 
        v-if="showHomeButton"
        type="primary" 
        size="small"
        @click="goToHome"
        :icon="HomeFilled"
      >
        {{ homeButtonText }}
      </el-button>
      
      <el-button 
        v-if="showBackButton"
        @click="goBack"
        :icon="ArrowLeft"
        size="small"
      >
        返回
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { HomeFilled, ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

interface BreadcrumbItem {
  name: string
  path?: string
  current?: boolean
}

interface Props {
  // 自定义面包屑项目
  customBreadcrumb?: BreadcrumbItem[]
  // 是否显示操作按钮
  showActions?: boolean
  // 是否显示首页按钮
  showHomeButton?: boolean
  // 是否显示返回按钮
  showBackButton?: boolean
  // 自定义首页按钮文字
  customHomeText?: string
  // 自定义返回路径
  customBackPath?: string
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  showHomeButton: true,
  showBackButton: false,
  customHomeText: '',
  customBackPath: ''
})

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  if (props.customBreadcrumb) {
    return props.customBreadcrumb
  }

  // 自动生成面包屑
  const items: BreadcrumbItem[] = []
  
  // 添加首页
  const homePath = authStore.getRedirectPath()
  const homeName = getHomePageName()
  items.push({
    name: homeName,
    path: homePath
  })

  // 添加当前页面
  if (route.meta.title) {
    items.push({
      name: route.meta.title as string,
      current: true
    })
  }

  return items
})

// 首页按钮文字
const homeButtonText = computed(() => {
  if (props.customHomeText) {
    return props.customHomeText
  }
  return `返回${getHomePageName()}`
})

// 获取首页名称
const getHomePageName = () => {
  if (!authStore.user) return '首页'
  
  switch (authStore.user.role) {
    case 'BOSS':
    case 'ADMIN':
      return '订单管理'
    case 'EMPLOYEE':
      return '可接单任务'
    default:
      return '首页'
  }
}

// 跳转到首页
const goToHome = () => {
  const homePath = authStore.getRedirectPath()
  router.push(homePath)
}

// 返回上一页
const goBack = () => {
  if (props.customBackPath) {
    router.push(props.customBackPath)
  } else {
    router.back()
  }
}
</script>

<style lang="scss" scoped>
.quick-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-lighter);

  .breadcrumb {
    flex: 1;

    :deep(.el-breadcrumb__item) {
      .el-breadcrumb__inner {
        color: var(--text-secondary);
        font-weight: 500;
        transition: color 0.3s ease;

        &:hover {
          color: var(--primary-color);
        }
      }

      &:last-child .el-breadcrumb__inner {
        color: var(--text-primary);
        font-weight: 600;
      }
    }

    .breadcrumb-link {
      color: var(--text-secondary);
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: var(--primary-color);
      }
    }

    .breadcrumb-current {
      color: var(--text-primary);
      font-weight: 600;
    }
  }

  .quick-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;

    .el-button {
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .quick-navigation {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);

    .breadcrumb {
      :deep(.el-breadcrumb) {
        font-size: var(--font-size-sm);
      }
    }

    .quick-actions {
      justify-content: flex-start;

      .el-button {
        flex: 1;
        max-width: 150px;
      }
    }
  }
}

@media (max-width: 480px) {
  .quick-navigation {
    .quick-actions {
      .el-button {
        max-width: none;
        min-width: 100px;
      }
    }
  }
}
</style>
