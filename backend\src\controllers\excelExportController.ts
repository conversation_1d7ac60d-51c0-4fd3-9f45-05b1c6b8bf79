import { Request, Response } from 'express';
import { ExcelExportService } from '../services/excelExportService';
import { ApiResponse } from '../types/common';
import dayjs from 'dayjs';

const excelExportService = new ExcelExportService();

/**
 * 导出月度结算报表
 */
export const exportMonthlySettlement = async (req: Request, res: Response): Promise<void> => {
  try {
    const { year, month } = req.query;
    const createdById = req.user?.role === 'BOSS' ? req.user.id : undefined;

    // 参数验证
    if (!year || !month) {
      res.status(400).json({
        success: false,
        message: '请提供年份和月份参数',
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }
    
    const yearNum = parseInt(year as string);
    const monthNum = parseInt(month as string);
    
    if (yearNum < 2020 || yearNum > 2030 || monthNum < 1 || monthNum > 12) {
      res.status(400).json({
        success: false,
        message: '年份或月份参数无效',
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }
    
    // 生成Excel文件
    const workbook = await excelExportService.exportMonthlySettlement(yearNum, monthNum, createdById);
    
    // 设置响应头
    const filename = `月度结算报表_${yearNum}年${monthNum}月_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    
    // 写入响应
    await workbook.xlsx.write(res);
    res.end();
    
  } catch (error: any) {
    console.error('导出月度结算报表失败:', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    } as ApiResponse);
  }
};

/**
 * 导出员工个人收益明细
 */
export const exportEmployeeEarnings = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId } = req.params;
    const { startDate, endDate } = req.query;
    
    // 参数验证
    if (!employeeId) {
      res.status(400).json({
        success: false,
        message: '请提供员工ID',
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }
    
    // 默认查询当前月
    const start = startDate ? dayjs(startDate as string).toDate() : dayjs().startOf('month').toDate();
    const end = endDate ? dayjs(endDate as string).toDate() : dayjs().endOf('month').toDate();
    
    // 权限检查：员工只能查看自己的，老板和管理员可以查看所有
    if (req.user?.role === 'EMPLOYEE' && req.user.id !== employeeId) {
      res.status(403).json({
        success: false,
        message: '权限不足',
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }
    
    // 生成Excel文件
    const workbook = await excelExportService.exportEmployeeEarnings(employeeId, start, end);
    
    // 设置响应头
    const filename = `员工收益明细_${dayjs(start).format('YYYYMM')}_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    
    // 写入响应
    await workbook.xlsx.write(res);
    res.end();
    
  } catch (error) {
    console.error('导出员工收益明细失败:', error);
    res.status(500).json({
      success: false,
      message: (error as Error)?.message || '导出失败',
      timestamp: new Date().toISOString()
    } as ApiResponse);
  }
};

/**
 * 导出订单完成情况汇总
 */
export const exportOrderSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;
    const createdById = req.user?.role === 'BOSS' ? req.user.id : undefined;
    
    // 默认查询当前月
    const start = startDate ? dayjs(startDate as string).toDate() : dayjs().startOf('month').toDate();
    const end = endDate ? dayjs(endDate as string).toDate() : dayjs().endOf('month').toDate();
    
    // 生成Excel文件
    const workbook = await excelExportService.exportOrderSummary(start, end, createdById);
    
    // 设置响应头
    const filename = `订单完成情况汇总_${dayjs(start).format('YYYYMM')}_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    
    // 写入响应
    await workbook.xlsx.write(res);
    res.end();
    
  } catch (error) {
    console.error('导出订单汇总失败:', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    } as ApiResponse);
  }
};

/**
 * 导出自定义报表
 */
export const exportCustomReport = async (req: Request, res: Response): Promise<void> => {
  try {
    const { reportType, startDate, endDate } = req.body;
    
    // 参数验证
    if (!reportType) {
      res.status(400).json({
        success: false,
        message: '请指定报表类型',
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }
    
    const start = startDate ? dayjs(startDate).toDate() : dayjs().startOf('month').toDate();
    const end = endDate ? dayjs(endDate).toDate() : dayjs().endOf('month').toDate();
    
    let workbook;
    let filename;
    
    switch (reportType) {
      case 'monthly_settlement':
        const year = dayjs(start).year();
        const month = dayjs(start).month() + 1;
        const createdById = req.user?.role === 'BOSS' ? req.user.id : undefined;
        workbook = await excelExportService.exportMonthlySettlement(year, month, createdById);
        filename = `月度结算报表_${year}年${month}月_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
        break;
        
      case 'order_summary':
        const orderCreatedById = req.user?.role === 'BOSS' ? req.user.id : undefined;
        workbook = await excelExportService.exportOrderSummary(start, end, orderCreatedById);
        filename = `订单汇总_${dayjs(start).format('YYYYMM')}_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
        break;
        
      default:
        res.status(400).json({
          success: false,
          message: '不支持的报表类型',
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
    }
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    
    // 写入响应
    await workbook.xlsx.write(res);
    res.end();
    
  } catch (error) {
    console.error('导出自定义报表失败:', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    } as ApiResponse);
  }
};
