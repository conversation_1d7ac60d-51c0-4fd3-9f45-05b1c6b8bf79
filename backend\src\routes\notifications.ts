import { Router, Request, Response } from 'express';
import Joi from 'joi';
import {
  getNotifications,
  createNotification,
  getNotificationById,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  getUnreadCount,
  cleanupOldNotifications,
} from '../controllers/notificationController';
import { getSocketService } from '../services/socketService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import { validate, validateQuery, validateParams } from '../utils/validation';

const router = Router();

// 验证schemas
const notificationSchemas = {
  create: Joi.object({
    userId: Joi.string().required().messages({
      'string.empty': '用户ID不能为空',
      'any.required': '用户ID是必填项',
    }),
    title: Joi.string().required().max(255).messages({
      'string.empty': '标题不能为空',
      'string.max': '标题长度不能超过255个字符',
      'any.required': '标题是必填项',
    }),
    content: Joi.string().required().messages({
      'string.empty': '内容不能为空',
      'any.required': '内容是必填项',
    }),
    type: Joi.string().valid('INFO', 'SUCCESS', 'WARNING', 'ERROR').default('INFO').messages({
      'any.only': '通知类型必须是 INFO、SUCCESS、WARNING 或 ERROR 之一',
    }),
    data: Joi.any().optional(),
  }),
};

const notificationQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  type: Joi.string().valid('INFO', 'SUCCESS', 'WARNING', 'ERROR').optional(),
  isRead: Joi.boolean().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  sortBy: Joi.string().valid('createdAt', 'title', 'type').default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
});

const idParamSchema = Joi.object({
  id: Joi.string().required().messages({
    'string.empty': 'ID不能为空',
    'any.required': 'ID是必填项',
  }),
});

// 获取当前用户的通知列表
router.get('/', 
  authenticateToken, 
  validateQuery(notificationQuerySchema), 
  getNotifications
);

// 获取未读通知数量
router.get('/unread-count', 
  authenticateToken, 
  getUnreadCount
);

// 标记所有通知为已读
router.post('/mark-all-read', 
  authenticateToken, 
  markAllNotificationsAsRead
);

// 创建通知（管理员功能）
router.post('/', 
  authenticateToken, 
  requireAdmin,
  validate(notificationSchemas.create), 
  createNotification
);

// 清理过期通知（管理员功能）
router.post('/cleanup', 
  authenticateToken, 
  requireAdmin,
  cleanupOldNotifications
);

// 获取通知详情
router.get('/:id', 
  authenticateToken, 
  validateParams(idParamSchema),
  getNotificationById
);

// 标记通知为已读
router.put('/:id/read', 
  authenticateToken, 
  validateParams(idParamSchema),
  markNotificationAsRead
);

// 删除通知
router.delete('/:id',
  authenticateToken,
  validateParams(idParamSchema),
  deleteNotification
);

// 发送系统维护通知（管理员功能）
router.post('/system-maintenance',
  authenticateToken,
  requireAdmin,
  validate(Joi.object({
    message: Joi.string().required().messages({
      'string.empty': '维护消息不能为空',
      'any.required': '维护消息是必填项',
    }),
    maintenanceTime: Joi.string().required().messages({
      'string.empty': '维护时间不能为空',
      'any.required': '维护时间是必填项',
    }),
  })),
  asyncHandler(async (req: Request, res: Response) => {
    const { message, maintenanceTime } = req.body;

    try {
      const socketService = getSocketService();
      await socketService.notifySystemMaintenance(message, maintenanceTime);

      const response: ApiResponse = {
        success: true,
        message: '系统维护通知发送成功',
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        message: '发送系统维护通知失败',
        timestamp: new Date().toISOString(),
      };

      res.status(500).json(response);
    }
  })
);

export default router;
