import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { taskApi } from '@/api/tasks'
import type { Task, TaskForm, PaginatedResponse, PaginationQuery } from '@/types'

export const useTaskStore = defineStore('tasks', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const availableTasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // 计算属性
  const totalTasks = computed(() => pagination.value.total)
  const hasTasks = computed(() => tasks.value.length > 0)
  const hasAvailableTasks = computed(() => availableTasks.value.length > 0)

  // 获取任务列表
  const fetchTasks = async (query: PaginationQuery & {
    status?: string
    assignType?: string
    assigneeId?: string
    orderId?: string
    keyword?: string
  } = {}) => {
    try {
      loading.value = true
      const response = await taskApi.getTasks(query)
      
      if (response.success && response.data) {
        tasks.value = response.data.items
        pagination.value = response.data.pagination
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取任务列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取可接单任务
  const fetchAvailableTasks = async (query: PaginationQuery = {}) => {
    try {
      loading.value = true
      const response = await taskApi.getAvailableTasks(query)
      
      if (response.success && response.data) {
        availableTasks.value = response.data.items
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取可接单任务失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取我的任务
  const fetchMyTasks = async (query: PaginationQuery & {
    status?: string
    keyword?: string
  } = {}) => {
    try {
      loading.value = true
      const response = await taskApi.getMyTasks(query)
      
      if (response.success && response.data) {
        tasks.value = response.data.items
        pagination.value = response.data.pagination
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取我的任务失败')
    } finally {
      loading.value = false
    }
  }

  // 获取任务详情
  const fetchTaskById = async (id: string) => {
    try {
      loading.value = true
      const response = await taskApi.getTaskById(id)
      
      if (response.success && response.data) {
        currentTask.value = response.data
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取任务详情失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建任务
  const createTask = async (taskForm: TaskForm) => {
    try {
      loading.value = true
      const response = await taskApi.createTask(taskForm)
      
      if (response.success && response.data) {
        ElMessage.success('任务创建成功')
        // 将新任务添加到列表开头
        tasks.value.unshift(response.data)
        pagination.value.total += 1
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '创建任务失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新任务
  const updateTask = async (id: string, updateData: Partial<TaskForm>) => {
    try {
      loading.value = true
      const response = await taskApi.updateTask(id, updateData)
      
      if (response.success && response.data) {
        ElMessage.success('任务更新成功')
        
        // 更新列表中的任务
        const index = tasks.value.findIndex(task => task.id === id)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        
        // 更新当前任务
        if (currentTask.value?.id === id) {
          currentTask.value = response.data
        }
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '更新任务失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 接单
  const acceptTask = async (id: string) => {
    try {
      loading.value = true
      const response = await taskApi.acceptTask(id)
      
      if (response.success && response.data) {
        ElMessage.success('接单成功')
        
        // 从可接单列表中移除
        const availableIndex = availableTasks.value.findIndex(task => task.id === id)
        if (availableIndex !== -1) {
          availableTasks.value.splice(availableIndex, 1)
        }
        
        // 添加到我的任务列表
        tasks.value.unshift(response.data)
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '接单失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新任务进度
  const updateTaskProgress = async (id: string, progressData: {
    progress: number
    currentRank?: string
    description?: string
    screenshots?: string[]
  }) => {
    try {
      loading.value = true
      const response = await taskApi.updateTaskProgress(id, progressData)
      
      if (response.success && response.data) {
        ElMessage.success('进度更新成功')
        
        // 刷新任务详情
        if (currentTask.value?.id === id) {
          await fetchTaskById(id)
        }
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '进度更新失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 审核任务
  const reviewTask = async (id: string, reviewData: {
    approved: boolean
    feedback?: string
  }) => {
    try {
      loading.value = true
      const response = await taskApi.reviewTask(id, reviewData)
      
      if (response.success && response.data) {
        ElMessage.success('任务审核完成')
        
        // 更新列表中的任务
        const index = tasks.value.findIndex(task => task.id === id)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        
        // 更新当前任务
        if (currentTask.value?.id === id) {
          currentTask.value = response.data
        }
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '任务审核失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 取消任务
  const cancelTask = async (id: string) => {
    try {
      loading.value = true
      const response = await taskApi.cancelTask(id)
      
      if (response.success && response.data) {
        ElMessage.success('任务取消成功')
        
        // 更新列表中的任务
        const index = tasks.value.findIndex(task => task.id === id)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        
        // 更新当前任务
        if (currentTask.value?.id === id) {
          currentTask.value = response.data
        }
        
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '任务取消失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取任务统计
  const fetchTaskStats = async () => {
    try {
      const response = await taskApi.getTaskStats()
      if (response.success && response.data) {
        return response.data
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取任务统计失败')
      return null
    }
  }

  // 清空任务列表
  const clearTasks = () => {
    tasks.value = []
    availableTasks.value = []
    currentTask.value = null
    pagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    }
  }

  return {
    // 状态
    tasks,
    availableTasks,
    currentTask,
    loading,
    pagination,
    
    // 计算属性
    totalTasks,
    hasTasks,
    hasAvailableTasks,
    
    // 方法
    fetchTasks,
    fetchAvailableTasks,
    fetchMyTasks,
    fetchTaskById,
    createTask,
    updateTask,
    acceptTask,
    updateTaskProgress,
    reviewTask,
    cancelTask,
    fetchTaskStats,
    clearTasks
  }
})
