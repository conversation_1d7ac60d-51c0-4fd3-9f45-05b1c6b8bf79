/**
 * 数据库迁移脚本：从单一王者荣耀系统迁移到多游戏支持系统
 * 
 * 此脚本将：
 * 1. 备份现有数据
 * 2. 创建新的游戏相关表
 * 3. 迁移现有订单数据
 * 4. 验证迁移结果
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

// 王者荣耀段位映射
const wzryRankMapping = {
  '倔强青铜': { level: 1, difficultyMultiplier: 1.0 },
  '秩序白银': { level: 2, difficultyMultiplier: 1.2 },
  '荣耀黄金': { level: 3, difficultyMultiplier: 1.4 },
  '尊贵铂金': { level: 4, difficultyMultiplier: 1.6 },
  '永恒钻石': { level: 5, difficultyMultiplier: 1.8 },
  '至尊星耀': { level: 6, difficultyMultiplier: 2.0 },
  '最强王者': { level: 7, difficultyMultiplier: 2.5 },
  '荣耀王者': { level: 8, difficultyMultiplier: 3.0 }
};

async function backupData() {
  console.log('📦 备份现有数据...');
  
  try {
    // 备份订单数据
    const orders = await prisma.order.findMany();
    const backupDir = path.join(__dirname, '../backups');
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `orders-backup-${timestamp}.json`);
    
    fs.writeFileSync(backupFile, JSON.stringify(orders, null, 2));
    console.log(`✅ 数据备份完成: ${backupFile}`);
    
    return backupFile;
  } catch (error) {
    console.error('❌ 数据备份失败:', error);
    throw error;
  }
}

async function createGameData() {
  console.log('🎮 创建游戏配置数据...');
  
  try {
    // 创建王者荣耀游戏
    const wzryGame = await prisma.game.upsert({
      where: { name: 'wzry' },
      update: {},
      create: {
        id: 'game_wzry_001',
        name: 'wzry',
        displayName: '王者荣耀',
        description: '腾讯王者荣耀MOBA游戏',
        isActive: true,
        sortOrder: 1,
        config: {
          maxRankJump: 2,
          seasonDuration: 90,
          features: ['排位赛', '巅峰赛', '娱乐模式']
        }
      }
    });
    
    console.log('✅ 王者荣耀游戏创建完成');
    
    // 创建王者荣耀段位
    const rankPromises = Object.entries(wzryRankMapping).map(([name, config]) => 
      prisma.gameRank.upsert({
        where: {
          gameId_name: {
            gameId: wzryGame.id,
            name: name
          }
        },
        update: {},
        create: {
          gameId: wzryGame.id,
          name: name,
          displayName: name,
          level: config.level,
          difficultyMultiplier: config.difficultyMultiplier,
          isActive: true
        }
      })
    );
    
    const ranks = await Promise.all(rankPromises);
    console.log(`✅ 创建了 ${ranks.length} 个段位`);
    
    // 创建默认价格规则
    await prisma.gamePriceRule.upsert({
      where: { id: 'price_wzry_001' },
      update: {},
      create: {
        id: 'price_wzry_001',
        gameId: wzryGame.id,
        name: '王者荣耀基础价格',
        description: '基于段位等级的价格计算',
        ruleType: 'RANK_BASED',
        basePrice: 50.0,
        pricePerLevel: 30.0,
        multiplier: 1.0,
        isActive: true
      }
    });
    
    console.log('✅ 价格规则创建完成');
    
    return { game: wzryGame, ranks };
  } catch (error) {
    console.error('❌ 游戏数据创建失败:', error);
    throw error;
  }
}

async function migrateOrderData() {
  console.log('📋 迁移订单数据...');
  
  try {
    // 获取王者荣耀游戏和段位信息
    const wzryGame = await prisma.game.findUnique({
      where: { name: 'wzry' },
      include: { ranks: true }
    });
    
    if (!wzryGame) {
      throw new Error('王者荣耀游戏数据不存在');
    }
    
    // 创建段位名称到ID的映射
    const rankNameToId = wzryGame.ranks.reduce((map, rank) => {
      map[rank.name] = rank.id;
      return map;
    }, {} as Record<string, string>);
    
    // 获取所有需要迁移的订单（假设这些是旧格式的订单）
    const ordersToMigrate = await prisma.$queryRaw`
      SELECT * FROM orders 
      WHERE gameId IS NULL 
      OR currentRankId IS NULL 
      OR targetRankId IS NULL
    ` as any[];
    
    console.log(`📊 找到 ${ordersToMigrate.length} 个需要迁移的订单`);
    
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const order of ordersToMigrate) {
      try {
        // 映射段位名称到ID
        const currentRankId = rankNameToId[order.currentRank] || rankNameToId['倔强青铜'];
        const targetRankId = rankNameToId[order.targetRank] || rankNameToId['秩序白银'];
        
        if (!currentRankId || !targetRankId) {
          console.warn(`⚠️ 订单 ${order.orderNo} 的段位映射失败: ${order.currentRank} -> ${order.targetRank}`);
          errorCount++;
          continue;
        }
        
        // 更新订单
        await prisma.order.update({
          where: { id: order.id },
          data: {
            gameId: wzryGame.id,
            currentRankId: currentRankId,
            targetRankId: targetRankId
          }
        });
        
        migratedCount++;
        
        if (migratedCount % 10 === 0) {
          console.log(`📈 已迁移 ${migratedCount} 个订单...`);
        }
        
      } catch (error) {
        console.error(`❌ 迁移订单 ${order.orderNo} 失败:`, error);
        errorCount++;
      }
    }
    
    console.log(`✅ 订单迁移完成: 成功 ${migratedCount} 个，失败 ${errorCount} 个`);
    
    return { migratedCount, errorCount };
  } catch (error) {
    console.error('❌ 订单数据迁移失败:', error);
    throw error;
  }
}

async function validateMigration() {
  console.log('🔍 验证迁移结果...');
  
  try {
    // 检查游戏数据
    const gameCount = await prisma.game.count();
    const rankCount = await prisma.gameRank.count();
    const priceRuleCount = await prisma.gamePriceRule.count();
    
    console.log(`📊 游戏数量: ${gameCount}`);
    console.log(`📊 段位数量: ${rankCount}`);
    console.log(`📊 价格规则数量: ${priceRuleCount}`);
    
    // 检查订单数据完整性
    const totalOrders = await prisma.order.count();
    const migratedOrders = await prisma.order.count({
      where: {
        AND: [
          { gameId: { not: null } },
          { currentRankId: { not: null } },
          { targetRankId: { not: null } }
        ]
      }
    });
    
    console.log(`📊 总订单数: ${totalOrders}`);
    console.log(`📊 已迁移订单数: ${migratedOrders}`);
    
    const migrationRate = totalOrders > 0 ? (migratedOrders / totalOrders * 100).toFixed(2) : '0';
    console.log(`📊 迁移完成率: ${migrationRate}%`);
    
    // 检查数据一致性
    const inconsistentOrders = await prisma.order.findMany({
      where: {
        OR: [
          { gameId: null },
          { currentRankId: null },
          { targetRankId: null }
        ]
      },
      select: { id: true, orderNo: true }
    });
    
    if (inconsistentOrders.length > 0) {
      console.warn(`⚠️ 发现 ${inconsistentOrders.length} 个数据不一致的订单:`);
      inconsistentOrders.forEach(order => {
        console.warn(`   - ${order.orderNo} (${order.id})`);
      });
    } else {
      console.log('✅ 所有订单数据一致性检查通过');
    }
    
    return {
      gameCount,
      rankCount,
      priceRuleCount,
      totalOrders,
      migratedOrders,
      migrationRate: parseFloat(migrationRate),
      inconsistentOrders: inconsistentOrders.length
    };
    
  } catch (error) {
    console.error('❌ 迁移验证失败:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 开始多游戏支持数据库迁移...\n');
  
  try {
    // 1. 备份数据
    const backupFile = await backupData();
    console.log('');
    
    // 2. 创建游戏数据
    await createGameData();
    console.log('');
    
    // 3. 迁移订单数据
    const migrationResult = await migrateOrderData();
    console.log('');
    
    // 4. 验证迁移结果
    const validationResult = await validateMigration();
    console.log('');
    
    // 5. 输出迁移报告
    console.log('📋 迁移完成报告:');
    console.log('================');
    console.log(`备份文件: ${backupFile}`);
    console.log(`游戏数量: ${validationResult.gameCount}`);
    console.log(`段位数量: ${validationResult.rankCount}`);
    console.log(`价格规则数量: ${validationResult.priceRuleCount}`);
    console.log(`订单迁移: ${migrationResult.migratedCount} 成功, ${migrationResult.errorCount} 失败`);
    console.log(`迁移完成率: ${validationResult.migrationRate}%`);
    console.log(`数据一致性: ${validationResult.inconsistentOrders === 0 ? '✅ 通过' : `❌ ${validationResult.inconsistentOrders} 个问题`}`);
    
    if (validationResult.migrationRate >= 95 && validationResult.inconsistentOrders === 0) {
      console.log('\n🎉 迁移成功完成！系统现在支持多游戏功能。');
    } else {
      console.log('\n⚠️ 迁移完成但存在问题，请检查上述报告。');
    }
    
  } catch (error) {
    console.error('\n❌ 迁移失败:', error);
    console.log('\n💡 建议：');
    console.log('1. 检查数据库连接');
    console.log('2. 确保Prisma schema已更新');
    console.log('3. 检查备份文件是否存在');
    console.log('4. 如需回滚，请使用备份文件恢复数据');
    process.exit(1);
  }
}

// 运行迁移
if (require.main === module) {
  main()
    .catch(console.error)
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { main as migrateToMultiGame };
