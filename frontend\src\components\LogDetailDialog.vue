<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录日志详情"
    width="600px"
    :before-close="handleClose"
  >
    <div class="log-detail-dialog" v-if="log">
      <div class="detail-sections">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">用户名：</span>
              <span class="value">{{ log.username }}</span>
            </div>
            <div class="detail-item">
              <span class="label">登录结果：</span>
              <el-tag :type="getResultTagType(log.loginResult)" size="small">
                {{ getResultText(log.loginResult) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">登录时间：</span>
              <span class="value">{{ formatDateTime(log.loginTime) }}</span>
            </div>
            <div class="detail-item" v-if="log.logoutTime">
              <span class="label">登出时间：</span>
              <span class="value">{{ formatDateTime(log.logoutTime) }}</span>
            </div>
            <div class="detail-item" v-if="log.sessionDuration">
              <span class="label">会话时长：</span>
              <span class="value">{{ formatDuration(log.sessionDuration) }}</span>
            </div>
            <div class="detail-item" v-if="log.failureReason">
              <span class="label">失败原因：</span>
              <span class="value failure-reason">{{ log.failureReason }}</span>
            </div>
          </div>
        </div>

        <!-- 网络信息 -->
        <div class="detail-section">
          <h3 class="section-title">网络信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">IP地址：</span>
              <span class="value ip-address">{{ log.ipAddress }}</span>
            </div>
            <div class="detail-item">
              <span class="label">国家：</span>
              <span class="value">{{ log.locationCountry || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">省份：</span>
              <span class="value">{{ log.locationProvince || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">城市：</span>
              <span class="value">{{ log.locationCity || '未知' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">ISP运营商：</span>
              <span class="value">{{ log.locationIsp || '未知' }}</span>
            </div>
            <div class="detail-item full-width">
              <span class="label">完整位置：</span>
              <span class="value">{{ log.locationFull || '未知' }}</span>
            </div>
          </div>
        </div>

        <!-- 设备信息 -->
        <div class="detail-section">
          <h3 class="section-title">设备信息</h3>
          <div class="detail-grid">
            <div class="detail-item full-width" v-if="log.userAgent">
              <span class="label">用户代理：</span>
              <span class="value user-agent">{{ log.userAgent }}</span>
            </div>
            <div class="detail-item" v-if="deviceInfo">
              <span class="label">平台：</span>
              <span class="value">{{ deviceInfo.platform || '未知' }}</span>
            </div>
            <div class="detail-item" v-if="deviceInfo">
              <span class="label">版本：</span>
              <span class="value">{{ deviceInfo.version || '未知' }}</span>
            </div>
            <div class="detail-item" v-if="deviceInfo">
              <span class="label">时间戳：</span>
              <span class="value">{{ deviceInfo.timestamp || '未知' }}</span>
            </div>
          </div>
        </div>

        <!-- 安全分析 -->
        <div class="detail-section">
          <h3 class="section-title">安全分析</h3>
          <div class="security-analysis">
            <div class="analysis-item">
              <div class="analysis-label">风险等级：</div>
              <el-tag :type="getRiskLevel(log).type" size="small">
                {{ getRiskLevel(log).text }}
              </el-tag>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">登录频率：</div>
              <span class="analysis-value">正常</span>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">地理位置：</div>
              <span class="analysis-value">
                {{ isLocationNormal(log) ? '正常' : '异常' }}
              </span>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">设备指纹：</div>
              <span class="analysis-value">
                {{ isDeviceNormal(log) ? '已知设备' : '新设备' }}
              </span>
            </div>
          </div>
        </div>

        <!-- 原始数据 -->
        <div class="detail-section">
          <h3 class="section-title">原始数据</h3>
          <div class="raw-data">
            <el-input
              type="textarea"
              :rows="8"
              :value="formatRawData(log)"
              readonly
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="copyToClipboard">
          复制详情
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/utils/date'
import type { LoginLog, LoginResult } from '@/types'

interface Props {
  modelValue: boolean
  log: LoginLog | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 解析设备信息
const deviceInfo = computed(() => {
  if (!props.log?.deviceInfo) return null
  try {
    return typeof props.log.deviceInfo === 'string' 
      ? JSON.parse(props.log.deviceInfo) 
      : props.log.deviceInfo
  } catch {
    return null
  }
})

// 获取结果标签类型
const getResultTagType = (result: LoginResult) => {
  switch (result) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'BLOCKED': return 'warning'
    default: return 'info'
  }
}

// 获取结果文本
const getResultText = (result: LoginResult) => {
  switch (result) {
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'BLOCKED': return '被阻止'
    default: return '未知'
  }
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取风险等级
const getRiskLevel = (log: LoginLog) => {
  if (log.loginResult === 'BLOCKED') {
    return { type: 'danger', text: '高风险' }
  } else if (log.loginResult === 'FAILED') {
    return { type: 'warning', text: '中风险' }
  } else if (log.ipAddress.startsWith('192.168.') || log.ipAddress.startsWith('10.') || log.ipAddress.startsWith('172.')) {
    return { type: 'info', text: '内网访问' }
  } else {
    return { type: 'success', text: '低风险' }
  }
}

// 判断地理位置是否正常
const isLocationNormal = (log: LoginLog): boolean => {
  // 简单的地理位置异常检测逻辑
  return log.locationCountry === '中国' || log.locationCountry === '未知'
}

// 判断设备是否正常
const isDeviceNormal = (log: LoginLog): boolean => {
  // 简单的设备检测逻辑
  return log.userAgent?.includes('Chrome') || log.userAgent?.includes('Firefox') || false
}

// 格式化原始数据
const formatRawData = (log: LoginLog): string => {
  return JSON.stringify(log, null, 2)
}

// 复制到剪贴板
const copyToClipboard = async () => {
  if (!props.log) return
  
  try {
    const text = formatRawData(props.log)
    await navigator.clipboard.writeText(text)
    ElMessage.success('详情已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.log-detail-dialog {
  .detail-sections {
    .detail-section {
      margin-bottom: 24px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;
      }
      
      .detail-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        
        .detail-item {
          display: flex;
          align-items: flex-start;
          
          &.full-width {
            grid-column: 1 / -1;
          }
          
          .label {
            min-width: 80px;
            color: #606266;
            font-size: 14px;
            margin-right: 8px;
          }
          
          .value {
            color: #303133;
            font-size: 14px;
            word-break: break-all;
            
            &.failure-reason {
              color: #f56c6c;
            }
            
            &.ip-address {
              font-family: 'Courier New', monospace;
              background-color: #f5f7fa;
              padding: 2px 6px;
              border-radius: 4px;
            }
            
            &.user-agent {
              font-size: 12px;
              color: #606266;
              line-height: 1.4;
            }
          }
        }
      }
      
      .security-analysis {
        .analysis-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .analysis-label {
            min-width: 80px;
            color: #606266;
            font-size: 14px;
            margin-right: 12px;
          }
          
          .analysis-value {
            color: #303133;
            font-size: 14px;
          }
        }
      }
      
      .raw-data {
        .el-textarea {
          :deep(.el-textarea__inner) {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
