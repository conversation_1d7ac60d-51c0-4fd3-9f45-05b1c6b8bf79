<template>
  <div class="quick-export-buttons">
    <!-- 月度结算导出 -->
    <el-button
      v-if="canExportSettlement"
      type="success"
      :loading="loadingStates.settlement"
      @click="exportCurrentMonthSettlement"
    >
      <el-icon><Download /></el-icon>
      导出本月结算
    </el-button>

    <!-- 订单汇总导出 -->
    <el-button
      v-if="canExportOrders"
      type="primary"
      :loading="loadingStates.orders"
      @click="exportCurrentMonthOrders"
    >
      <el-icon><Document /></el-icon>
      导出本月订单
    </el-button>

    <!-- 个人收益导出（员工专用） -->
    <el-button
      v-if="canExportPersonalEarnings"
      type="warning"
      :loading="loadingStates.personal"
      @click="exportPersonalEarnings"
    >
      <el-icon><Money /></el-icon>
      导出我的收益
    </el-button>

    <!-- 自定义导出 -->
    <el-button
      type="info"
      @click="showCustomExport"
    >
      <el-icon><Setting /></el-icon>
      自定义导出
    </el-button>

    <!-- 自定义导出对话框 -->
    <ExcelExportDialog v-model="customExportVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Download, Document, Money, Setting } from '@element-plus/icons-vue';
import { excelApi, downloadFile } from '@/api/excel';
import { useAuthStore } from '@/stores/auth';
import ExcelExportDialog from './ExcelExportDialog.vue';

// 响应式数据
const authStore = useAuthStore();
const customExportVisible = ref(false);

const loadingStates = ref({
  settlement: false,
  orders: false,
  personal: false
});

// 计算属性 - 权限控制
const canExportSettlement = computed(() => {
  return ['BOSS', 'ADMIN'].includes(authStore.user?.role || '');
});

const canExportOrders = computed(() => {
  return ['BOSS', 'ADMIN'].includes(authStore.user?.role || '');
});

const canExportPersonalEarnings = computed(() => {
  return authStore.user?.role === 'EMPLOYEE';
});

// 方法
const exportCurrentMonthSettlement = async () => {
  try {
    loadingStates.value.settlement = true;
    
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    const blob = await excelApi.exportMonthlySettlement(year, month);
    const filename = `月度结算报表_${year}年${month}月_${formatDateTime(now)}.xlsx`;
    
    downloadFile(blob, filename);
    ElMessage.success('本月结算报表导出成功');
    
  } catch (error: any) {
    console.error('导出本月结算失败:', error);
    ElMessage.error(error.message || '导出失败');
  } finally {
    loadingStates.value.settlement = false;
  }
};

const exportCurrentMonthOrders = async () => {
  try {
    loadingStates.value.orders = true;
    
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const startDate = formatDate(startOfMonth);
    const endDate = formatDate(endOfMonth);
    
    const blob = await excelApi.exportOrderSummary(startDate, endDate);
    const filename = `订单汇总_${now.getFullYear()}年${now.getMonth() + 1}月_${formatDateTime(now)}.xlsx`;
    
    downloadFile(blob, filename);
    ElMessage.success('本月订单汇总导出成功');
    
  } catch (error: any) {
    console.error('导出本月订单失败:', error);
    ElMessage.error(error.message || '导出失败');
  } finally {
    loadingStates.value.orders = false;
  }
};

const exportPersonalEarnings = async () => {
  try {
    loadingStates.value.personal = true;
    
    if (!authStore.user?.id) {
      throw new Error('用户信息不完整');
    }
    
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const startDate = formatDate(startOfMonth);
    const endDate = formatDate(endOfMonth);
    
    const blob = await excelApi.exportEmployeeEarnings(authStore.user.id, startDate, endDate);
    const filename = `我的收益明细_${now.getFullYear()}年${now.getMonth() + 1}月_${formatDateTime(now)}.xlsx`;
    
    downloadFile(blob, filename);
    ElMessage.success('个人收益明细导出成功');
    
  } catch (error: any) {
    console.error('导出个人收益失败:', error);
    ElMessage.error(error.message || '导出失败');
  } finally {
    loadingStates.value.personal = false;
  }
};

const showCustomExport = () => {
  customExportVisible.value = true;
};

// 工具函数
const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

const formatDateTime = (date: Date): string => {
  return date.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '_');
};
</script>

<style lang="scss" scoped>
.quick-export-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;

  .el-button {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>
