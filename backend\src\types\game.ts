import { PriceRuleType, SkillLevel, OrderPriority } from '@prisma/client';

// 游戏配置接口
export interface GameConfig {
  maxRankJump?: number;      // 最大段位跨越
  seasonDuration?: number;   // 赛季持续天数
  features?: string[];       // 游戏特色功能
  [key: string]: any;        // 其他自定义配置
}

// 创建游戏请求
export interface CreateGameRequest {
  name: string;
  displayName: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
  sortOrder?: number;
  config?: GameConfig;
}

// 更新游戏请求
export interface UpdateGameRequest {
  name?: string;
  displayName?: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
  sortOrder?: number;
  config?: GameConfig;
}

// 游戏段位创建请求
export interface CreateGameRankRequest {
  gameId: string;
  name: string;
  displayName: string;
  level: number;
  difficultyMultiplier?: number;
  icon?: string;
  description?: string;
  isActive?: boolean;
}

// 游戏段位更新请求
export interface UpdateGameRankRequest {
  name?: string;
  displayName?: string;
  level?: number;
  difficultyMultiplier?: number;
  icon?: string;
  description?: string;
  isActive?: boolean;
}

// 游戏价格规则创建请求
export interface CreateGamePriceRuleRequest {
  gameId: string;
  name: string;
  description?: string;
  ruleType: PriceRuleType;
  basePrice: number;
  pricePerLevel?: number;
  multiplier?: number;
  minLevel?: number;
  maxLevel?: number;
  priority?: OrderPriority;
  isActive?: boolean;
}

// 游戏价格规则更新请求
export interface UpdateGamePriceRuleRequest {
  name?: string;
  description?: string;
  ruleType?: PriceRuleType;
  basePrice?: number;
  pricePerLevel?: number;
  multiplier?: number;
  minLevel?: number;
  maxLevel?: number;
  priority?: OrderPriority;
  isActive?: boolean;
}

// 员工游戏技能创建请求
export interface CreateEmployeeGameSkillRequest {
  userId: string;
  gameId: string;
  skillLevel: SkillLevel;
  maxRankLevel?: number;
  isActive?: boolean;
}

// 员工游戏技能更新请求
export interface UpdateEmployeeGameSkillRequest {
  skillLevel?: SkillLevel;
  maxRankLevel?: number;
  isActive?: boolean;
  certifiedBy?: string;
}

// 游戏统计信息
export interface GameStatistics {
  gameId: string;
  gameName: string;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  completionRate: number;
  popularRanks: {
    rankName: string;
    orderCount: number;
  }[];
  monthlyTrend: {
    month: string;
    orderCount: number;
    revenue: number;
  }[];
}

// 价格计算结果
export interface PriceCalculationResult {
  basePrice: number;
  levelDifference: number;
  difficultyMultiplier: number;
  priorityMultiplier: number;
  finalPrice: number;
  appliedRules: string[];
}

// 游戏热度分析
export interface GameHotness {
  gameId: string;
  gameName: string;
  orderCount: number;
  revenue: number;
  averageCompletionTime: number;
  employeeCount: number;
  hotnesScore: number; // 综合热度评分
  trend: 'rising' | 'stable' | 'declining';
}

// 员工技能匹配结果
export interface EmployeeSkillMatch {
  userId: string;
  username: string;
  nickname?: string;
  skillLevel: SkillLevel;
  maxRankLevel?: number;
  matchScore: number; // 匹配度评分
  completedTasks: number;
  averageRating: number;
}

// 游戏查询参数
export interface GameQuery {
  isActive?: boolean;
  sortBy?: 'name' | 'displayName' | 'sortOrder' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 游戏段位查询参数
export interface GameRankQuery {
  gameId?: string;
  isActive?: boolean;
  minLevel?: number;
  maxLevel?: number;
  sortBy?: 'level' | 'name' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 价格规则查询参数
export interface GamePriceRuleQuery {
  gameId?: string;
  ruleType?: PriceRuleType;
  isActive?: boolean;
  priority?: OrderPriority;
  sortBy?: 'name' | 'basePrice' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// 员工技能查询参数
export interface EmployeeGameSkillQuery {
  userId?: string;
  gameId?: string;
  skillLevel?: SkillLevel;
  isActive?: boolean;
  certified?: boolean;
  sortBy?: 'skillLevel' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}
